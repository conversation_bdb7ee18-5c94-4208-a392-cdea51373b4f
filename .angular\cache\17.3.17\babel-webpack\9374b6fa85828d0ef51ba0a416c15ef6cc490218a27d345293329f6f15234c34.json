{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMaximumWordsInfo = exports.validateMaximumWordsSync = exports.validateMaximumWords = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableTextFieldComponent = component => {\n  var _a;\n  return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('maxWords'));\n};\nconst getValidationSetting = component => {\n  var _a;\n  let maxWords = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.maxWords;\n  if (typeof maxWords === 'string') {\n    maxWords = parseInt(maxWords, 10);\n  }\n  return maxWords;\n};\nconst shouldValidate = context => {\n  const {\n    component\n  } = context;\n  if (!isValidatableTextFieldComponent(component)) {\n    return false;\n  }\n  const setting = getValidationSetting(component);\n  if (setting === undefined) {\n    return false;\n  }\n  if (!setting || Number.isNaN(setting)) {\n    return false;\n  }\n  return true;\n};\nconst validateMaximumWords = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMaximumWordsSync)(context);\n});\nexports.validateMaximumWords = validateMaximumWords;\nconst validateMaximumWordsSync = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!shouldValidate(context)) {\n    return null;\n  }\n  const maxWords = getValidationSetting(component);\n  if (maxWords && typeof value === 'string') {\n    if (value.trim().split(/\\s+/).length > maxWords) {\n      const error = new error_1.FieldError('maxWords', Object.assign(Object.assign({}, context), {\n        length: String(maxWords),\n        setting: String(maxWords)\n      }));\n      return error;\n    }\n  }\n  return null;\n};\nexports.validateMaximumWordsSync = validateMaximumWordsSync;\nexports.validateMaximumWordsInfo = {\n  name: 'validateMaximumWords',\n  process: exports.validateMaximumWords,\n  processSync: exports.validateMaximumWordsSync,\n  shouldProcess: shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateMaximumWordsInfo", "validateMaximumWordsSync", "validateMax<PERSON>um<PERSON>ords", "error_1", "require", "isValidatableTextFieldComponent", "component", "_a", "validate", "hasOwnProperty", "getValidationSetting", "max<PERSON><PERSON>s", "parseInt", "shouldValidate", "context", "setting", "undefined", "Number", "isNaN", "trim", "split", "length", "error", "FieldError", "assign", "String", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMaximumWords.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMaximumWordsInfo = exports.validateMaximumWordsSync = exports.validateMaximumWords = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableTextFieldComponent = (component) => {\n    var _a;\n    return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('maxWords'));\n};\nconst getValidationSetting = (component) => {\n    var _a;\n    let maxWords = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.maxWords;\n    if (typeof maxWords === 'string') {\n        maxWords = parseInt(maxWords, 10);\n    }\n    return maxWords;\n};\nconst shouldValidate = (context) => {\n    const { component } = context;\n    if (!isValidatableTextFieldComponent(component)) {\n        return false;\n    }\n    const setting = getValidationSetting(component);\n    if (setting === undefined) {\n        return false;\n    }\n    if (!setting || Number.isNaN(setting)) {\n        return false;\n    }\n    return true;\n};\nconst validateMaximumWords = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMaximumWordsSync)(context);\n});\nexports.validateMaximumWords = validateMaximumWords;\nconst validateMaximumWordsSync = (context) => {\n    const { component, value } = context;\n    if (!shouldValidate(context)) {\n        return null;\n    }\n    const maxWords = getValidationSetting(component);\n    if (maxWords && typeof value === 'string') {\n        if (value.trim().split(/\\s+/).length > maxWords) {\n            const error = new error_1.FieldError('maxWords', Object.assign(Object.assign({}, context), { length: String(maxWords), setting: String(maxWords) }));\n            return error;\n        }\n    }\n    return null;\n};\nexports.validateMaximumWordsSync = validateMaximumWordsSync;\nexports.validateMaximumWordsInfo = {\n    name: 'validateMaximumWords',\n    process: exports.validateMaximumWords,\n    processSync: exports.validateMaximumWordsSync,\n    shouldProcess: shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,wBAAwB,GAAGD,OAAO,CAACE,wBAAwB,GAAGF,OAAO,CAACG,oBAAoB,GAAG,KAAK,CAAC;AAC3G,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,+BAA+B,GAAIC,SAAS,IAAK;EACnD,IAAIC,EAAE;EACN,OAAOD,SAAS,KAAK,CAACC,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,cAAc,CAAC,UAAU,CAAC,CAAC;AACtH,CAAC;AACD,MAAMC,oBAAoB,GAAIJ,SAAS,IAAK;EACxC,IAAIC,EAAE;EACN,IAAII,QAAQ,GAAG,CAACJ,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ;EACzF,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC9BA,QAAQ,GAAGC,QAAQ,CAACD,QAAQ,EAAE,EAAE,CAAC;EACrC;EACA,OAAOA,QAAQ;AACnB,CAAC;AACD,MAAME,cAAc,GAAIC,OAAO,IAAK;EAChC,MAAM;IAAER;EAAU,CAAC,GAAGQ,OAAO;EAC7B,IAAI,CAACT,+BAA+B,CAACC,SAAS,CAAC,EAAE;IAC7C,OAAO,KAAK;EAChB;EACA,MAAMS,OAAO,GAAGL,oBAAoB,CAACJ,SAAS,CAAC;EAC/C,IAAIS,OAAO,KAAKC,SAAS,EAAE;IACvB,OAAO,KAAK;EAChB;EACA,IAAI,CAACD,OAAO,IAAIE,MAAM,CAACC,KAAK,CAACH,OAAO,CAAC,EAAE;IACnC,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAMb,oBAAoB,GAAIY,OAAO,IAAKpC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACrF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,wBAAwB,EAAEa,OAAO,CAAC;AACzD,CAAC,CAAC;AACFf,OAAO,CAACG,oBAAoB,GAAGA,oBAAoB;AACnD,MAAMD,wBAAwB,GAAIa,OAAO,IAAK;EAC1C,MAAM;IAAER,SAAS;IAAEtB;EAAM,CAAC,GAAG8B,OAAO;EACpC,IAAI,CAACD,cAAc,CAACC,OAAO,CAAC,EAAE;IAC1B,OAAO,IAAI;EACf;EACA,MAAMH,QAAQ,GAAGD,oBAAoB,CAACJ,SAAS,CAAC;EAChD,IAAIK,QAAQ,IAAI,OAAO3B,KAAK,KAAK,QAAQ,EAAE;IACvC,IAAIA,KAAK,CAACmC,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,GAAGV,QAAQ,EAAE;MAC7C,MAAMW,KAAK,GAAG,IAAInB,OAAO,CAACoB,UAAU,CAAC,UAAU,EAAE1B,MAAM,CAAC2B,MAAM,CAAC3B,MAAM,CAAC2B,MAAM,CAAC,CAAC,CAAC,EAAEV,OAAO,CAAC,EAAE;QAAEO,MAAM,EAAEI,MAAM,CAACd,QAAQ,CAAC;QAAEI,OAAO,EAAEU,MAAM,CAACd,QAAQ;MAAE,CAAC,CAAC,CAAC;MACpJ,OAAOW,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AACDvB,OAAO,CAACE,wBAAwB,GAAGA,wBAAwB;AAC3DF,OAAO,CAACC,wBAAwB,GAAG;EAC/B0B,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,EAAE5B,OAAO,CAACG,oBAAoB;EACrC0B,WAAW,EAAE7B,OAAO,CAACE,wBAAwB;EAC7C4B,aAAa,EAAEhB;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}