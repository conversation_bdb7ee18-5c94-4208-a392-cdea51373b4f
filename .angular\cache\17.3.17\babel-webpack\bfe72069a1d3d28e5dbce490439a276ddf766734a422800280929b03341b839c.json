{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.I18n = exports.i18nConfig = exports.coreEnTranslation = void 0;\nconst en_1 = __importDefault(require(\"./translations/en\"));\nconst fastCloneDeep_1 = require(\"../utils/fastCloneDeep\");\nconst lodash_1 = require(\"lodash\");\nconst Evaluator_1 = require(\"./Evaluator\");\nexports.coreEnTranslation = en_1.default;\nexports.i18nConfig = {\n  lng: 'en',\n  nsSeparator: '::',\n  keySeparator: '.|.',\n  pluralSeparator: '._.',\n  contextSeparator: '._.',\n  resources: {\n    en: {\n      translation: (0, fastCloneDeep_1.fastCloneDeep)(en_1.default)\n    }\n  }\n};\nconst i18Defaults = {};\nfor (const lang in exports.i18nConfig.resources) {\n  if (exports.i18nConfig.resources.hasOwnProperty(lang)) {\n    i18Defaults[lang] = exports.i18nConfig.resources[lang].translation;\n  }\n}\n/**\n * This file is used to mimic the i18n library interface.\n */\nclass I18n {\n  constructor(languages = {}) {\n    var _a;\n    this.languages = (0, fastCloneDeep_1.fastCloneDeep)(I18n.languages || {});\n    this.defaultKeys = ((_a = I18n.languages) === null || _a === void 0 ? void 0 : _a.en) || {};\n    this.language = 'en';\n    this.currentLanguage = i18Defaults.en;\n    this.setLanguages(languages, false);\n    this.changeLanguage(this.language);\n  }\n  static setDefaultTranslations(languages) {\n    if ((0, lodash_1.isEmpty)(languages)) {\n      return;\n    }\n    for (const lang in languages) {\n      if (lang !== 'language' && languages.hasOwnProperty(lang)) {\n        if (!this.languages[lang]) {\n          this.languages[lang] = {};\n        }\n        this.languages[lang] = Object.assign(Object.assign({}, languages[lang]), this.languages[lang]);\n      }\n    }\n  }\n  setLanguages(languages, noDefaultOverride) {\n    if (languages.resources) {\n      for (const lang in languages.resources) {\n        if (languages.resources.hasOwnProperty(lang)) {\n          languages[lang] = languages.resources[lang].translation;\n        }\n      }\n      delete languages.resources;\n    }\n    if (languages.lng) {\n      languages.language = languages.lng;\n      delete languages.lng;\n    }\n    // Do not use these configurations.\n    delete languages.nsSeparator;\n    delete languages.keySeparator;\n    delete languages.pluralSeparator;\n    delete languages.contextSeparator;\n    // Now establish the languages default.\n    if (languages.language) {\n      this.language = languages.language;\n    }\n    for (const lang in languages) {\n      if (lang !== 'language' && languages.hasOwnProperty(lang)) {\n        if (!this.languages[lang]) {\n          this.languages[lang] = {};\n        }\n        this.languages[lang] = noDefaultOverride ? Object.assign(Object.assign({}, languages[lang]), this.languages[lang]) : Object.assign(Object.assign({}, this.languages[lang]), languages[lang]);\n      }\n    }\n  }\n  static init(languages = {}) {\n    return new I18n(languages);\n  }\n  dir(lang = '') {\n    lang = lang || this.language;\n    const rtls = ['ar', 'he', 'fa', 'ps', 'ur'];\n    return rtls.includes(lang) ? 'rtl' : 'ltr';\n  }\n  static createInstance() {\n    return new I18n();\n  }\n  changeLanguage(language, ready) {\n    if (!this.languages[language]) {\n      language = 'en';\n    }\n    this.language = language;\n    this.currentLanguage = this.languages[language] ? this.languages[language] : {};\n    if (ready) {\n      ready();\n    }\n  }\n  addResourceBundle(language, type, strings) {\n    this.languages[language] = strings;\n  }\n  t(text, data, ...args) {\n    let currentTranslation = this.currentLanguage[text];\n    // provide compatibility with cases where the entire phrase is used as a key\n    // get the phrase that is possibly being used as a key\n    const defaultKey = this.defaultKeys[text];\n    if (defaultKey && this.currentLanguage[defaultKey]) {\n      // get translation using the phrase as a key\n      currentTranslation = this.currentLanguage[defaultKey];\n    }\n    if (currentTranslation) {\n      const customTranslationFieldName = data === null || data === void 0 ? void 0 : data.field;\n      if (customTranslationFieldName && this.currentLanguage[customTranslationFieldName]) {\n        data.field = this.currentLanguage[customTranslationFieldName];\n      }\n      return Evaluator_1.Evaluator.interpolateString(currentTranslation, data, ...args);\n    }\n    return Evaluator_1.Evaluator.interpolateString(text, data, ...args);\n  }\n}\nexports.I18n = I18n;\nI18n.languages = i18Defaults;", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "I18n", "i18nConfig", "coreEnTranslation", "en_1", "require", "fastCloneDeep_1", "lodash_1", "Evaluator_1", "default", "lng", "nsSeparator", "keySeparator", "pluralSeparator", "contextSeparator", "resources", "en", "translation", "fastCloneDeep", "i18Defaults", "lang", "hasOwnProperty", "constructor", "languages", "_a", "defaultKeys", "language", "currentLanguage", "setLanguages", "changeLanguage", "setDefaultTranslations", "isEmpty", "assign", "noDefaultOverride", "init", "dir", "rtls", "includes", "createInstance", "ready", "addResourceBundle", "type", "strings", "t", "text", "data", "args", "currentTranslation", "defaultKey", "customTranslationFieldName", "field", "Evaluator", "interpolateString"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/utils/i18n.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.I18n = exports.i18nConfig = exports.coreEnTranslation = void 0;\nconst en_1 = __importDefault(require(\"./translations/en\"));\nconst fastCloneDeep_1 = require(\"../utils/fastCloneDeep\");\nconst lodash_1 = require(\"lodash\");\nconst Evaluator_1 = require(\"./Evaluator\");\nexports.coreEnTranslation = en_1.default;\nexports.i18nConfig = {\n    lng: 'en',\n    nsSeparator: '::',\n    keySeparator: '.|.',\n    pluralSeparator: '._.',\n    contextSeparator: '._.',\n    resources: {\n        en: {\n            translation: (0, fastCloneDeep_1.fastCloneDeep)(en_1.default),\n        },\n    },\n};\nconst i18Defaults = {};\nfor (const lang in exports.i18nConfig.resources) {\n    if (exports.i18nConfig.resources.hasOwnProperty(lang)) {\n        i18Defaults[lang] = exports.i18nConfig.resources[lang].translation;\n    }\n}\n/**\n * This file is used to mimic the i18n library interface.\n */\nclass I18n {\n    constructor(languages = {}) {\n        var _a;\n        this.languages = (0, fastCloneDeep_1.fastCloneDeep)(I18n.languages || {});\n        this.defaultKeys = ((_a = I18n.languages) === null || _a === void 0 ? void 0 : _a.en) || {};\n        this.language = 'en';\n        this.currentLanguage = i18Defaults.en;\n        this.setLanguages(languages, false);\n        this.changeLanguage(this.language);\n    }\n    static setDefaultTranslations(languages) {\n        if ((0, lodash_1.isEmpty)(languages)) {\n            return;\n        }\n        for (const lang in languages) {\n            if (lang !== 'language' && languages.hasOwnProperty(lang)) {\n                if (!this.languages[lang]) {\n                    this.languages[lang] = {};\n                }\n                this.languages[lang] = Object.assign(Object.assign({}, languages[lang]), this.languages[lang]);\n            }\n        }\n    }\n    setLanguages(languages, noDefaultOverride) {\n        if (languages.resources) {\n            for (const lang in languages.resources) {\n                if (languages.resources.hasOwnProperty(lang)) {\n                    languages[lang] = languages.resources[lang].translation;\n                }\n            }\n            delete languages.resources;\n        }\n        if (languages.lng) {\n            languages.language = languages.lng;\n            delete languages.lng;\n        }\n        // Do not use these configurations.\n        delete languages.nsSeparator;\n        delete languages.keySeparator;\n        delete languages.pluralSeparator;\n        delete languages.contextSeparator;\n        // Now establish the languages default.\n        if (languages.language) {\n            this.language = languages.language;\n        }\n        for (const lang in languages) {\n            if (lang !== 'language' && languages.hasOwnProperty(lang)) {\n                if (!this.languages[lang]) {\n                    this.languages[lang] = {};\n                }\n                this.languages[lang] = noDefaultOverride\n                    ? Object.assign(Object.assign({}, languages[lang]), this.languages[lang]) : Object.assign(Object.assign({}, this.languages[lang]), languages[lang]);\n            }\n        }\n    }\n    static init(languages = {}) {\n        return new I18n(languages);\n    }\n    dir(lang = '') {\n        lang = lang || this.language;\n        const rtls = ['ar', 'he', 'fa', 'ps', 'ur'];\n        return rtls.includes(lang) ? 'rtl' : 'ltr';\n    }\n    static createInstance() {\n        return new I18n();\n    }\n    changeLanguage(language, ready) {\n        if (!this.languages[language]) {\n            language = 'en';\n        }\n        this.language = language;\n        this.currentLanguage = this.languages[language] ? this.languages[language] : {};\n        if (ready) {\n            ready();\n        }\n    }\n    addResourceBundle(language, type, strings) {\n        this.languages[language] = strings;\n    }\n    t(text, data, ...args) {\n        let currentTranslation = this.currentLanguage[text];\n        // provide compatibility with cases where the entire phrase is used as a key\n        // get the phrase that is possibly being used as a key\n        const defaultKey = this.defaultKeys[text];\n        if (defaultKey && this.currentLanguage[defaultKey]) {\n            // get translation using the phrase as a key\n            currentTranslation = this.currentLanguage[defaultKey];\n        }\n        if (currentTranslation) {\n            const customTranslationFieldName = data === null || data === void 0 ? void 0 : data.field;\n            if (customTranslationFieldName && this.currentLanguage[customTranslationFieldName]) {\n                data.field = this.currentLanguage[customTranslationFieldName];\n            }\n            return Evaluator_1.Evaluator.interpolateString(currentTranslation, data, ...args);\n        }\n        return Evaluator_1.Evaluator.interpolateString(text, data, ...args);\n    }\n}\nexports.I18n = I18n;\nI18n.languages = i18Defaults;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,IAAI,GAAGF,OAAO,CAACG,UAAU,GAAGH,OAAO,CAACI,iBAAiB,GAAG,KAAK,CAAC;AACtE,MAAMC,IAAI,GAAGV,eAAe,CAACW,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAC1D,MAAMC,eAAe,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AACzD,MAAME,QAAQ,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMG,WAAW,GAAGH,OAAO,CAAC,aAAa,CAAC;AAC1CN,OAAO,CAACI,iBAAiB,GAAGC,IAAI,CAACK,OAAO;AACxCV,OAAO,CAACG,UAAU,GAAG;EACjBQ,GAAG,EAAE,IAAI;EACTC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE,KAAK;EACnBC,eAAe,EAAE,KAAK;EACtBC,gBAAgB,EAAE,KAAK;EACvBC,SAAS,EAAE;IACPC,EAAE,EAAE;MACAC,WAAW,EAAE,CAAC,CAAC,EAAEX,eAAe,CAACY,aAAa,EAAEd,IAAI,CAACK,OAAO;IAChE;EACJ;AACJ,CAAC;AACD,MAAMU,WAAW,GAAG,CAAC,CAAC;AACtB,KAAK,MAAMC,IAAI,IAAIrB,OAAO,CAACG,UAAU,CAACa,SAAS,EAAE;EAC7C,IAAIhB,OAAO,CAACG,UAAU,CAACa,SAAS,CAACM,cAAc,CAACD,IAAI,CAAC,EAAE;IACnDD,WAAW,CAACC,IAAI,CAAC,GAAGrB,OAAO,CAACG,UAAU,CAACa,SAAS,CAACK,IAAI,CAAC,CAACH,WAAW;EACtE;AACJ;AACA;AACA;AACA;AACA,MAAMhB,IAAI,CAAC;EACPqB,WAAWA,CAACC,SAAS,GAAG,CAAC,CAAC,EAAE;IACxB,IAAIC,EAAE;IACN,IAAI,CAACD,SAAS,GAAG,CAAC,CAAC,EAAEjB,eAAe,CAACY,aAAa,EAAEjB,IAAI,CAACsB,SAAS,IAAI,CAAC,CAAC,CAAC;IACzE,IAAI,CAACE,WAAW,GAAG,CAAC,CAACD,EAAE,GAAGvB,IAAI,CAACsB,SAAS,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACR,EAAE,KAAK,CAAC,CAAC;IAC3F,IAAI,CAACU,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,eAAe,GAAGR,WAAW,CAACH,EAAE;IACrC,IAAI,CAACY,YAAY,CAACL,SAAS,EAAE,KAAK,CAAC;IACnC,IAAI,CAACM,cAAc,CAAC,IAAI,CAACH,QAAQ,CAAC;EACtC;EACA,OAAOI,sBAAsBA,CAACP,SAAS,EAAE;IACrC,IAAI,CAAC,CAAC,EAAEhB,QAAQ,CAACwB,OAAO,EAAER,SAAS,CAAC,EAAE;MAClC;IACJ;IACA,KAAK,MAAMH,IAAI,IAAIG,SAAS,EAAE;MAC1B,IAAIH,IAAI,KAAK,UAAU,IAAIG,SAAS,CAACF,cAAc,CAACD,IAAI,CAAC,EAAE;QACvD,IAAI,CAAC,IAAI,CAACG,SAAS,CAACH,IAAI,CAAC,EAAE;UACvB,IAAI,CAACG,SAAS,CAACH,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7B;QACA,IAAI,CAACG,SAAS,CAACH,IAAI,CAAC,GAAGvB,MAAM,CAACmC,MAAM,CAACnC,MAAM,CAACmC,MAAM,CAAC,CAAC,CAAC,EAAET,SAAS,CAACH,IAAI,CAAC,CAAC,EAAE,IAAI,CAACG,SAAS,CAACH,IAAI,CAAC,CAAC;MAClG;IACJ;EACJ;EACAQ,YAAYA,CAACL,SAAS,EAAEU,iBAAiB,EAAE;IACvC,IAAIV,SAAS,CAACR,SAAS,EAAE;MACrB,KAAK,MAAMK,IAAI,IAAIG,SAAS,CAACR,SAAS,EAAE;QACpC,IAAIQ,SAAS,CAACR,SAAS,CAACM,cAAc,CAACD,IAAI,CAAC,EAAE;UAC1CG,SAAS,CAACH,IAAI,CAAC,GAAGG,SAAS,CAACR,SAAS,CAACK,IAAI,CAAC,CAACH,WAAW;QAC3D;MACJ;MACA,OAAOM,SAAS,CAACR,SAAS;IAC9B;IACA,IAAIQ,SAAS,CAACb,GAAG,EAAE;MACfa,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACb,GAAG;MAClC,OAAOa,SAAS,CAACb,GAAG;IACxB;IACA;IACA,OAAOa,SAAS,CAACZ,WAAW;IAC5B,OAAOY,SAAS,CAACX,YAAY;IAC7B,OAAOW,SAAS,CAACV,eAAe;IAChC,OAAOU,SAAS,CAACT,gBAAgB;IACjC;IACA,IAAIS,SAAS,CAACG,QAAQ,EAAE;MACpB,IAAI,CAACA,QAAQ,GAAGH,SAAS,CAACG,QAAQ;IACtC;IACA,KAAK,MAAMN,IAAI,IAAIG,SAAS,EAAE;MAC1B,IAAIH,IAAI,KAAK,UAAU,IAAIG,SAAS,CAACF,cAAc,CAACD,IAAI,CAAC,EAAE;QACvD,IAAI,CAAC,IAAI,CAACG,SAAS,CAACH,IAAI,CAAC,EAAE;UACvB,IAAI,CAACG,SAAS,CAACH,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7B;QACA,IAAI,CAACG,SAAS,CAACH,IAAI,CAAC,GAAGa,iBAAiB,GAClCpC,MAAM,CAACmC,MAAM,CAACnC,MAAM,CAACmC,MAAM,CAAC,CAAC,CAAC,EAAET,SAAS,CAACH,IAAI,CAAC,CAAC,EAAE,IAAI,CAACG,SAAS,CAACH,IAAI,CAAC,CAAC,GAAGvB,MAAM,CAACmC,MAAM,CAACnC,MAAM,CAACmC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACT,SAAS,CAACH,IAAI,CAAC,CAAC,EAAEG,SAAS,CAACH,IAAI,CAAC,CAAC;MAC3J;IACJ;EACJ;EACA,OAAOc,IAAIA,CAACX,SAAS,GAAG,CAAC,CAAC,EAAE;IACxB,OAAO,IAAItB,IAAI,CAACsB,SAAS,CAAC;EAC9B;EACAY,GAAGA,CAACf,IAAI,GAAG,EAAE,EAAE;IACXA,IAAI,GAAGA,IAAI,IAAI,IAAI,CAACM,QAAQ;IAC5B,MAAMU,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3C,OAAOA,IAAI,CAACC,QAAQ,CAACjB,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK;EAC9C;EACA,OAAOkB,cAAcA,CAAA,EAAG;IACpB,OAAO,IAAIrC,IAAI,CAAC,CAAC;EACrB;EACA4B,cAAcA,CAACH,QAAQ,EAAEa,KAAK,EAAE;IAC5B,IAAI,CAAC,IAAI,CAAChB,SAAS,CAACG,QAAQ,CAAC,EAAE;MAC3BA,QAAQ,GAAG,IAAI;IACnB;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,eAAe,GAAG,IAAI,CAACJ,SAAS,CAACG,QAAQ,CAAC,GAAG,IAAI,CAACH,SAAS,CAACG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC/E,IAAIa,KAAK,EAAE;MACPA,KAAK,CAAC,CAAC;IACX;EACJ;EACAC,iBAAiBA,CAACd,QAAQ,EAAEe,IAAI,EAAEC,OAAO,EAAE;IACvC,IAAI,CAACnB,SAAS,CAACG,QAAQ,CAAC,GAAGgB,OAAO;EACtC;EACAC,CAACA,CAACC,IAAI,EAAEC,IAAI,EAAE,GAAGC,IAAI,EAAE;IACnB,IAAIC,kBAAkB,GAAG,IAAI,CAACpB,eAAe,CAACiB,IAAI,CAAC;IACnD;IACA;IACA,MAAMI,UAAU,GAAG,IAAI,CAACvB,WAAW,CAACmB,IAAI,CAAC;IACzC,IAAII,UAAU,IAAI,IAAI,CAACrB,eAAe,CAACqB,UAAU,CAAC,EAAE;MAChD;MACAD,kBAAkB,GAAG,IAAI,CAACpB,eAAe,CAACqB,UAAU,CAAC;IACzD;IACA,IAAID,kBAAkB,EAAE;MACpB,MAAME,0BAA0B,GAAGJ,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACK,KAAK;MACzF,IAAID,0BAA0B,IAAI,IAAI,CAACtB,eAAe,CAACsB,0BAA0B,CAAC,EAAE;QAChFJ,IAAI,CAACK,KAAK,GAAG,IAAI,CAACvB,eAAe,CAACsB,0BAA0B,CAAC;MACjE;MACA,OAAOzC,WAAW,CAAC2C,SAAS,CAACC,iBAAiB,CAACL,kBAAkB,EAAEF,IAAI,EAAE,GAAGC,IAAI,CAAC;IACrF;IACA,OAAOtC,WAAW,CAAC2C,SAAS,CAACC,iBAAiB,CAACR,IAAI,EAAEC,IAAI,EAAE,GAAGC,IAAI,CAAC;EACvE;AACJ;AACA/C,OAAO,CAACE,IAAI,GAAGA,IAAI;AACnBA,IAAI,CAACsB,SAAS,GAAGJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}