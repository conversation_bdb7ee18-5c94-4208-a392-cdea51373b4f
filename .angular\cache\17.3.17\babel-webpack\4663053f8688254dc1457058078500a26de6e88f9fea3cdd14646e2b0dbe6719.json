{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.populateProcessInfo = exports.populateProcessSync = void 0;\nconst lodash_1 = require(\"lodash\");\nconst formUtil_1 = require(\"../../utils/formUtil\");\n// This processor ensures that a \"linked\" row context is provided to every component.\nconst populateProcessSync = context => {\n  const {\n    component,\n    path,\n    scope,\n    value\n  } = context;\n  const {\n    data\n  } = scope;\n  if (!scope.populated) scope.populated = [];\n  switch ((0, formUtil_1.getModelType)(component)) {\n    case 'nestedArray':\n      if (!value || !value.length) {\n        const newValue = [{}];\n        (0, lodash_1.set)(data, path, newValue);\n        scope.row = newValue[0];\n        scope.populated.push({\n          path\n        });\n      }\n      break;\n    case 'dataObject':\n    case 'object':\n      if (!value || typeof value !== 'object') {\n        const newValue = {};\n        (0, lodash_1.set)(data, value, newValue);\n        scope.row = newValue;\n        scope.populated.push({\n          path\n        });\n      }\n      break;\n  }\n};\nexports.populateProcessSync = populateProcessSync;\nexports.populateProcessInfo = {\n  name: 'populate',\n  shouldProcess: () => true,\n  processSync: exports.populateProcessSync\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "populateProcessInfo", "populateProcessSync", "lodash_1", "require", "formUtil_1", "context", "component", "path", "scope", "data", "populated", "getModelType", "length", "newValue", "set", "row", "push", "name", "shouldProcess", "processSync"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/populate/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.populateProcessInfo = exports.populateProcessSync = void 0;\nconst lodash_1 = require(\"lodash\");\nconst formUtil_1 = require(\"../../utils/formUtil\");\n// This processor ensures that a \"linked\" row context is provided to every component.\nconst populateProcessSync = (context) => {\n    const { component, path, scope, value } = context;\n    const { data } = scope;\n    if (!scope.populated)\n        scope.populated = [];\n    switch ((0, formUtil_1.getModelType)(component)) {\n        case 'nestedArray':\n            if (!value || !value.length) {\n                const newValue = [{}];\n                (0, lodash_1.set)(data, path, newValue);\n                scope.row = newValue[0];\n                scope.populated.push({\n                    path,\n                });\n            }\n            break;\n        case 'dataObject':\n        case 'object':\n            if (!value || typeof value !== 'object') {\n                const newValue = {};\n                (0, lodash_1.set)(data, value, newValue);\n                scope.row = newValue;\n                scope.populated.push({\n                    path,\n                });\n            }\n            break;\n    }\n};\nexports.populateProcessSync = populateProcessSync;\nexports.populateProcessInfo = {\n    name: 'populate',\n    shouldProcess: () => true,\n    processSync: exports.populateProcessSync,\n};\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,mBAAmB,GAAGF,OAAO,CAACG,mBAAmB,GAAG,KAAK,CAAC;AAClE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,UAAU,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAClD;AACA,MAAMF,mBAAmB,GAAII,OAAO,IAAK;EACrC,MAAM;IAAEC,SAAS;IAAEC,IAAI;IAAEC,KAAK;IAAET;EAAM,CAAC,GAAGM,OAAO;EACjD,MAAM;IAAEI;EAAK,CAAC,GAAGD,KAAK;EACtB,IAAI,CAACA,KAAK,CAACE,SAAS,EAChBF,KAAK,CAACE,SAAS,GAAG,EAAE;EACxB,QAAQ,CAAC,CAAC,EAAEN,UAAU,CAACO,YAAY,EAAEL,SAAS,CAAC;IAC3C,KAAK,aAAa;MACd,IAAI,CAACP,KAAK,IAAI,CAACA,KAAK,CAACa,MAAM,EAAE;QACzB,MAAMC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,EAAEX,QAAQ,CAACY,GAAG,EAAEL,IAAI,EAAEF,IAAI,EAAEM,QAAQ,CAAC;QACvCL,KAAK,CAACO,GAAG,GAAGF,QAAQ,CAAC,CAAC,CAAC;QACvBL,KAAK,CAACE,SAAS,CAACM,IAAI,CAAC;UACjBT;QACJ,CAAC,CAAC;MACN;MACA;IACJ,KAAK,YAAY;IACjB,KAAK,QAAQ;MACT,IAAI,CAACR,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACrC,MAAMc,QAAQ,GAAG,CAAC,CAAC;QACnB,CAAC,CAAC,EAAEX,QAAQ,CAACY,GAAG,EAAEL,IAAI,EAAEV,KAAK,EAAEc,QAAQ,CAAC;QACxCL,KAAK,CAACO,GAAG,GAAGF,QAAQ;QACpBL,KAAK,CAACE,SAAS,CAACM,IAAI,CAAC;UACjBT;QACJ,CAAC,CAAC;MACN;MACA;EACR;AACJ,CAAC;AACDT,OAAO,CAACG,mBAAmB,GAAGA,mBAAmB;AACjDH,OAAO,CAACE,mBAAmB,GAAG;EAC1BiB,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,IAAI;EACzBC,WAAW,EAAErB,OAAO,CAACG;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}