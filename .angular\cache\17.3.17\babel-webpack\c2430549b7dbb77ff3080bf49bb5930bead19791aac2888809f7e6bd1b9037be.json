{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMinimumLengthInfo = exports.validateMinimumLengthSync = exports.validateMinimumLength = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableTextFieldComponent = component => {\n  var _a;\n  return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('minLength'));\n};\nconst getValidationSetting = component => {\n  var _a;\n  let minLength = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.minLength;\n  if (typeof minLength === 'string') {\n    minLength = parseInt(minLength, 10);\n  }\n  return minLength;\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!isValidatableTextFieldComponent(component) || !value) {\n    return false;\n  }\n  if (!value) {\n    return false;\n  }\n  if (!getValidationSetting(component)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMinimumLength = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMinimumLengthSync)(context);\n});\nexports.validateMinimumLength = validateMinimumLength;\nconst validateMinimumLengthSync = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  const minLength = getValidationSetting(component);\n  if (value && minLength && typeof value === 'string') {\n    if (value.length < minLength) {\n      const error = new error_1.FieldError('minLength', Object.assign(Object.assign({}, context), {\n        length: String(minLength),\n        setting: String(minLength)\n      }));\n      return error;\n    }\n  }\n  return null;\n};\nexports.validateMinimumLengthSync = validateMinimumLengthSync;\nexports.validateMinimumLengthInfo = {\n  name: 'validateMinimumLength',\n  process: exports.validateMinimumLength,\n  processSync: exports.validateMinimumLengthSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateMinimumLengthInfo", "validateMinimumLengthSync", "validateMinimum<PERSON>ength", "shouldValidate", "error_1", "require", "isValidatableTextFieldComponent", "component", "_a", "validate", "hasOwnProperty", "getValidationSetting", "<PERSON><PERSON><PERSON><PERSON>", "parseInt", "context", "length", "error", "FieldError", "assign", "String", "setting", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMinimumLength.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMinimumLengthInfo = exports.validateMinimumLengthSync = exports.validateMinimumLength = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableTextFieldComponent = (component) => {\n    var _a;\n    return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('minLength'));\n};\nconst getValidationSetting = (component) => {\n    var _a;\n    let minLength = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.minLength;\n    if (typeof minLength === 'string') {\n        minLength = parseInt(minLength, 10);\n    }\n    return minLength;\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!isValidatableTextFieldComponent(component) || !value) {\n        return false;\n    }\n    if (!value) {\n        return false;\n    }\n    if (!getValidationSetting(component)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMinimumLength = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMinimumLengthSync)(context);\n});\nexports.validateMinimumLength = validateMinimumLength;\nconst validateMinimumLengthSync = (context) => {\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    const minLength = getValidationSetting(component);\n    if (value && minLength && typeof value === 'string') {\n        if (value.length < minLength) {\n            const error = new error_1.FieldError('minLength', Object.assign(Object.assign({}, context), { length: String(minLength), setting: String(minLength) }));\n            return error;\n        }\n    }\n    return null;\n};\nexports.validateMinimumLengthSync = validateMinimumLengthSync;\nexports.validateMinimumLengthInfo = {\n    name: 'validateMinimumLength',\n    process: exports.validateMinimumLength,\n    processSync: exports.validateMinimumLengthSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,yBAAyB,GAAGD,OAAO,CAACE,yBAAyB,GAAGF,OAAO,CAACG,qBAAqB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACvI,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,+BAA+B,GAAIC,SAAS,IAAK;EACnD,IAAIC,EAAE;EACN,OAAOD,SAAS,KAAK,CAACC,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,cAAc,CAAC,WAAW,CAAC,CAAC;AACvH,CAAC;AACD,MAAMC,oBAAoB,GAAIJ,SAAS,IAAK;EACxC,IAAIC,EAAE;EACN,IAAII,SAAS,GAAG,CAACJ,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,SAAS;EAC3F,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IAC/BA,SAAS,GAAGC,QAAQ,CAACD,SAAS,EAAE,EAAE,CAAC;EACvC;EACA,OAAOA,SAAS;AACpB,CAAC;AACD,MAAMT,cAAc,GAAIW,OAAO,IAAK;EAChC,MAAM;IAAEP,SAAS;IAAEvB;EAAM,CAAC,GAAG8B,OAAO;EACpC,IAAI,CAACR,+BAA+B,CAACC,SAAS,CAAC,IAAI,CAACvB,KAAK,EAAE;IACvD,OAAO,KAAK;EAChB;EACA,IAAI,CAACA,KAAK,EAAE;IACR,OAAO,KAAK;EAChB;EACA,IAAI,CAAC2B,oBAAoB,CAACJ,SAAS,CAAC,EAAE;IAClC,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDR,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,qBAAqB,GAAIY,OAAO,IAAKpC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACtF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,yBAAyB,EAAEa,OAAO,CAAC;AAC1D,CAAC,CAAC;AACFf,OAAO,CAACG,qBAAqB,GAAGA,qBAAqB;AACrD,MAAMD,yBAAyB,GAAIa,OAAO,IAAK;EAC3C,MAAM;IAAEP,SAAS;IAAEvB;EAAM,CAAC,GAAG8B,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAEf,OAAO,CAACI,cAAc,EAAEW,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,MAAMF,SAAS,GAAGD,oBAAoB,CAACJ,SAAS,CAAC;EACjD,IAAIvB,KAAK,IAAI4B,SAAS,IAAI,OAAO5B,KAAK,KAAK,QAAQ,EAAE;IACjD,IAAIA,KAAK,CAAC+B,MAAM,GAAGH,SAAS,EAAE;MAC1B,MAAMI,KAAK,GAAG,IAAIZ,OAAO,CAACa,UAAU,CAAC,WAAW,EAAEpB,MAAM,CAACqB,MAAM,CAACrB,MAAM,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAE;QAAEC,MAAM,EAAEI,MAAM,CAACP,SAAS,CAAC;QAAEQ,OAAO,EAAED,MAAM,CAACP,SAAS;MAAE,CAAC,CAAC,CAAC;MACvJ,OAAOI,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AACDjB,OAAO,CAACE,yBAAyB,GAAGA,yBAAyB;AAC7DF,OAAO,CAACC,yBAAyB,GAAG;EAChCqB,IAAI,EAAE,uBAAuB;EAC7BC,OAAO,EAAEvB,OAAO,CAACG,qBAAqB;EACtCqB,WAAW,EAAExB,OAAO,CAACE,yBAAyB;EAC9CuB,aAAa,EAAEzB,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}