{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMaskInfo = exports.validateMaskSync = exports.validateMask = exports.shouldValidate = exports.matchInputMask = void 0;\nconst lodash_1 = require(\"lodash\");\nconst error_1 = require(\"../../../error\");\nconst inputmask_1 = __importDefault(require(\"inputmask\"));\nconst isMaskType = obj => {\n  return (obj === null || obj === void 0 ? void 0 : obj.maskName) && typeof (obj === null || obj === void 0 ? void 0 : obj.maskName) === 'string' && (obj === null || obj === void 0 ? void 0 : obj.value) && typeof (obj === null || obj === void 0 ? void 0 : obj.value) === 'string';\n};\nconst isValidatableComponent = (component, instance) => {\n  if (!component) return false;\n  const {\n    type,\n    inputMask,\n    inputMasks,\n    validate\n  } = component;\n  // For some reason we skip mask validation for time components\n  if (type === 'time') return false;\n  const hasInputMask = inputMask || !(0, lodash_1.isEmpty)(inputMasks);\n  // Include instance.skipMaskValidation check to maintain backward compatibility\n  const skipMaskValidation = (validate === null || validate === void 0 ? void 0 : validate.skipMaskValidation) || (instance === null || instance === void 0 ? void 0 : instance.skipMaskValidation);\n  return hasInputMask && !skipMaskValidation;\n};\nfunction getMaskByLabel(component, maskName) {\n  var _a;\n  if (maskName) {\n    const inputMask = (_a = component.inputMasks) === null || _a === void 0 ? void 0 : _a.find(inputMask => {\n      return inputMask.label === maskName;\n    });\n    return inputMask ? inputMask.mask : undefined;\n  }\n  return;\n}\nfunction getInputMask(mask, placeholderChar) {\n  if (mask instanceof Array) {\n    return mask;\n  }\n  const maskArray = [];\n  for (let i = 0; i < mask.length; i++) {\n    switch (mask[i]) {\n      case '9':\n        maskArray.push(/\\d/);\n        break;\n      case 'A':\n        maskArray.push(/[a-zA-Z]/);\n        break;\n      case 'a':\n        maskArray.push(/[a-z]/);\n        break;\n      case '*':\n        maskArray.push(/[a-zA-Z0-9]/);\n        break;\n      // If char which is used inside mask placeholder was used in the mask, replace it with space to prevent errors\n      case placeholderChar:\n        maskArray.push(' ');\n        break;\n      default:\n        maskArray.push(mask[i]);\n        break;\n    }\n  }\n  return maskArray;\n}\nfunction matchInputMask(value, inputMask) {\n  if (!inputMask) {\n    return true;\n  }\n  // If value is longer than mask, it isn't valid.\n  if (value.length > inputMask.length) {\n    return false;\n  }\n  for (let i = 0; i < inputMask.length; i++) {\n    const char = value[i];\n    const charPart = inputMask[i];\n    if (charPart instanceof RegExp) {\n      if (!charPart.test(char)) {\n        return false;\n      }\n      continue;\n    } else if (charPart !== char) {\n      return false;\n    }\n  }\n  return true;\n}\nexports.matchInputMask = matchInputMask;\nconst shouldValidate = context => {\n  var _a;\n  const {\n    component,\n    value,\n    instance\n  } = context;\n  if (!isValidatableComponent(component, instance) || !value) {\n    return false;\n  }\n  if (value == null) {\n    return false;\n  }\n  if (component.allowMultipleMasks && ((_a = component.inputMasks) === null || _a === void 0 ? void 0 : _a.length)) {\n    const mask = value && isMaskType(value) ? value : undefined;\n    const formioInputMask = getMaskByLabel(component, mask === null || mask === void 0 ? void 0 : mask.maskName);\n    if (formioInputMask && !getInputMask(formioInputMask)) {\n      return false;\n    }\n  } else if (!getInputMask(component.inputMask || '')) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMask = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMaskSync)(context);\n});\nexports.validateMask = validateMask;\n// TODO: this function has side effects\nconst validateMaskSync = context => {\n  var _a;\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  let inputMask;\n  let maskValue;\n  if (component.allowMultipleMasks && ((_a = component.inputMasks) === null || _a === void 0 ? void 0 : _a.length)) {\n    const mask = value && isMaskType(value) ? value : undefined;\n    const formioInputMask = getMaskByLabel(component, mask === null || mask === void 0 ? void 0 : mask.maskName);\n    if (formioInputMask) {\n      inputMask = formioInputMask;\n    }\n    maskValue = mask === null || mask === void 0 ? void 0 : mask.value;\n  } else {\n    inputMask = component.inputMask || '';\n  }\n  if (!inputMask) {\n    return null;\n  }\n  if (value && inputMask && typeof value === 'string' && component.type === 'textfield') {\n    return inputmask_1.default.isValid(value, {\n      mask: inputMask.toString()\n    }) ? null : new error_1.FieldError('mask', context);\n  }\n  const inputMaskArr = getInputMask(inputMask);\n  if (value != null && inputMaskArr) {\n    const error = new error_1.FieldError('mask', context);\n    return matchInputMask(maskValue || value, inputMaskArr) ? null : error;\n  }\n  return null;\n};\nexports.validateMaskSync = validateMaskSync;\nexports.validateMaskInfo = {\n  name: 'validateMask',\n  process: exports.validateMask,\n  processSync: exports.validateMaskSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "validateMaskInfo", "validateMaskSync", "validateMask", "shouldValidate", "matchInputMask", "lodash_1", "require", "error_1", "inputmask_1", "isMaskType", "obj", "<PERSON><PERSON><PERSON>", "isValidatableComponent", "component", "instance", "type", "inputMask", "inputMasks", "validate", "hasInputMask", "isEmpty", "skipMaskValidation", "getMaskByLabel", "_a", "find", "label", "mask", "undefined", "getInputMask", "placeholder<PERSON><PERSON>", "Array", "<PERSON><PERSON><PERSON><PERSON>", "i", "length", "push", "char", "char<PERSON><PERSON>", "RegExp", "test", "context", "allowMultipleMasks", "formioInputMask", "maskValue", "default", "<PERSON><PERSON><PERSON><PERSON>", "toString", "FieldError", "inputMaskArr", "error", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMask.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMaskInfo = exports.validateMaskSync = exports.validateMask = exports.shouldValidate = exports.matchInputMask = void 0;\nconst lodash_1 = require(\"lodash\");\nconst error_1 = require(\"../../../error\");\nconst inputmask_1 = __importDefault(require(\"inputmask\"));\nconst isMaskType = (obj) => {\n    return ((obj === null || obj === void 0 ? void 0 : obj.maskName) &&\n        typeof (obj === null || obj === void 0 ? void 0 : obj.maskName) === 'string' &&\n        (obj === null || obj === void 0 ? void 0 : obj.value) &&\n        typeof (obj === null || obj === void 0 ? void 0 : obj.value) === 'string');\n};\nconst isValidatableComponent = (component, instance) => {\n    if (!component)\n        return false;\n    const { type, inputMask, inputMasks, validate } = component;\n    // For some reason we skip mask validation for time components\n    if (type === 'time')\n        return false;\n    const hasInputMask = inputMask || !(0, lodash_1.isEmpty)(inputMasks);\n    // Include instance.skipMaskValidation check to maintain backward compatibility\n    const skipMaskValidation = (validate === null || validate === void 0 ? void 0 : validate.skipMaskValidation) || (instance === null || instance === void 0 ? void 0 : instance.skipMaskValidation);\n    return hasInputMask && !skipMaskValidation;\n};\nfunction getMaskByLabel(component, maskName) {\n    var _a;\n    if (maskName) {\n        const inputMask = (_a = component.inputMasks) === null || _a === void 0 ? void 0 : _a.find((inputMask) => {\n            return inputMask.label === maskName;\n        });\n        return inputMask ? inputMask.mask : undefined;\n    }\n    return;\n}\nfunction getInputMask(mask, placeholderChar) {\n    if (mask instanceof Array) {\n        return mask;\n    }\n    const maskArray = [];\n    for (let i = 0; i < mask.length; i++) {\n        switch (mask[i]) {\n            case '9':\n                maskArray.push(/\\d/);\n                break;\n            case 'A':\n                maskArray.push(/[a-zA-Z]/);\n                break;\n            case 'a':\n                maskArray.push(/[a-z]/);\n                break;\n            case '*':\n                maskArray.push(/[a-zA-Z0-9]/);\n                break;\n            // If char which is used inside mask placeholder was used in the mask, replace it with space to prevent errors\n            case placeholderChar:\n                maskArray.push(' ');\n                break;\n            default:\n                maskArray.push(mask[i]);\n                break;\n        }\n    }\n    return maskArray;\n}\nfunction matchInputMask(value, inputMask) {\n    if (!inputMask) {\n        return true;\n    }\n    // If value is longer than mask, it isn't valid.\n    if (value.length > inputMask.length) {\n        return false;\n    }\n    for (let i = 0; i < inputMask.length; i++) {\n        const char = value[i];\n        const charPart = inputMask[i];\n        if (charPart instanceof RegExp) {\n            if (!charPart.test(char)) {\n                return false;\n            }\n            continue;\n        }\n        else if (charPart !== char) {\n            return false;\n        }\n    }\n    return true;\n}\nexports.matchInputMask = matchInputMask;\nconst shouldValidate = (context) => {\n    var _a;\n    const { component, value, instance } = context;\n    if (!isValidatableComponent(component, instance) || !value) {\n        return false;\n    }\n    if (value == null) {\n        return false;\n    }\n    if (component.allowMultipleMasks && ((_a = component.inputMasks) === null || _a === void 0 ? void 0 : _a.length)) {\n        const mask = value && isMaskType(value) ? value : undefined;\n        const formioInputMask = getMaskByLabel(component, mask === null || mask === void 0 ? void 0 : mask.maskName);\n        if (formioInputMask && !getInputMask(formioInputMask)) {\n            return false;\n        }\n    }\n    else if (!getInputMask(component.inputMask || '')) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMask = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMaskSync)(context);\n});\nexports.validateMask = validateMask;\n// TODO: this function has side effects\nconst validateMaskSync = (context) => {\n    var _a;\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    let inputMask;\n    let maskValue;\n    if (component.allowMultipleMasks && ((_a = component.inputMasks) === null || _a === void 0 ? void 0 : _a.length)) {\n        const mask = value && isMaskType(value) ? value : undefined;\n        const formioInputMask = getMaskByLabel(component, mask === null || mask === void 0 ? void 0 : mask.maskName);\n        if (formioInputMask) {\n            inputMask = formioInputMask;\n        }\n        maskValue = mask === null || mask === void 0 ? void 0 : mask.value;\n    }\n    else {\n        inputMask = component.inputMask || '';\n    }\n    if (!inputMask) {\n        return null;\n    }\n    if (value && inputMask && typeof value === 'string' && component.type === 'textfield') {\n        return inputmask_1.default.isValid(value, { mask: inputMask.toString() })\n            ? null\n            : new error_1.FieldError('mask', context);\n    }\n    const inputMaskArr = getInputMask(inputMask);\n    if (value != null && inputMaskArr) {\n        const error = new error_1.FieldError('mask', context);\n        return matchInputMask(maskValue || value, inputMaskArr) ? null : error;\n    }\n    return null;\n};\nexports.validateMaskSync = validateMaskSync;\nexports.validateMaskInfo = {\n    name: 'validateMask',\n    process: exports.validateMask,\n    processSync: exports.validateMaskSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,IAAIO,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAElB,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DkB,OAAO,CAACC,gBAAgB,GAAGD,OAAO,CAACE,gBAAgB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,cAAc,GAAGJ,OAAO,CAACK,cAAc,GAAG,KAAK,CAAC;AACrI,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,OAAO,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAME,WAAW,GAAGd,eAAe,CAACY,OAAO,CAAC,WAAW,CAAC,CAAC;AACzD,MAAMG,UAAU,GAAIC,GAAG,IAAK;EACxB,OAAQ,CAACA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACC,QAAQ,KAC3D,QAAQD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACC,QAAQ,CAAC,KAAK,QAAQ,KAC3ED,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC7B,KAAK,CAAC,IACrD,QAAQ6B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC7B,KAAK,CAAC,KAAK,QAAQ;AACjF,CAAC;AACD,MAAM+B,sBAAsB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;EACpD,IAAI,CAACD,SAAS,EACV,OAAO,KAAK;EAChB,MAAM;IAAEE,IAAI;IAAEC,SAAS;IAAEC,UAAU;IAAEC;EAAS,CAAC,GAAGL,SAAS;EAC3D;EACA,IAAIE,IAAI,KAAK,MAAM,EACf,OAAO,KAAK;EAChB,MAAMI,YAAY,GAAGH,SAAS,IAAI,CAAC,CAAC,CAAC,EAAEX,QAAQ,CAACe,OAAO,EAAEH,UAAU,CAAC;EACpE;EACA,MAAMI,kBAAkB,GAAG,CAACH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACG,kBAAkB,MAAMP,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACO,kBAAkB,CAAC;EACjM,OAAOF,YAAY,IAAI,CAACE,kBAAkB;AAC9C,CAAC;AACD,SAASC,cAAcA,CAACT,SAAS,EAAEF,QAAQ,EAAE;EACzC,IAAIY,EAAE;EACN,IAAIZ,QAAQ,EAAE;IACV,MAAMK,SAAS,GAAG,CAACO,EAAE,GAAGV,SAAS,CAACI,UAAU,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAAER,SAAS,IAAK;MACtG,OAAOA,SAAS,CAACS,KAAK,KAAKd,QAAQ;IACvC,CAAC,CAAC;IACF,OAAOK,SAAS,GAAGA,SAAS,CAACU,IAAI,GAAGC,SAAS;EACjD;EACA;AACJ;AACA,SAASC,YAAYA,CAACF,IAAI,EAAEG,eAAe,EAAE;EACzC,IAAIH,IAAI,YAAYI,KAAK,EAAE;IACvB,OAAOJ,IAAI;EACf;EACA,MAAMK,SAAS,GAAG,EAAE;EACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,QAAQN,IAAI,CAACM,CAAC,CAAC;MACX,KAAK,GAAG;QACJD,SAAS,CAACG,IAAI,CAAC,IAAI,CAAC;QACpB;MACJ,KAAK,GAAG;QACJH,SAAS,CAACG,IAAI,CAAC,UAAU,CAAC;QAC1B;MACJ,KAAK,GAAG;QACJH,SAAS,CAACG,IAAI,CAAC,OAAO,CAAC;QACvB;MACJ,KAAK,GAAG;QACJH,SAAS,CAACG,IAAI,CAAC,aAAa,CAAC;QAC7B;MACJ;MACA,KAAKL,eAAe;QAChBE,SAAS,CAACG,IAAI,CAAC,GAAG,CAAC;QACnB;MACJ;QACIH,SAAS,CAACG,IAAI,CAACR,IAAI,CAACM,CAAC,CAAC,CAAC;QACvB;IACR;EACJ;EACA,OAAOD,SAAS;AACpB;AACA,SAAS3B,cAAcA,CAACvB,KAAK,EAAEmC,SAAS,EAAE;EACtC,IAAI,CAACA,SAAS,EAAE;IACZ,OAAO,IAAI;EACf;EACA;EACA,IAAInC,KAAK,CAACoD,MAAM,GAAGjB,SAAS,CAACiB,MAAM,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,SAAS,CAACiB,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAMG,IAAI,GAAGtD,KAAK,CAACmD,CAAC,CAAC;IACrB,MAAMI,QAAQ,GAAGpB,SAAS,CAACgB,CAAC,CAAC;IAC7B,IAAII,QAAQ,YAAYC,MAAM,EAAE;MAC5B,IAAI,CAACD,QAAQ,CAACE,IAAI,CAACH,IAAI,CAAC,EAAE;QACtB,OAAO,KAAK;MAChB;MACA;IACJ,CAAC,MACI,IAAIC,QAAQ,KAAKD,IAAI,EAAE;MACxB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACApC,OAAO,CAACK,cAAc,GAAGA,cAAc;AACvC,MAAMD,cAAc,GAAIoC,OAAO,IAAK;EAChC,IAAIhB,EAAE;EACN,MAAM;IAAEV,SAAS;IAAEhC,KAAK;IAAEiC;EAAS,CAAC,GAAGyB,OAAO;EAC9C,IAAI,CAAC3B,sBAAsB,CAACC,SAAS,EAAEC,QAAQ,CAAC,IAAI,CAACjC,KAAK,EAAE;IACxD,OAAO,KAAK;EAChB;EACA,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAO,KAAK;EAChB;EACA,IAAIgC,SAAS,CAAC2B,kBAAkB,KAAK,CAACjB,EAAE,GAAGV,SAAS,CAACI,UAAU,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,MAAM,CAAC,EAAE;IAC9G,MAAMP,IAAI,GAAG7C,KAAK,IAAI4B,UAAU,CAAC5B,KAAK,CAAC,GAAGA,KAAK,GAAG8C,SAAS;IAC3D,MAAMc,eAAe,GAAGnB,cAAc,CAACT,SAAS,EAAEa,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACf,QAAQ,CAAC;IAC5G,IAAI8B,eAAe,IAAI,CAACb,YAAY,CAACa,eAAe,CAAC,EAAE;MACnD,OAAO,KAAK;IAChB;EACJ,CAAC,MACI,IAAI,CAACb,YAAY,CAACf,SAAS,CAACG,SAAS,IAAI,EAAE,CAAC,EAAE;IAC/C,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDjB,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,YAAY,GAAIqC,OAAO,IAAKhE,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC7E,OAAO,CAAC,CAAC,EAAEwB,OAAO,CAACE,gBAAgB,EAAEsC,OAAO,CAAC;AACjD,CAAC,CAAC;AACFxC,OAAO,CAACG,YAAY,GAAGA,YAAY;AACnC;AACA,MAAMD,gBAAgB,GAAIsC,OAAO,IAAK;EAClC,IAAIhB,EAAE;EACN,MAAM;IAAEV,SAAS;IAAEhC;EAAM,CAAC,GAAG0D,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAExC,OAAO,CAACI,cAAc,EAAEoC,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAIvB,SAAS;EACb,IAAI0B,SAAS;EACb,IAAI7B,SAAS,CAAC2B,kBAAkB,KAAK,CAACjB,EAAE,GAAGV,SAAS,CAACI,UAAU,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,MAAM,CAAC,EAAE;IAC9G,MAAMP,IAAI,GAAG7C,KAAK,IAAI4B,UAAU,CAAC5B,KAAK,CAAC,GAAGA,KAAK,GAAG8C,SAAS;IAC3D,MAAMc,eAAe,GAAGnB,cAAc,CAACT,SAAS,EAAEa,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACf,QAAQ,CAAC;IAC5G,IAAI8B,eAAe,EAAE;MACjBzB,SAAS,GAAGyB,eAAe;IAC/B;IACAC,SAAS,GAAGhB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC7C,KAAK;EACtE,CAAC,MACI;IACDmC,SAAS,GAAGH,SAAS,CAACG,SAAS,IAAI,EAAE;EACzC;EACA,IAAI,CAACA,SAAS,EAAE;IACZ,OAAO,IAAI;EACf;EACA,IAAInC,KAAK,IAAImC,SAAS,IAAI,OAAOnC,KAAK,KAAK,QAAQ,IAAIgC,SAAS,CAACE,IAAI,KAAK,WAAW,EAAE;IACnF,OAAOP,WAAW,CAACmC,OAAO,CAACC,OAAO,CAAC/D,KAAK,EAAE;MAAE6C,IAAI,EAAEV,SAAS,CAAC6B,QAAQ,CAAC;IAAE,CAAC,CAAC,GACnE,IAAI,GACJ,IAAItC,OAAO,CAACuC,UAAU,CAAC,MAAM,EAAEP,OAAO,CAAC;EACjD;EACA,MAAMQ,YAAY,GAAGnB,YAAY,CAACZ,SAAS,CAAC;EAC5C,IAAInC,KAAK,IAAI,IAAI,IAAIkE,YAAY,EAAE;IAC/B,MAAMC,KAAK,GAAG,IAAIzC,OAAO,CAACuC,UAAU,CAAC,MAAM,EAAEP,OAAO,CAAC;IACrD,OAAOnC,cAAc,CAACsC,SAAS,IAAI7D,KAAK,EAAEkE,YAAY,CAAC,GAAG,IAAI,GAAGC,KAAK;EAC1E;EACA,OAAO,IAAI;AACf,CAAC;AACDjD,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3CF,OAAO,CAACC,gBAAgB,GAAG;EACvBiD,IAAI,EAAE,cAAc;EACpBC,OAAO,EAAEnD,OAAO,CAACG,YAAY;EAC7BiD,WAAW,EAAEpD,OAAO,CAACE,gBAAgB;EACrCmD,aAAa,EAAErD,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}