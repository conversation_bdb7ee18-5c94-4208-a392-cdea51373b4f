{"ast": null, "code": "exports.defaults = {};\nexports.set = function (name, value, options) {\n  // Retrieve options and defaults\n  var opts = options || {};\n  var defaults = exports.defaults;\n\n  // Apply default value for unspecified options\n  var expires = opts.expires || defaults.expires;\n  var domain = opts.domain || defaults.domain;\n  var path = opts.path !== undefined ? opts.path : defaults.path !== undefined ? defaults.path : '/';\n  var secure = opts.secure !== undefined ? opts.secure : defaults.secure;\n  var httponly = opts.httponly !== undefined ? opts.httponly : defaults.httponly;\n  var samesite = opts.samesite !== undefined ? opts.samesite : defaults.samesite;\n\n  // Determine cookie expiration date\n  // If succesful the result will be a valid Date, otherwise it will be an invalid Date or false(ish)\n  var expDate = expires ? new Date(\n  // in case expires is an integer, it should specify the number of days till the cookie expires\n  typeof expires === 'number' ? new Date().getTime() + expires * 864e5 :\n  // else expires should be either a Date object or in a format recognized by Date.parse()\n  expires) : 0;\n\n  // Set cookie\n  document.cookie = name.replace(/[^+#$&^`|]/g, encodeURIComponent) // Encode cookie name\n  .replace('(', '%28').replace(')', '%29') + '=' + value.replace(/[^+#$&/:<-\\[\\]-}]/g, encodeURIComponent) + (\n  // Encode cookie value (RFC6265)\n  expDate && expDate.getTime() >= 0 ? ';expires=' + expDate.toUTCString() : '') + (\n  // Add expiration date\n  domain ? ';domain=' + domain : '') + (\n  // Add domain\n  path ? ';path=' + path : '') + (\n  // Add path\n  secure ? ';secure' : '') + (\n  // Add secure option\n  httponly ? ';httponly' : '') + (\n  // Add httponly option\n  samesite ? ';samesite=' + samesite : ''); // Add samesite option\n};\nexports.get = function (name) {\n  var cookies = document.cookie.split(';');\n\n  // Iterate all cookies\n  while (cookies.length) {\n    var cookie = cookies.pop();\n\n    // Determine separator index (\"name=value\")\n    var separatorIndex = cookie.indexOf('=');\n\n    // IE<11 emits the equal sign when the cookie value is empty\n    separatorIndex = separatorIndex < 0 ? cookie.length : separatorIndex;\n    var cookie_name = decodeURIComponent(cookie.slice(0, separatorIndex).replace(/^\\s+/, ''));\n\n    // Return cookie value if the name matches\n    if (cookie_name === name) {\n      return decodeURIComponent(cookie.slice(separatorIndex + 1));\n    }\n  }\n\n  // Return `null` as the cookie was not found\n  return null;\n};\nexports.erase = function (name, options) {\n  exports.set(name, '', {\n    expires: -1,\n    domain: options && options.domain,\n    path: options && options.path,\n    secure: 0,\n    httponly: 0\n  });\n};\nexports.all = function () {\n  var all = {};\n  var cookies = document.cookie.split(';');\n\n  // Iterate all cookies\n  while (cookies.length) {\n    var cookie = cookies.pop();\n\n    // Determine separator index (\"name=value\")\n    var separatorIndex = cookie.indexOf('=');\n\n    // IE<11 emits the equal sign when the cookie value is empty\n    separatorIndex = separatorIndex < 0 ? cookie.length : separatorIndex;\n\n    // add the cookie name and value to the `all` object\n    var cookie_name = decodeURIComponent(cookie.slice(0, separatorIndex).replace(/^\\s+/, ''));\n    all[cookie_name] = decodeURIComponent(cookie.slice(separatorIndex + 1));\n  }\n  return all;\n};", "map": {"version": 3, "names": ["exports", "defaults", "set", "name", "value", "options", "opts", "expires", "domain", "path", "undefined", "secure", "httponly", "samesite", "expDate", "Date", "getTime", "document", "cookie", "replace", "encodeURIComponent", "toUTCString", "get", "cookies", "split", "length", "pop", "separatorIndex", "indexOf", "cookie_name", "decodeURIComponent", "slice", "erase", "all"], "sources": ["D:/workspace/formtest_aug/node_modules/browser-cookies/src/browser-cookies.js"], "sourcesContent": ["exports.defaults = {};\r\n\r\nexports.set = function(name, value, options) {\r\n  // Retrieve options and defaults\r\n  var opts = options || {};\r\n  var defaults = exports.defaults;\r\n\r\n  // Apply default value for unspecified options\r\n  var expires  = opts.expires  || defaults.expires;\r\n  var domain   = opts.domain   || defaults.domain;\r\n  var path     = opts.path     !== undefined ? opts.path     : (defaults.path !== undefined ? defaults.path : '/');\r\n  var secure   = opts.secure   !== undefined ? opts.secure   : defaults.secure;\r\n  var httponly = opts.httponly !== undefined ? opts.httponly : defaults.httponly;\r\n  var samesite = opts.samesite !== undefined ? opts.samesite : defaults.samesite;\r\n\r\n  // Determine cookie expiration date\r\n  // If succesful the result will be a valid Date, otherwise it will be an invalid Date or false(ish)\r\n  var expDate = expires ? new Date(\r\n      // in case expires is an integer, it should specify the number of days till the cookie expires\r\n      typeof expires === 'number' ? new Date().getTime() + (expires * 864e5) :\r\n      // else expires should be either a Date object or in a format recognized by Date.parse()\r\n      expires\r\n  ) : 0;\r\n\r\n  // Set cookie\r\n  document.cookie = name.replace(/[^+#$&^`|]/g, encodeURIComponent)                // Encode cookie name\r\n  .replace('(', '%28')\r\n  .replace(')', '%29') +\r\n  '=' + value.replace(/[^+#$&/:<-\\[\\]-}]/g, encodeURIComponent) +                  // Encode cookie value (RFC6265)\r\n  (expDate && expDate.getTime() >= 0 ? ';expires=' + expDate.toUTCString() : '') + // Add expiration date\r\n  (domain   ? ';domain=' + domain     : '') +                                      // Add domain\r\n  (path     ? ';path='   + path       : '') +                                      // Add path\r\n  (secure   ? ';secure'               : '') +                                      // Add secure option\r\n  (httponly ? ';httponly'             : '') +                                      // Add httponly option\r\n  (samesite ? ';samesite=' + samesite : '');                                       // Add samesite option\r\n};\r\n\r\nexports.get = function(name) {\r\n  var cookies = document.cookie.split(';');\r\n  \r\n  // Iterate all cookies\r\n  while(cookies.length) {\r\n    var cookie = cookies.pop();\r\n\r\n    // Determine separator index (\"name=value\")\r\n    var separatorIndex = cookie.indexOf('=');\r\n\r\n    // IE<11 emits the equal sign when the cookie value is empty\r\n    separatorIndex = separatorIndex < 0 ? cookie.length : separatorIndex;\r\n\r\n    var cookie_name = decodeURIComponent(cookie.slice(0, separatorIndex).replace(/^\\s+/, ''));\r\n\r\n    // Return cookie value if the name matches\r\n    if (cookie_name === name) {\r\n      return decodeURIComponent(cookie.slice(separatorIndex + 1));\r\n    }\r\n  }\r\n\r\n  // Return `null` as the cookie was not found\r\n  return null;\r\n};\r\n\r\nexports.erase = function(name, options) {\r\n  exports.set(name, '', {\r\n    expires:  -1,\r\n    domain:   options && options.domain,\r\n    path:     options && options.path,\r\n    secure:   0,\r\n    httponly: 0}\r\n  );\r\n};\r\n\r\nexports.all = function() {\r\n  var all = {};\r\n  var cookies = document.cookie.split(';');\r\n\r\n  // Iterate all cookies\r\n  while(cookies.length) {\r\n    var cookie = cookies.pop();\r\n\r\n    // Determine separator index (\"name=value\")\r\n    var separatorIndex = cookie.indexOf('=');\r\n\r\n    // IE<11 emits the equal sign when the cookie value is empty\r\n    separatorIndex = separatorIndex < 0 ? cookie.length : separatorIndex;\r\n\r\n    // add the cookie name and value to the `all` object\r\n    var cookie_name = decodeURIComponent(cookie.slice(0, separatorIndex).replace(/^\\s+/, ''));\r\n    all[cookie_name] = decodeURIComponent(cookie.slice(separatorIndex + 1));\r\n  }\r\n\r\n  return all;\r\n};\r\n"], "mappings": "AAAAA,OAAO,CAACC,QAAQ,GAAG,CAAC,CAAC;AAErBD,OAAO,CAACE,GAAG,GAAG,UAASC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAC3C;EACA,IAAIC,IAAI,GAAGD,OAAO,IAAI,CAAC,CAAC;EACxB,IAAIJ,QAAQ,GAAGD,OAAO,CAACC,QAAQ;;EAE/B;EACA,IAAIM,OAAO,GAAID,IAAI,CAACC,OAAO,IAAKN,QAAQ,CAACM,OAAO;EAChD,IAAIC,MAAM,GAAKF,IAAI,CAACE,MAAM,IAAMP,QAAQ,CAACO,MAAM;EAC/C,IAAIC,IAAI,GAAOH,IAAI,CAACG,IAAI,KAASC,SAAS,GAAGJ,IAAI,CAACG,IAAI,GAAQR,QAAQ,CAACQ,IAAI,KAAKC,SAAS,GAAGT,QAAQ,CAACQ,IAAI,GAAG,GAAI;EAChH,IAAIE,MAAM,GAAKL,IAAI,CAACK,MAAM,KAAOD,SAAS,GAAGJ,IAAI,CAACK,MAAM,GAAKV,QAAQ,CAACU,MAAM;EAC5E,IAAIC,QAAQ,GAAGN,IAAI,CAACM,QAAQ,KAAKF,SAAS,GAAGJ,IAAI,CAACM,QAAQ,GAAGX,QAAQ,CAACW,QAAQ;EAC9E,IAAIC,QAAQ,GAAGP,IAAI,CAACO,QAAQ,KAAKH,SAAS,GAAGJ,IAAI,CAACO,QAAQ,GAAGZ,QAAQ,CAACY,QAAQ;;EAE9E;EACA;EACA,IAAIC,OAAO,GAAGP,OAAO,GAAG,IAAIQ,IAAI;EAC5B;EACA,OAAOR,OAAO,KAAK,QAAQ,GAAG,IAAIQ,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAIT,OAAO,GAAG,KAAM;EACtE;EACAA,OACJ,CAAC,GAAG,CAAC;;EAEL;EACAU,QAAQ,CAACC,MAAM,GAAGf,IAAI,CAACgB,OAAO,CAAC,aAAa,EAAEC,kBAAkB,CAAC,CAAgB;EAAA,CAChFD,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CACnBA,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,GACpB,GAAG,GAAGf,KAAK,CAACe,OAAO,CAAC,oBAAoB,EAAEC,kBAAkB,CAAC;EAAoB;EAChFN,OAAO,IAAIA,OAAO,CAACE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,WAAW,GAAGF,OAAO,CAACO,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;EAAG;EAChFb,MAAM,GAAK,UAAU,GAAGA,MAAM,GAAO,EAAE,CAAC;EAAwC;EAChFC,IAAI,GAAO,QAAQ,GAAKA,IAAI,GAAS,EAAE,CAAC;EAAwC;EAChFE,MAAM,GAAK,SAAS,GAAiB,EAAE,CAAC;EAAwC;EAChFC,QAAQ,GAAG,WAAW,GAAe,EAAE,CAAC;EAAwC;EAChFC,QAAQ,GAAG,YAAY,GAAGA,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAuC;AACnF,CAAC;AAEDb,OAAO,CAACsB,GAAG,GAAG,UAASnB,IAAI,EAAE;EAC3B,IAAIoB,OAAO,GAAGN,QAAQ,CAACC,MAAM,CAACM,KAAK,CAAC,GAAG,CAAC;;EAExC;EACA,OAAMD,OAAO,CAACE,MAAM,EAAE;IACpB,IAAIP,MAAM,GAAGK,OAAO,CAACG,GAAG,CAAC,CAAC;;IAE1B;IACA,IAAIC,cAAc,GAAGT,MAAM,CAACU,OAAO,CAAC,GAAG,CAAC;;IAExC;IACAD,cAAc,GAAGA,cAAc,GAAG,CAAC,GAAGT,MAAM,CAACO,MAAM,GAAGE,cAAc;IAEpE,IAAIE,WAAW,GAAGC,kBAAkB,CAACZ,MAAM,CAACa,KAAK,CAAC,CAAC,EAAEJ,cAAc,CAAC,CAACR,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;;IAEzF;IACA,IAAIU,WAAW,KAAK1B,IAAI,EAAE;MACxB,OAAO2B,kBAAkB,CAACZ,MAAM,CAACa,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC7D;EACF;;EAEA;EACA,OAAO,IAAI;AACb,CAAC;AAED3B,OAAO,CAACgC,KAAK,GAAG,UAAS7B,IAAI,EAAEE,OAAO,EAAE;EACtCL,OAAO,CAACE,GAAG,CAACC,IAAI,EAAE,EAAE,EAAE;IACpBI,OAAO,EAAG,CAAC,CAAC;IACZC,MAAM,EAAIH,OAAO,IAAIA,OAAO,CAACG,MAAM;IACnCC,IAAI,EAAMJ,OAAO,IAAIA,OAAO,CAACI,IAAI;IACjCE,MAAM,EAAI,CAAC;IACXC,QAAQ,EAAE;EAAC,CACb,CAAC;AACH,CAAC;AAEDZ,OAAO,CAACiC,GAAG,GAAG,YAAW;EACvB,IAAIA,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIV,OAAO,GAAGN,QAAQ,CAACC,MAAM,CAACM,KAAK,CAAC,GAAG,CAAC;;EAExC;EACA,OAAMD,OAAO,CAACE,MAAM,EAAE;IACpB,IAAIP,MAAM,GAAGK,OAAO,CAACG,GAAG,CAAC,CAAC;;IAE1B;IACA,IAAIC,cAAc,GAAGT,MAAM,CAACU,OAAO,CAAC,GAAG,CAAC;;IAExC;IACAD,cAAc,GAAGA,cAAc,GAAG,CAAC,GAAGT,MAAM,CAACO,MAAM,GAAGE,cAAc;;IAEpE;IACA,IAAIE,WAAW,GAAGC,kBAAkB,CAACZ,MAAM,CAACa,KAAK,CAAC,CAAC,EAAEJ,cAAc,CAAC,CAACR,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACzFc,GAAG,CAACJ,WAAW,CAAC,GAAGC,kBAAkB,CAACZ,MAAM,CAACa,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;EACzE;EAEA,OAAOM,GAAG;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}