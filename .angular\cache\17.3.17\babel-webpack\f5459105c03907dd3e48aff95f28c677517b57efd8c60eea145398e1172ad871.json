{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.applyActions = exports.setCustomAction = exports.setMergeComponentSchema = exports.setValueProperty = exports.setActionProperty = exports.setActionStringProperty = exports.setActionBooleanProperty = exports.checkTrigger = exports.hasLogic = void 0;\nconst conditions_1 = require(\"./conditions\");\nconst lodash_1 = require(\"lodash\");\nconst utils_1 = require(\"./utils\");\nconst formUtil_1 = require(\"./formUtil\");\nconst hasLogic = context => {\n  const {\n    component\n  } = context;\n  const {\n    logic\n  } = component;\n  if (!logic || !logic.length) {\n    return false;\n  }\n  return true;\n};\nexports.hasLogic = hasLogic;\nconst checkTrigger = (context, trigger) => {\n  let shouldTrigger = false;\n  switch (trigger.type) {\n    case 'simple':\n      if ((0, conditions_1.isLegacyConditional)(trigger.simple)) {\n        shouldTrigger = (0, conditions_1.checkLegacyConditional)(trigger.simple, context);\n      } else {\n        shouldTrigger = (0, conditions_1.checkSimpleConditional)(trigger.simple, context);\n      }\n      break;\n    case 'javascript':\n      shouldTrigger = (0, conditions_1.checkCustomConditional)(trigger.javascript, context, 'result');\n      break;\n    case 'json':\n      shouldTrigger = (0, conditions_1.checkJsonConditional)(trigger, context);\n      break;\n    default:\n      shouldTrigger = false;\n      break;\n  }\n  if (shouldTrigger === null) {\n    return false;\n  }\n  return shouldTrigger;\n};\nexports.checkTrigger = checkTrigger;\nfunction setActionBooleanProperty(context, action) {\n  var _a, _b;\n  const {\n    component,\n    scope,\n    path\n  } = context;\n  const property = action.property.value;\n  const currentValue = (0, lodash_1.get)(component, property, false).toString();\n  const newValue = action.state.toString();\n  if (currentValue !== newValue) {\n    (0, lodash_1.set)(component, property, newValue === 'true');\n    // If this is \"logic\" forcing a component to set hidden property, then we will set the \"conditionallyHidden\"\n    // flag which will trigger the clearOnHide functionality.\n    if (property === 'hidden' && path) {\n      if (!scope.conditionals) {\n        scope.conditionals = [];\n      }\n      const conditionallyHidden = (_a = scope.conditionals) === null || _a === void 0 ? void 0 : _a.find(cond => {\n        return cond.path === path;\n      });\n      if (conditionallyHidden) {\n        conditionallyHidden.conditionallyHidden = !!component.hidden;\n        (0, formUtil_1.setComponentScope)(component, 'conditionallyHidden', !!component.hidden);\n      } else {\n        (_b = scope.conditionals) === null || _b === void 0 ? void 0 : _b.push({\n          path,\n          conditionallyHidden: !!component.hidden\n        });\n      }\n    }\n    return true;\n  }\n  return false;\n}\nexports.setActionBooleanProperty = setActionBooleanProperty;\nfunction setActionStringProperty(context, action) {\n  const {\n    component\n  } = context;\n  const property = action.property.value;\n  const textValue = action.property.component ? action[action.property.component] : action.text;\n  const currentValue = (0, lodash_1.get)(component, property, '');\n  const newValue = (0, utils_1.interpolate)(textValue, Object.assign(Object.assign({}, context), {\n    value: ''\n  }), evalContext => {\n    evalContext.value = currentValue;\n  });\n  if (newValue !== currentValue) {\n    (0, lodash_1.set)(component, property, newValue);\n    return true;\n  }\n  return false;\n}\nexports.setActionStringProperty = setActionStringProperty;\nfunction setActionProperty(context, action) {\n  switch (action.property.type) {\n    case 'boolean':\n      return setActionBooleanProperty(context, action);\n    case 'string':\n      return setActionStringProperty(context, action);\n  }\n  return false;\n}\nexports.setActionProperty = setActionProperty;\nfunction setValueProperty(context, action) {\n  const {\n    component,\n    data,\n    path\n  } = context;\n  const oldValue = (0, lodash_1.get)(data, path);\n  const newValue = (0, utils_1.evaluate)(action.value, context, 'value', false, evalContext => {\n    evalContext.value = (0, lodash_1.clone)(oldValue);\n  });\n  if (!(0, lodash_1.isEqual)(oldValue, newValue) && !(component.clearOnHide && (0, conditions_1.conditionallyHidden)(context))) {\n    (0, lodash_1.set)(data, path, newValue);\n    return true;\n  }\n  return false;\n}\nexports.setValueProperty = setValueProperty;\nfunction setMergeComponentSchema(context, action) {\n  const {\n    component,\n    data,\n    path\n  } = context;\n  const oldValue = (0, lodash_1.get)(data, path);\n  const schema = (0, utils_1.evaluate)(action.schemaDefinition, Object.assign(Object.assign({}, context), {\n    value: {}\n  }), 'schema', false, evalContext => {\n    evalContext.value = (0, lodash_1.clone)(oldValue);\n  });\n  const merged = (0, lodash_1.assign)({}, component, schema);\n  if (!(0, lodash_1.isEqual)(component, merged)) {\n    (0, lodash_1.assign)(component, schema);\n    return true;\n  }\n  return false;\n}\nexports.setMergeComponentSchema = setMergeComponentSchema;\nfunction setCustomAction(context, action) {\n  return setValueProperty(context, {\n    type: 'value',\n    value: action.customAction\n  });\n}\nexports.setCustomAction = setCustomAction;\nconst applyActions = context => {\n  const {\n    component\n  } = context;\n  const {\n    logic\n  } = component;\n  if (!logic || !logic.length) {\n    return false;\n  }\n  return logic.reduce((changed, logicItem) => {\n    const {\n      actions,\n      trigger\n    } = logicItem;\n    if (!trigger || !actions || !actions.length || !(0, exports.checkTrigger)(context, trigger)) {\n      return changed;\n    }\n    return actions.reduce((changed, action) => {\n      switch (action.type) {\n        case 'property':\n          if (setActionProperty(context, action)) {\n            return true;\n          }\n          return changed;\n        case 'value':\n          return setValueProperty(context, action) || changed;\n        case 'mergeComponentSchema':\n          if (setMergeComponentSchema(context, action)) {\n            return true;\n          }\n          return changed;\n        case 'customAction':\n          return setCustomAction(context, action) || changed;\n        default:\n          return changed;\n      }\n    }, changed);\n  }, false);\n};\nexports.applyActions = applyActions;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "applyActions", "setCustomAction", "setMergeComponentSchema", "setValueProperty", "setActionProperty", "setActionStringProperty", "setActionBooleanProperty", "checkTrigger", "<PERSON><PERSON><PERSON><PERSON>", "conditions_1", "require", "lodash_1", "utils_1", "formUtil_1", "context", "component", "logic", "length", "trigger", "should<PERSON><PERSON>ger", "type", "isLegacyConditional", "simple", "checkLegacyConditional", "checkSimpleConditional", "checkCustomConditional", "javascript", "checkJsonConditional", "action", "_a", "_b", "scope", "path", "property", "currentValue", "get", "toString", "newValue", "state", "set", "conditionals", "conditionally<PERSON><PERSON><PERSON>", "find", "cond", "hidden", "setComponentScope", "push", "textValue", "text", "interpolate", "assign", "evalContext", "data", "oldValue", "evaluate", "clone", "isEqual", "clearOnHide", "schema", "schemaDefinition", "merged", "customAction", "reduce", "changed", "logicItem", "actions"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/utils/logic.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.applyActions = exports.setCustomAction = exports.setMergeComponentSchema = exports.setValueProperty = exports.setActionProperty = exports.setActionStringProperty = exports.setActionBooleanProperty = exports.checkTrigger = exports.hasLogic = void 0;\nconst conditions_1 = require(\"./conditions\");\nconst lodash_1 = require(\"lodash\");\nconst utils_1 = require(\"./utils\");\nconst formUtil_1 = require(\"./formUtil\");\nconst hasLogic = (context) => {\n    const { component } = context;\n    const { logic } = component;\n    if (!logic || !logic.length) {\n        return false;\n    }\n    return true;\n};\nexports.hasLogic = hasLogic;\nconst checkTrigger = (context, trigger) => {\n    let shouldTrigger = false;\n    switch (trigger.type) {\n        case 'simple':\n            if ((0, conditions_1.isLegacyConditional)(trigger.simple)) {\n                shouldTrigger = (0, conditions_1.checkLegacyConditional)(trigger.simple, context);\n            }\n            else {\n                shouldTrigger = (0, conditions_1.checkSimpleConditional)(trigger.simple, context);\n            }\n            break;\n        case 'javascript':\n            shouldTrigger = (0, conditions_1.checkCustomConditional)(trigger.javascript, context, 'result');\n            break;\n        case 'json':\n            shouldTrigger = (0, conditions_1.checkJsonConditional)(trigger, context);\n            break;\n        default:\n            shouldTrigger = false;\n            break;\n    }\n    if (shouldTrigger === null) {\n        return false;\n    }\n    return shouldTrigger;\n};\nexports.checkTrigger = checkTrigger;\nfunction setActionBooleanProperty(context, action) {\n    var _a, _b;\n    const { component, scope, path } = context;\n    const property = action.property.value;\n    const currentValue = (0, lodash_1.get)(component, property, false).toString();\n    const newValue = action.state.toString();\n    if (currentValue !== newValue) {\n        (0, lodash_1.set)(component, property, newValue === 'true');\n        // If this is \"logic\" forcing a component to set hidden property, then we will set the \"conditionallyHidden\"\n        // flag which will trigger the clearOnHide functionality.\n        if (property === 'hidden' && path) {\n            if (!scope.conditionals) {\n                scope.conditionals = [];\n            }\n            const conditionallyHidden = (_a = scope.conditionals) === null || _a === void 0 ? void 0 : _a.find((cond) => {\n                return cond.path === path;\n            });\n            if (conditionallyHidden) {\n                conditionallyHidden.conditionallyHidden = !!component.hidden;\n                (0, formUtil_1.setComponentScope)(component, 'conditionallyHidden', !!component.hidden);\n            }\n            else {\n                (_b = scope.conditionals) === null || _b === void 0 ? void 0 : _b.push({\n                    path,\n                    conditionallyHidden: !!component.hidden,\n                });\n            }\n        }\n        return true;\n    }\n    return false;\n}\nexports.setActionBooleanProperty = setActionBooleanProperty;\nfunction setActionStringProperty(context, action) {\n    const { component } = context;\n    const property = action.property.value;\n    const textValue = action.property.component\n        ? action[action.property.component]\n        : action.text;\n    const currentValue = (0, lodash_1.get)(component, property, '');\n    const newValue = (0, utils_1.interpolate)(textValue, Object.assign(Object.assign({}, context), { value: '' }), (evalContext) => {\n        evalContext.value = currentValue;\n    });\n    if (newValue !== currentValue) {\n        (0, lodash_1.set)(component, property, newValue);\n        return true;\n    }\n    return false;\n}\nexports.setActionStringProperty = setActionStringProperty;\nfunction setActionProperty(context, action) {\n    switch (action.property.type) {\n        case 'boolean':\n            return setActionBooleanProperty(context, action);\n        case 'string':\n            return setActionStringProperty(context, action);\n    }\n    return false;\n}\nexports.setActionProperty = setActionProperty;\nfunction setValueProperty(context, action) {\n    const { component, data, path } = context;\n    const oldValue = (0, lodash_1.get)(data, path);\n    const newValue = (0, utils_1.evaluate)(action.value, context, 'value', false, (evalContext) => {\n        evalContext.value = (0, lodash_1.clone)(oldValue);\n    });\n    if (!(0, lodash_1.isEqual)(oldValue, newValue) &&\n        !(component.clearOnHide && (0, conditions_1.conditionallyHidden)(context))) {\n        (0, lodash_1.set)(data, path, newValue);\n        return true;\n    }\n    return false;\n}\nexports.setValueProperty = setValueProperty;\nfunction setMergeComponentSchema(context, action) {\n    const { component, data, path } = context;\n    const oldValue = (0, lodash_1.get)(data, path);\n    const schema = (0, utils_1.evaluate)(action.schemaDefinition, Object.assign(Object.assign({}, context), { value: {} }), 'schema', false, (evalContext) => {\n        evalContext.value = (0, lodash_1.clone)(oldValue);\n    });\n    const merged = (0, lodash_1.assign)({}, component, schema);\n    if (!(0, lodash_1.isEqual)(component, merged)) {\n        (0, lodash_1.assign)(component, schema);\n        return true;\n    }\n    return false;\n}\nexports.setMergeComponentSchema = setMergeComponentSchema;\nfunction setCustomAction(context, action) {\n    return setValueProperty(context, { type: 'value', value: action.customAction });\n}\nexports.setCustomAction = setCustomAction;\nconst applyActions = (context) => {\n    const { component } = context;\n    const { logic } = component;\n    if (!logic || !logic.length) {\n        return false;\n    }\n    return logic.reduce((changed, logicItem) => {\n        const { actions, trigger } = logicItem;\n        if (!trigger || !actions || !actions.length || !(0, exports.checkTrigger)(context, trigger)) {\n            return changed;\n        }\n        return actions.reduce((changed, action) => {\n            switch (action.type) {\n                case 'property':\n                    if (setActionProperty(context, action)) {\n                        return true;\n                    }\n                    return changed;\n                case 'value':\n                    return setValueProperty(context, action) || changed;\n                case 'mergeComponentSchema':\n                    if (setMergeComponentSchema(context, action)) {\n                        return true;\n                    }\n                    return changed;\n                case 'customAction':\n                    return setCustomAction(context, action) || changed;\n                default:\n                    return changed;\n            }\n        }, changed);\n    }, false);\n};\nexports.applyActions = applyActions;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,eAAe,GAAGH,OAAO,CAACI,uBAAuB,GAAGJ,OAAO,CAACK,gBAAgB,GAAGL,OAAO,CAACM,iBAAiB,GAAGN,OAAO,CAACO,uBAAuB,GAAGP,OAAO,CAACQ,wBAAwB,GAAGR,OAAO,CAACS,YAAY,GAAGT,OAAO,CAACU,QAAQ,GAAG,KAAK,CAAC;AAC/P,MAAMC,YAAY,GAAGC,OAAO,CAAC,cAAc,CAAC;AAC5C,MAAMC,QAAQ,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAME,OAAO,GAAGF,OAAO,CAAC,SAAS,CAAC;AAClC,MAAMG,UAAU,GAAGH,OAAO,CAAC,YAAY,CAAC;AACxC,MAAMF,QAAQ,GAAIM,OAAO,IAAK;EAC1B,MAAM;IAAEC;EAAU,CAAC,GAAGD,OAAO;EAC7B,MAAM;IAAEE;EAAM,CAAC,GAAGD,SAAS;EAC3B,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAACC,MAAM,EAAE;IACzB,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDnB,OAAO,CAACU,QAAQ,GAAGA,QAAQ;AAC3B,MAAMD,YAAY,GAAGA,CAACO,OAAO,EAAEI,OAAO,KAAK;EACvC,IAAIC,aAAa,GAAG,KAAK;EACzB,QAAQD,OAAO,CAACE,IAAI;IAChB,KAAK,QAAQ;MACT,IAAI,CAAC,CAAC,EAAEX,YAAY,CAACY,mBAAmB,EAAEH,OAAO,CAACI,MAAM,CAAC,EAAE;QACvDH,aAAa,GAAG,CAAC,CAAC,EAAEV,YAAY,CAACc,sBAAsB,EAAEL,OAAO,CAACI,MAAM,EAAER,OAAO,CAAC;MACrF,CAAC,MACI;QACDK,aAAa,GAAG,CAAC,CAAC,EAAEV,YAAY,CAACe,sBAAsB,EAAEN,OAAO,CAACI,MAAM,EAAER,OAAO,CAAC;MACrF;MACA;IACJ,KAAK,YAAY;MACbK,aAAa,GAAG,CAAC,CAAC,EAAEV,YAAY,CAACgB,sBAAsB,EAAEP,OAAO,CAACQ,UAAU,EAAEZ,OAAO,EAAE,QAAQ,CAAC;MAC/F;IACJ,KAAK,MAAM;MACPK,aAAa,GAAG,CAAC,CAAC,EAAEV,YAAY,CAACkB,oBAAoB,EAAET,OAAO,EAAEJ,OAAO,CAAC;MACxE;IACJ;MACIK,aAAa,GAAG,KAAK;MACrB;EACR;EACA,IAAIA,aAAa,KAAK,IAAI,EAAE;IACxB,OAAO,KAAK;EAChB;EACA,OAAOA,aAAa;AACxB,CAAC;AACDrB,OAAO,CAACS,YAAY,GAAGA,YAAY;AACnC,SAASD,wBAAwBA,CAACQ,OAAO,EAAEc,MAAM,EAAE;EAC/C,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;IAAEf,SAAS;IAAEgB,KAAK;IAAEC;EAAK,CAAC,GAAGlB,OAAO;EAC1C,MAAMmB,QAAQ,GAAGL,MAAM,CAACK,QAAQ,CAAClC,KAAK;EACtC,MAAMmC,YAAY,GAAG,CAAC,CAAC,EAAEvB,QAAQ,CAACwB,GAAG,EAAEpB,SAAS,EAAEkB,QAAQ,EAAE,KAAK,CAAC,CAACG,QAAQ,CAAC,CAAC;EAC7E,MAAMC,QAAQ,GAAGT,MAAM,CAACU,KAAK,CAACF,QAAQ,CAAC,CAAC;EACxC,IAAIF,YAAY,KAAKG,QAAQ,EAAE;IAC3B,CAAC,CAAC,EAAE1B,QAAQ,CAAC4B,GAAG,EAAExB,SAAS,EAAEkB,QAAQ,EAAEI,QAAQ,KAAK,MAAM,CAAC;IAC3D;IACA;IACA,IAAIJ,QAAQ,KAAK,QAAQ,IAAID,IAAI,EAAE;MAC/B,IAAI,CAACD,KAAK,CAACS,YAAY,EAAE;QACrBT,KAAK,CAACS,YAAY,GAAG,EAAE;MAC3B;MACA,MAAMC,mBAAmB,GAAG,CAACZ,EAAE,GAAGE,KAAK,CAACS,YAAY,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,IAAI,CAAEC,IAAI,IAAK;QACzG,OAAOA,IAAI,CAACX,IAAI,KAAKA,IAAI;MAC7B,CAAC,CAAC;MACF,IAAIS,mBAAmB,EAAE;QACrBA,mBAAmB,CAACA,mBAAmB,GAAG,CAAC,CAAC1B,SAAS,CAAC6B,MAAM;QAC5D,CAAC,CAAC,EAAE/B,UAAU,CAACgC,iBAAiB,EAAE9B,SAAS,EAAE,qBAAqB,EAAE,CAAC,CAACA,SAAS,CAAC6B,MAAM,CAAC;MAC3F,CAAC,MACI;QACD,CAACd,EAAE,GAAGC,KAAK,CAACS,YAAY,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,IAAI,CAAC;UACnEd,IAAI;UACJS,mBAAmB,EAAE,CAAC,CAAC1B,SAAS,CAAC6B;QACrC,CAAC,CAAC;MACN;IACJ;IACA,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA9C,OAAO,CAACQ,wBAAwB,GAAGA,wBAAwB;AAC3D,SAASD,uBAAuBA,CAACS,OAAO,EAAEc,MAAM,EAAE;EAC9C,MAAM;IAAEb;EAAU,CAAC,GAAGD,OAAO;EAC7B,MAAMmB,QAAQ,GAAGL,MAAM,CAACK,QAAQ,CAAClC,KAAK;EACtC,MAAMgD,SAAS,GAAGnB,MAAM,CAACK,QAAQ,CAAClB,SAAS,GACrCa,MAAM,CAACA,MAAM,CAACK,QAAQ,CAAClB,SAAS,CAAC,GACjCa,MAAM,CAACoB,IAAI;EACjB,MAAMd,YAAY,GAAG,CAAC,CAAC,EAAEvB,QAAQ,CAACwB,GAAG,EAAEpB,SAAS,EAAEkB,QAAQ,EAAE,EAAE,CAAC;EAC/D,MAAMI,QAAQ,GAAG,CAAC,CAAC,EAAEzB,OAAO,CAACqC,WAAW,EAAEF,SAAS,EAAEnD,MAAM,CAACsD,MAAM,CAACtD,MAAM,CAACsD,MAAM,CAAC,CAAC,CAAC,EAAEpC,OAAO,CAAC,EAAE;IAAEf,KAAK,EAAE;EAAG,CAAC,CAAC,EAAGoD,WAAW,IAAK;IAC5HA,WAAW,CAACpD,KAAK,GAAGmC,YAAY;EACpC,CAAC,CAAC;EACF,IAAIG,QAAQ,KAAKH,YAAY,EAAE;IAC3B,CAAC,CAAC,EAAEvB,QAAQ,CAAC4B,GAAG,EAAExB,SAAS,EAAEkB,QAAQ,EAAEI,QAAQ,CAAC;IAChD,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACAvC,OAAO,CAACO,uBAAuB,GAAGA,uBAAuB;AACzD,SAASD,iBAAiBA,CAACU,OAAO,EAAEc,MAAM,EAAE;EACxC,QAAQA,MAAM,CAACK,QAAQ,CAACb,IAAI;IACxB,KAAK,SAAS;MACV,OAAOd,wBAAwB,CAACQ,OAAO,EAAEc,MAAM,CAAC;IACpD,KAAK,QAAQ;MACT,OAAOvB,uBAAuB,CAACS,OAAO,EAAEc,MAAM,CAAC;EACvD;EACA,OAAO,KAAK;AAChB;AACA9B,OAAO,CAACM,iBAAiB,GAAGA,iBAAiB;AAC7C,SAASD,gBAAgBA,CAACW,OAAO,EAAEc,MAAM,EAAE;EACvC,MAAM;IAAEb,SAAS;IAAEqC,IAAI;IAAEpB;EAAK,CAAC,GAAGlB,OAAO;EACzC,MAAMuC,QAAQ,GAAG,CAAC,CAAC,EAAE1C,QAAQ,CAACwB,GAAG,EAAEiB,IAAI,EAAEpB,IAAI,CAAC;EAC9C,MAAMK,QAAQ,GAAG,CAAC,CAAC,EAAEzB,OAAO,CAAC0C,QAAQ,EAAE1B,MAAM,CAAC7B,KAAK,EAAEe,OAAO,EAAE,OAAO,EAAE,KAAK,EAAGqC,WAAW,IAAK;IAC3FA,WAAW,CAACpD,KAAK,GAAG,CAAC,CAAC,EAAEY,QAAQ,CAAC4C,KAAK,EAAEF,QAAQ,CAAC;EACrD,CAAC,CAAC;EACF,IAAI,CAAC,CAAC,CAAC,EAAE1C,QAAQ,CAAC6C,OAAO,EAAEH,QAAQ,EAAEhB,QAAQ,CAAC,IAC1C,EAAEtB,SAAS,CAAC0C,WAAW,IAAI,CAAC,CAAC,EAAEhD,YAAY,CAACgC,mBAAmB,EAAE3B,OAAO,CAAC,CAAC,EAAE;IAC5E,CAAC,CAAC,EAAEH,QAAQ,CAAC4B,GAAG,EAAEa,IAAI,EAAEpB,IAAI,EAAEK,QAAQ,CAAC;IACvC,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACAvC,OAAO,CAACK,gBAAgB,GAAGA,gBAAgB;AAC3C,SAASD,uBAAuBA,CAACY,OAAO,EAAEc,MAAM,EAAE;EAC9C,MAAM;IAAEb,SAAS;IAAEqC,IAAI;IAAEpB;EAAK,CAAC,GAAGlB,OAAO;EACzC,MAAMuC,QAAQ,GAAG,CAAC,CAAC,EAAE1C,QAAQ,CAACwB,GAAG,EAAEiB,IAAI,EAAEpB,IAAI,CAAC;EAC9C,MAAM0B,MAAM,GAAG,CAAC,CAAC,EAAE9C,OAAO,CAAC0C,QAAQ,EAAE1B,MAAM,CAAC+B,gBAAgB,EAAE/D,MAAM,CAACsD,MAAM,CAACtD,MAAM,CAACsD,MAAM,CAAC,CAAC,CAAC,EAAEpC,OAAO,CAAC,EAAE;IAAEf,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAGoD,WAAW,IAAK;IACtJA,WAAW,CAACpD,KAAK,GAAG,CAAC,CAAC,EAAEY,QAAQ,CAAC4C,KAAK,EAAEF,QAAQ,CAAC;EACrD,CAAC,CAAC;EACF,MAAMO,MAAM,GAAG,CAAC,CAAC,EAAEjD,QAAQ,CAACuC,MAAM,EAAE,CAAC,CAAC,EAAEnC,SAAS,EAAE2C,MAAM,CAAC;EAC1D,IAAI,CAAC,CAAC,CAAC,EAAE/C,QAAQ,CAAC6C,OAAO,EAAEzC,SAAS,EAAE6C,MAAM,CAAC,EAAE;IAC3C,CAAC,CAAC,EAAEjD,QAAQ,CAACuC,MAAM,EAAEnC,SAAS,EAAE2C,MAAM,CAAC;IACvC,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA5D,OAAO,CAACI,uBAAuB,GAAGA,uBAAuB;AACzD,SAASD,eAAeA,CAACa,OAAO,EAAEc,MAAM,EAAE;EACtC,OAAOzB,gBAAgB,CAACW,OAAO,EAAE;IAAEM,IAAI,EAAE,OAAO;IAAErB,KAAK,EAAE6B,MAAM,CAACiC;EAAa,CAAC,CAAC;AACnF;AACA/D,OAAO,CAACG,eAAe,GAAGA,eAAe;AACzC,MAAMD,YAAY,GAAIc,OAAO,IAAK;EAC9B,MAAM;IAAEC;EAAU,CAAC,GAAGD,OAAO;EAC7B,MAAM;IAAEE;EAAM,CAAC,GAAGD,SAAS;EAC3B,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAACC,MAAM,EAAE;IACzB,OAAO,KAAK;EAChB;EACA,OAAOD,KAAK,CAAC8C,MAAM,CAAC,CAACC,OAAO,EAAEC,SAAS,KAAK;IACxC,MAAM;MAAEC,OAAO;MAAE/C;IAAQ,CAAC,GAAG8C,SAAS;IACtC,IAAI,CAAC9C,OAAO,IAAI,CAAC+C,OAAO,IAAI,CAACA,OAAO,CAAChD,MAAM,IAAI,CAAC,CAAC,CAAC,EAAEnB,OAAO,CAACS,YAAY,EAAEO,OAAO,EAAEI,OAAO,CAAC,EAAE;MACzF,OAAO6C,OAAO;IAClB;IACA,OAAOE,OAAO,CAACH,MAAM,CAAC,CAACC,OAAO,EAAEnC,MAAM,KAAK;MACvC,QAAQA,MAAM,CAACR,IAAI;QACf,KAAK,UAAU;UACX,IAAIhB,iBAAiB,CAACU,OAAO,EAAEc,MAAM,CAAC,EAAE;YACpC,OAAO,IAAI;UACf;UACA,OAAOmC,OAAO;QAClB,KAAK,OAAO;UACR,OAAO5D,gBAAgB,CAACW,OAAO,EAAEc,MAAM,CAAC,IAAImC,OAAO;QACvD,KAAK,sBAAsB;UACvB,IAAI7D,uBAAuB,CAACY,OAAO,EAAEc,MAAM,CAAC,EAAE;YAC1C,OAAO,IAAI;UACf;UACA,OAAOmC,OAAO;QAClB,KAAK,cAAc;UACf,OAAO9D,eAAe,CAACa,OAAO,EAAEc,MAAM,CAAC,IAAImC,OAAO;QACtD;UACI,OAAOA,OAAO;MACtB;IACJ,CAAC,EAAEA,OAAO,CAAC;EACf,CAAC,EAAE,KAAK,CAAC;AACb,CAAC;AACDjE,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}