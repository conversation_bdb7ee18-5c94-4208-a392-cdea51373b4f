{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMinimumWordsInfo = exports.validateMinimumWordsSync = exports.validateMinimumWords = exports.shouldValidate = void 0;\nconst FieldError_1 = require(\"../../../error/FieldError\");\nconst isValidatableTextFieldComponent = component => {\n  var _a;\n  return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('minWords'));\n};\nconst getValidationSetting = component => {\n  var _a;\n  let minWords = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.minWords;\n  if (typeof minWords === 'string') {\n    minWords = parseInt(minWords, 10);\n  }\n  return minWords;\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!isValidatableTextFieldComponent(component)) {\n    return false;\n  }\n  if (!getValidationSetting(component)) {\n    return false;\n  }\n  if (!value || typeof value !== 'string') {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMinimumWords = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMinimumWordsSync)(context);\n});\nexports.validateMinimumWords = validateMinimumWords;\nconst validateMinimumWordsSync = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  const minWords = getValidationSetting(component);\n  if (minWords && value && typeof value === 'string') {\n    if (value.trim().split(/\\s+/).length < minWords) {\n      const error = new FieldError_1.FieldError('minWords', Object.assign(Object.assign({}, context), {\n        length: String(minWords),\n        setting: String(minWords)\n      }));\n      return error;\n    }\n  }\n  return null;\n};\nexports.validateMinimumWordsSync = validateMinimumWordsSync;\nexports.validateMinimumWordsInfo = {\n  name: 'validateMinimumWords',\n  process: exports.validateMinimumWords,\n  processSync: exports.validateMinimumWordsSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateMinimumWordsInfo", "validateMinimumWordsSync", "validateMinimumWords", "shouldValidate", "FieldError_1", "require", "isValidatableTextFieldComponent", "component", "_a", "validate", "hasOwnProperty", "getValidationSetting", "minWords", "parseInt", "context", "trim", "split", "length", "error", "FieldError", "assign", "String", "setting", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMinimumWords.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMinimumWordsInfo = exports.validateMinimumWordsSync = exports.validateMinimumWords = exports.shouldValidate = void 0;\nconst FieldError_1 = require(\"../../../error/FieldError\");\nconst isValidatableTextFieldComponent = (component) => {\n    var _a;\n    return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('minWords'));\n};\nconst getValidationSetting = (component) => {\n    var _a;\n    let minWords = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.minWords;\n    if (typeof minWords === 'string') {\n        minWords = parseInt(minWords, 10);\n    }\n    return minWords;\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!isValidatableTextFieldComponent(component)) {\n        return false;\n    }\n    if (!getValidationSetting(component)) {\n        return false;\n    }\n    if (!value || typeof value !== 'string') {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMinimumWords = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMinimumWordsSync)(context);\n});\nexports.validateMinimumWords = validateMinimumWords;\nconst validateMinimumWordsSync = (context) => {\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    const minWords = getValidationSetting(component);\n    if (minWords && value && typeof value === 'string') {\n        if (value.trim().split(/\\s+/).length < minWords) {\n            const error = new FieldError_1.FieldError('minWords', Object.assign(Object.assign({}, context), { length: String(minWords), setting: String(minWords) }));\n            return error;\n        }\n    }\n    return null;\n};\nexports.validateMinimumWordsSync = validateMinimumWordsSync;\nexports.validateMinimumWordsInfo = {\n    name: 'validateMinimumWords',\n    process: exports.validateMinimumWords,\n    processSync: exports.validateMinimumWordsSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,wBAAwB,GAAGD,OAAO,CAACE,wBAAwB,GAAGF,OAAO,CAACG,oBAAoB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACpI,MAAMC,YAAY,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACzD,MAAMC,+BAA+B,GAAIC,SAAS,IAAK;EACnD,IAAIC,EAAE;EACN,OAAOD,SAAS,KAAK,CAACC,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,cAAc,CAAC,UAAU,CAAC,CAAC;AACtH,CAAC;AACD,MAAMC,oBAAoB,GAAIJ,SAAS,IAAK;EACxC,IAAIC,EAAE;EACN,IAAII,QAAQ,GAAG,CAACJ,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ;EACzF,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC9BA,QAAQ,GAAGC,QAAQ,CAACD,QAAQ,EAAE,EAAE,CAAC;EACrC;EACA,OAAOA,QAAQ;AACnB,CAAC;AACD,MAAMT,cAAc,GAAIW,OAAO,IAAK;EAChC,MAAM;IAAEP,SAAS;IAAEvB;EAAM,CAAC,GAAG8B,OAAO;EACpC,IAAI,CAACR,+BAA+B,CAACC,SAAS,CAAC,EAAE;IAC7C,OAAO,KAAK;EAChB;EACA,IAAI,CAACI,oBAAoB,CAACJ,SAAS,CAAC,EAAE;IAClC,OAAO,KAAK;EAChB;EACA,IAAI,CAACvB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDe,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,oBAAoB,GAAIY,OAAO,IAAKpC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACrF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,wBAAwB,EAAEa,OAAO,CAAC;AACzD,CAAC,CAAC;AACFf,OAAO,CAACG,oBAAoB,GAAGA,oBAAoB;AACnD,MAAMD,wBAAwB,GAAIa,OAAO,IAAK;EAC1C,MAAM;IAAEP,SAAS;IAAEvB;EAAM,CAAC,GAAG8B,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAEf,OAAO,CAACI,cAAc,EAAEW,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,MAAMF,QAAQ,GAAGD,oBAAoB,CAACJ,SAAS,CAAC;EAChD,IAAIK,QAAQ,IAAI5B,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAChD,IAAIA,KAAK,CAAC+B,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,GAAGL,QAAQ,EAAE;MAC7C,MAAMM,KAAK,GAAG,IAAId,YAAY,CAACe,UAAU,CAAC,UAAU,EAAEtB,MAAM,CAACuB,MAAM,CAACvB,MAAM,CAACuB,MAAM,CAAC,CAAC,CAAC,EAAEN,OAAO,CAAC,EAAE;QAAEG,MAAM,EAAEI,MAAM,CAACT,QAAQ,CAAC;QAAEU,OAAO,EAAED,MAAM,CAACT,QAAQ;MAAE,CAAC,CAAC,CAAC;MACzJ,OAAOM,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AACDnB,OAAO,CAACE,wBAAwB,GAAGA,wBAAwB;AAC3DF,OAAO,CAACC,wBAAwB,GAAG;EAC/BuB,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,EAAEzB,OAAO,CAACG,oBAAoB;EACrCuB,WAAW,EAAE1B,OAAO,CAACE,wBAAwB;EAC7CyB,aAAa,EAAE3B,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}