var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Slovak = {
    weekdays: {
        shorthand: ["<PERSON>", "<PERSON>n", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>tv", "<PERSON><PERSON>", "Sob"],
        longhand: [
            "Ned<PERSON>ľa",
            "Pondelok",
            "Utorok",
            "Streda",
            "Štvrtok",
            "Piatok",
            "Sobota",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "Feb",
            "Mar",
            "Apr",
            "<PERSON>á<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Aug",
            "Sep",
            "Okt",
            "Nov",
            "Dec",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "August",
            "September",
            "Október",
            "November",
            "December",
        ],
    },
    firstDayOfWeek: 1,
    rangeSeparator: " do ",
    time_24hr: true,
    ordinal: function () {
        return ".";
    },
};
fp.l10ns.sk = Slovak;
export default fp.l10ns;
