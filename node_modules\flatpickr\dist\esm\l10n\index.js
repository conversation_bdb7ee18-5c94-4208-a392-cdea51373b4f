var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
import { Arabic as ar } from "./ar";
import { Austria as at } from "./at";
import { Azerbaijan as az } from "./az";
import { Belarusian as be } from "./be";
import { Bosnian as bs } from "./bs";
import { Bulgarian as bg } from "./bg";
import { Bangla as bn } from "./bn";
import { Catalan as cat } from "./cat";
import { Kurdish as ckb } from "./ckb";
import { Czech as cs } from "./cs";
import { Welsh as cy } from "./cy";
import { Danish as da } from "./da";
import { German as de } from "./de";
import { english as en } from "./default";
import { Esperanto as eo } from "./eo";
import { Spanish as es } from "./es";
import { Estonian as et } from "./et";
import { Persian as fa } from "./fa";
import { Finnish as fi } from "./fi";
import { Faroese as fo } from "./fo";
import { French as fr } from "./fr";
import { Greek as gr } from "./gr";
import { Hebrew as he } from "./he";
import { Hindi as hi } from "./hi";
import { Croatian as hr } from "./hr";
import { Hungarian as hu } from "./hu";
import { Armenian as hy } from "./hy";
import { Indonesian as id } from "./id";
import { Icelandic as is } from "./is";
import { Italian as it } from "./it";
import { Japanese as ja } from "./ja";
import { Georgian as ka } from "./ka";
import { Korean as ko } from "./ko";
import { Khmer as km } from "./km";
import { Kazakh as kz } from "./kz";
import { Lithuanian as lt } from "./lt";
import { Latvian as lv } from "./lv";
import { Macedonian as mk } from "./mk";
import { Mongolian as mn } from "./mn";
import { Malaysian as ms } from "./ms";
import { Burmese as my } from "./my";
import { Dutch as nl } from "./nl";
import { NorwegianNynorsk as nn } from "./nn";
import { Norwegian as no } from "./no";
import { Punjabi as pa } from "./pa";
import { Polish as pl } from "./pl";
import { Portuguese as pt } from "./pt";
import { Romanian as ro } from "./ro";
import { Russian as ru } from "./ru";
import { Sinhala as si } from "./si";
import { Slovak as sk } from "./sk";
import { Slovenian as sl } from "./sl";
import { Albanian as sq } from "./sq";
import { Serbian as sr } from "./sr";
import { Swedish as sv } from "./sv";
import { Thai as th } from "./th";
import { Turkish as tr } from "./tr";
import { Ukrainian as uk } from "./uk";
import { Uzbek as uz } from "./uz";
import { UzbekLatin as uzLatn } from "./uz_latn";
import { Vietnamese as vn } from "./vn";
import { Mandarin as zh } from "./zh";
import { MandarinTraditional as zh_tw } from "./zh-tw";
var l10n = {
    ar: ar,
    at: at,
    az: az,
    be: be,
    bg: bg,
    bn: bn,
    bs: bs,
    ca: cat,
    ckb: ckb,
    cat: cat,
    cs: cs,
    cy: cy,
    da: da,
    de: de,
    default: __assign({}, en),
    en: en,
    eo: eo,
    es: es,
    et: et,
    fa: fa,
    fi: fi,
    fo: fo,
    fr: fr,
    gr: gr,
    he: he,
    hi: hi,
    hr: hr,
    hu: hu,
    hy: hy,
    id: id,
    is: is,
    it: it,
    ja: ja,
    ka: ka,
    ko: ko,
    km: km,
    kz: kz,
    lt: lt,
    lv: lv,
    mk: mk,
    mn: mn,
    ms: ms,
    my: my,
    nl: nl,
    nn: nn,
    no: no,
    pa: pa,
    pl: pl,
    pt: pt,
    ro: ro,
    ru: ru,
    si: si,
    sk: sk,
    sl: sl,
    sq: sq,
    sr: sr,
    sv: sv,
    th: th,
    tr: tr,
    uk: uk,
    vn: vn,
    zh: zh,
    zh_tw: zh_tw,
    uz: uz,
    uz_latn: uzLatn,
};
export default l10n;
