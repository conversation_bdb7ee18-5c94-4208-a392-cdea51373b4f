{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fetchProcessInfo = exports.fetchProcess = exports.shouldFetch = void 0;\nconst lodash_1 = require(\"lodash\");\nconst utils_1 = require(\"../../utils\");\nconst formUtil_1 = require(\"../../utils/formUtil\");\nconst shouldFetch = context => {\n  const {\n    component,\n    config\n  } = context;\n  if (component.type !== 'datasource' || (config === null || config === void 0 ? void 0 : config.server) && !(0, lodash_1.get)(component, 'trigger.server', false)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldFetch = shouldFetch;\nconst fetchProcess = context => __awaiter(void 0, void 0, void 0, function* () {\n  var _a;\n  const {\n    component,\n    row,\n    path,\n    scope,\n    config\n  } = context;\n  let _fetch = null;\n  try {\n    _fetch = context.fetch ? context.fetch : fetch;\n  } catch (ignoreErr) {\n    _fetch = null;\n  }\n  if (!_fetch) {\n    console.log('You must provide a fetch interface to the fetch processor.');\n    return;\n  }\n  if (!(0, exports.shouldFetch)(context)) {\n    return;\n  }\n  if (!scope.fetched) scope.fetched = {};\n  const url = (0, utils_1.interpolate)((0, lodash_1.get)(component, 'fetch.url', ''), context);\n  if (!url) {\n    return;\n  }\n  const request = {\n    method: (0, lodash_1.get)(component, 'fetch.method', 'get').toUpperCase(),\n    headers: {}\n  };\n  if ((config === null || config === void 0 ? void 0 : config.headers) && (component === null || component === void 0 ? void 0 : component.fetch) && ((_a = component === null || component === void 0 ? void 0 : component.fetch) === null || _a === void 0 ? void 0 : _a.forwardHeaders)) {\n    request.headers = JSON.parse(JSON.stringify(config.headers));\n    delete request.headers['host'];\n    delete request.headers['content-length'];\n    delete request.headers['content-type'];\n    delete request.headers['connection'];\n    delete request.headers['cache-control'];\n  }\n  request.headers['Accept'] = '*/*';\n  request.headers['user-agent'] = 'Form.io DataSource Component';\n  (0, lodash_1.get)(component, 'fetch.headers', []).map(header => {\n    header.value = (0, utils_1.interpolate)(header.value, context);\n    if (header.value && header.key) {\n      request.headers[header.key] = header.value;\n    }\n    return header;\n  });\n  if ((0, lodash_1.get)(component, 'fetch.authenticate', false) && (config === null || config === void 0 ? void 0 : config.tokens)) {\n    Object.assign(request.headers, config.tokens);\n  }\n  const body = (0, lodash_1.get)(component, 'fetch.specifyBody', '');\n  if (request.method === 'POST') {\n    request.body = JSON.stringify((0, utils_1.evaluate)(body, context, 'body'));\n  }\n  try {\n    // Perform the fetch.\n    const result = yield (yield _fetch(url, request)).json();\n    const mapFunction = (0, lodash_1.get)(component, 'fetch.mapFunction');\n    // Set the row data of the fetched value.\n    const key = (0, formUtil_1.getComponentKey)(component);\n    (0, lodash_1.set)(row, key, mapFunction ? (0, utils_1.evaluate)(mapFunction, Object.assign(Object.assign({}, context), {\n      responseData: result\n    }), 'value') : result);\n    // Make sure the value does not get filtered for now...\n    if (!scope.filter) scope.filter = {};\n    scope.filter[path] = true;\n    scope.fetched[path] = (0, lodash_1.get)(row, key);\n  } catch (err) {\n    console.log(err.message);\n  }\n});\nexports.fetchProcess = fetchProcess;\nexports.fetchProcessInfo = {\n  name: 'fetch',\n  process: exports.fetchProcess,\n  shouldProcess: exports.shouldFetch\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "fetchProcessInfo", "fetchProcess", "shouldFetch", "lodash_1", "require", "utils_1", "formUtil_1", "context", "component", "config", "type", "server", "get", "_a", "row", "path", "scope", "_fetch", "fetch", "ignoreErr", "console", "log", "fetched", "url", "interpolate", "request", "method", "toUpperCase", "headers", "forwardHeaders", "JSON", "parse", "stringify", "map", "header", "key", "tokens", "assign", "body", "evaluate", "json", "mapFunction", "getComponent<PERSON>ey", "set", "responseData", "filter", "err", "message", "name", "process", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/fetch/index.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.fetchProcessInfo = exports.fetchProcess = exports.shouldFetch = void 0;\nconst lodash_1 = require(\"lodash\");\nconst utils_1 = require(\"../../utils\");\nconst formUtil_1 = require(\"../../utils/formUtil\");\nconst shouldFetch = (context) => {\n    const { component, config } = context;\n    if (component.type !== 'datasource' ||\n        ((config === null || config === void 0 ? void 0 : config.server) && !(0, lodash_1.get)(component, 'trigger.server', false))) {\n        return false;\n    }\n    return true;\n};\nexports.shouldFetch = shouldFetch;\nconst fetchProcess = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    const { component, row, path, scope, config } = context;\n    let _fetch = null;\n    try {\n        _fetch = context.fetch ? context.fetch : fetch;\n    }\n    catch (ignoreErr) {\n        _fetch = null;\n    }\n    if (!_fetch) {\n        console.log('You must provide a fetch interface to the fetch processor.');\n        return;\n    }\n    if (!(0, exports.shouldFetch)(context)) {\n        return;\n    }\n    if (!scope.fetched)\n        scope.fetched = {};\n    const url = (0, utils_1.interpolate)((0, lodash_1.get)(component, 'fetch.url', ''), context);\n    if (!url) {\n        return;\n    }\n    const request = {\n        method: (0, lodash_1.get)(component, 'fetch.method', 'get').toUpperCase(),\n        headers: {},\n    };\n    if ((config === null || config === void 0 ? void 0 : config.headers) &&\n        (component === null || component === void 0 ? void 0 : component.fetch) &&\n        ((_a = component === null || component === void 0 ? void 0 : component.fetch) === null || _a === void 0 ? void 0 : _a.forwardHeaders)) {\n        request.headers = JSON.parse(JSON.stringify(config.headers));\n        delete request.headers['host'];\n        delete request.headers['content-length'];\n        delete request.headers['content-type'];\n        delete request.headers['connection'];\n        delete request.headers['cache-control'];\n    }\n    request.headers['Accept'] = '*/*';\n    request.headers['user-agent'] = 'Form.io DataSource Component';\n    (0, lodash_1.get)(component, 'fetch.headers', []).map((header) => {\n        header.value = (0, utils_1.interpolate)(header.value, context);\n        if (header.value && header.key) {\n            request.headers[header.key] = header.value;\n        }\n        return header;\n    });\n    if ((0, lodash_1.get)(component, 'fetch.authenticate', false) && (config === null || config === void 0 ? void 0 : config.tokens)) {\n        Object.assign(request.headers, config.tokens);\n    }\n    const body = (0, lodash_1.get)(component, 'fetch.specifyBody', '');\n    if (request.method === 'POST') {\n        request.body = JSON.stringify((0, utils_1.evaluate)(body, context, 'body'));\n    }\n    try {\n        // Perform the fetch.\n        const result = yield (yield _fetch(url, request)).json();\n        const mapFunction = (0, lodash_1.get)(component, 'fetch.mapFunction');\n        // Set the row data of the fetched value.\n        const key = (0, formUtil_1.getComponentKey)(component);\n        (0, lodash_1.set)(row, key, mapFunction\n            ? (0, utils_1.evaluate)(mapFunction, Object.assign(Object.assign({}, context), { responseData: result }), 'value')\n            : result);\n        // Make sure the value does not get filtered for now...\n        if (!scope.filter)\n            scope.filter = {};\n        scope.filter[path] = true;\n        scope.fetched[path] = (0, lodash_1.get)(row, key);\n    }\n    catch (err) {\n        console.log(err.message);\n    }\n});\nexports.fetchProcess = fetchProcess;\nexports.fetchProcessInfo = {\n    name: 'fetch',\n    process: exports.fetchProcess,\n    shouldProcess: exports.shouldFetch,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,gBAAgB,GAAGD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,WAAW,GAAG,KAAK,CAAC;AAC9E,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,OAAO,GAAGD,OAAO,CAAC,aAAa,CAAC;AACtC,MAAME,UAAU,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAClD,MAAMF,WAAW,GAAIK,OAAO,IAAK;EAC7B,MAAM;IAAEC,SAAS;IAAEC;EAAO,CAAC,GAAGF,OAAO;EACrC,IAAIC,SAAS,CAACE,IAAI,KAAK,YAAY,IAC9B,CAACD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,MAAM,KAAK,CAAC,CAAC,CAAC,EAAER,QAAQ,CAACS,GAAG,EAAEJ,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAE,EAAE;IAC7H,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDT,OAAO,CAACG,WAAW,GAAGA,WAAW;AACjC,MAAMD,YAAY,GAAIM,OAAO,IAAK7B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC7E,IAAImC,EAAE;EACN,MAAM;IAAEL,SAAS;IAAEM,GAAG;IAAEC,IAAI;IAAEC,KAAK;IAAEP;EAAO,CAAC,GAAGF,OAAO;EACvD,IAAIU,MAAM,GAAG,IAAI;EACjB,IAAI;IACAA,MAAM,GAAGV,OAAO,CAACW,KAAK,GAAGX,OAAO,CAACW,KAAK,GAAGA,KAAK;EAClD,CAAC,CACD,OAAOC,SAAS,EAAE;IACdF,MAAM,GAAG,IAAI;EACjB;EACA,IAAI,CAACA,MAAM,EAAE;IACTG,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzE;EACJ;EACA,IAAI,CAAC,CAAC,CAAC,EAAEtB,OAAO,CAACG,WAAW,EAAEK,OAAO,CAAC,EAAE;IACpC;EACJ;EACA,IAAI,CAACS,KAAK,CAACM,OAAO,EACdN,KAAK,CAACM,OAAO,GAAG,CAAC,CAAC;EACtB,MAAMC,GAAG,GAAG,CAAC,CAAC,EAAElB,OAAO,CAACmB,WAAW,EAAE,CAAC,CAAC,EAAErB,QAAQ,CAACS,GAAG,EAAEJ,SAAS,EAAE,WAAW,EAAE,EAAE,CAAC,EAAED,OAAO,CAAC;EAC5F,IAAI,CAACgB,GAAG,EAAE;IACN;EACJ;EACA,MAAME,OAAO,GAAG;IACZC,MAAM,EAAE,CAAC,CAAC,EAAEvB,QAAQ,CAACS,GAAG,EAAEJ,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAACmB,WAAW,CAAC,CAAC;IACzEC,OAAO,EAAE,CAAC;EACd,CAAC;EACD,IAAI,CAACnB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACmB,OAAO,MAC9DpB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACU,KAAK,CAAC,KACtE,CAACL,EAAE,GAAGL,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACU,KAAK,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,cAAc,CAAC,EAAE;IACvIJ,OAAO,CAACG,OAAO,GAAGE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACvB,MAAM,CAACmB,OAAO,CAAC,CAAC;IAC5D,OAAOH,OAAO,CAACG,OAAO,CAAC,MAAM,CAAC;IAC9B,OAAOH,OAAO,CAACG,OAAO,CAAC,gBAAgB,CAAC;IACxC,OAAOH,OAAO,CAACG,OAAO,CAAC,cAAc,CAAC;IACtC,OAAOH,OAAO,CAACG,OAAO,CAAC,YAAY,CAAC;IACpC,OAAOH,OAAO,CAACG,OAAO,CAAC,eAAe,CAAC;EAC3C;EACAH,OAAO,CAACG,OAAO,CAAC,QAAQ,CAAC,GAAG,KAAK;EACjCH,OAAO,CAACG,OAAO,CAAC,YAAY,CAAC,GAAG,8BAA8B;EAC9D,CAAC,CAAC,EAAEzB,QAAQ,CAACS,GAAG,EAAEJ,SAAS,EAAE,eAAe,EAAE,EAAE,CAAC,CAACyB,GAAG,CAAEC,MAAM,IAAK;IAC9DA,MAAM,CAAClD,KAAK,GAAG,CAAC,CAAC,EAAEqB,OAAO,CAACmB,WAAW,EAAEU,MAAM,CAAClD,KAAK,EAAEuB,OAAO,CAAC;IAC9D,IAAI2B,MAAM,CAAClD,KAAK,IAAIkD,MAAM,CAACC,GAAG,EAAE;MAC5BV,OAAO,CAACG,OAAO,CAACM,MAAM,CAACC,GAAG,CAAC,GAAGD,MAAM,CAAClD,KAAK;IAC9C;IACA,OAAOkD,MAAM;EACjB,CAAC,CAAC;EACF,IAAI,CAAC,CAAC,EAAE/B,QAAQ,CAACS,GAAG,EAAEJ,SAAS,EAAE,oBAAoB,EAAE,KAAK,CAAC,KAAKC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC2B,MAAM,CAAC,EAAE;IAC9HvC,MAAM,CAACwC,MAAM,CAACZ,OAAO,CAACG,OAAO,EAAEnB,MAAM,CAAC2B,MAAM,CAAC;EACjD;EACA,MAAME,IAAI,GAAG,CAAC,CAAC,EAAEnC,QAAQ,CAACS,GAAG,EAAEJ,SAAS,EAAE,mBAAmB,EAAE,EAAE,CAAC;EAClE,IAAIiB,OAAO,CAACC,MAAM,KAAK,MAAM,EAAE;IAC3BD,OAAO,CAACa,IAAI,GAAGR,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,EAAE3B,OAAO,CAACkC,QAAQ,EAAED,IAAI,EAAE/B,OAAO,EAAE,MAAM,CAAC,CAAC;EAC/E;EACA,IAAI;IACA;IACA,MAAMd,MAAM,GAAG,MAAM,CAAC,MAAMwB,MAAM,CAACM,GAAG,EAAEE,OAAO,CAAC,EAAEe,IAAI,CAAC,CAAC;IACxD,MAAMC,WAAW,GAAG,CAAC,CAAC,EAAEtC,QAAQ,CAACS,GAAG,EAAEJ,SAAS,EAAE,mBAAmB,CAAC;IACrE;IACA,MAAM2B,GAAG,GAAG,CAAC,CAAC,EAAE7B,UAAU,CAACoC,eAAe,EAAElC,SAAS,CAAC;IACtD,CAAC,CAAC,EAAEL,QAAQ,CAACwC,GAAG,EAAE7B,GAAG,EAAEqB,GAAG,EAAEM,WAAW,GACjC,CAAC,CAAC,EAAEpC,OAAO,CAACkC,QAAQ,EAAEE,WAAW,EAAE5C,MAAM,CAACwC,MAAM,CAACxC,MAAM,CAACwC,MAAM,CAAC,CAAC,CAAC,EAAE9B,OAAO,CAAC,EAAE;MAAEqC,YAAY,EAAEnD;IAAO,CAAC,CAAC,EAAE,OAAO,CAAC,GAChHA,MAAM,CAAC;IACb;IACA,IAAI,CAACuB,KAAK,CAAC6B,MAAM,EACb7B,KAAK,CAAC6B,MAAM,GAAG,CAAC,CAAC;IACrB7B,KAAK,CAAC6B,MAAM,CAAC9B,IAAI,CAAC,GAAG,IAAI;IACzBC,KAAK,CAACM,OAAO,CAACP,IAAI,CAAC,GAAG,CAAC,CAAC,EAAEZ,QAAQ,CAACS,GAAG,EAAEE,GAAG,EAAEqB,GAAG,CAAC;EACrD,CAAC,CACD,OAAOW,GAAG,EAAE;IACR1B,OAAO,CAACC,GAAG,CAACyB,GAAG,CAACC,OAAO,CAAC;EAC5B;AACJ,CAAC,CAAC;AACFhD,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnCF,OAAO,CAACC,gBAAgB,GAAG;EACvBgD,IAAI,EAAE,OAAO;EACbC,OAAO,EAAElD,OAAO,CAACE,YAAY;EAC7BiD,aAAa,EAAEnD,OAAO,CAACG;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}