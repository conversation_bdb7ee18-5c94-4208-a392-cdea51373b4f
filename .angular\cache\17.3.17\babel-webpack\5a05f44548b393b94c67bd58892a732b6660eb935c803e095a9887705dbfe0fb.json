{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.FieldError = void 0;\nconst formUtil_1 = require(\"../utils/formUtil\");\nclass FieldError {\n  constructor(errorKeyOrMessage, context, ruleName = errorKeyOrMessage) {\n    var _a;\n    const {\n      component,\n      hasLabel = true,\n      field = (0, formUtil_1.getComponentErrorField)(component, context),\n      level = 'error'\n    } = context;\n    this.ruleName = ruleName;\n    this.level = level;\n    if ((_a = context.component.validate) === null || _a === void 0 ? void 0 : _a.customMessage) {\n      this.errorKeyOrMessage = context.component.validate.customMessage;\n      this.context = Object.assign(Object.assign({}, context), {\n        hasLabel: false,\n        field,\n        level\n      });\n    } else {\n      this.errorKeyOrMessage = errorKeyOrMessage;\n      this.context = Object.assign(Object.assign({}, context), {\n        hasLabel,\n        field\n      });\n    }\n  }\n}\nexports.FieldError = FieldError;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "FieldError", "formUtil_1", "require", "constructor", "errorKeyOrMessage", "context", "ruleName", "_a", "component", "<PERSON><PERSON><PERSON><PERSON>", "field", "getComponentErrorField", "level", "validate", "customMessage", "assign"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/error/FieldError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FieldError = void 0;\nconst formUtil_1 = require(\"../utils/formUtil\");\nclass FieldError {\n    constructor(errorKeyOrMessage, context, ruleName = errorKeyOrMessage) {\n        var _a;\n        const { component, hasLabel = true, field = (0, formUtil_1.getComponentErrorField)(component, context), level = 'error', } = context;\n        this.ruleName = ruleName;\n        this.level = level;\n        if ((_a = context.component.validate) === null || _a === void 0 ? void 0 : _a.customMessage) {\n            this.errorKeyOrMessage = context.component.validate.customMessage;\n            this.context = Object.assign(Object.assign({}, context), { hasLabel: false, field, level });\n        }\n        else {\n            this.errorKeyOrMessage = errorKeyOrMessage;\n            this.context = Object.assign(Object.assign({}, context), { hasLabel, field });\n        }\n    }\n}\nexports.FieldError = FieldError;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AAC3B,MAAMC,UAAU,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAC/C,MAAMF,UAAU,CAAC;EACbG,WAAWA,CAACC,iBAAiB,EAAEC,OAAO,EAAEC,QAAQ,GAAGF,iBAAiB,EAAE;IAClE,IAAIG,EAAE;IACN,MAAM;MAAEC,SAAS;MAAEC,QAAQ,GAAG,IAAI;MAAEC,KAAK,GAAG,CAAC,CAAC,EAAET,UAAU,CAACU,sBAAsB,EAAEH,SAAS,EAAEH,OAAO,CAAC;MAAEO,KAAK,GAAG;IAAS,CAAC,GAAGP,OAAO;IACpI,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACM,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACL,EAAE,GAAGF,OAAO,CAACG,SAAS,CAACK,QAAQ,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,aAAa,EAAE;MACzF,IAAI,CAACV,iBAAiB,GAAGC,OAAO,CAACG,SAAS,CAACK,QAAQ,CAACC,aAAa;MACjE,IAAI,CAACT,OAAO,GAAGT,MAAM,CAACmB,MAAM,CAACnB,MAAM,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAEV,OAAO,CAAC,EAAE;QAAEI,QAAQ,EAAE,KAAK;QAAEC,KAAK;QAAEE;MAAM,CAAC,CAAC;IAC/F,CAAC,MACI;MACD,IAAI,CAACR,iBAAiB,GAAGA,iBAAiB;MAC1C,IAAI,CAACC,OAAO,GAAGT,MAAM,CAACmB,MAAM,CAACnB,MAAM,CAACmB,MAAM,CAAC,CAAC,CAAC,EAAEV,OAAO,CAAC,EAAE;QAAEI,QAAQ;QAAEC;MAAM,CAAC,CAAC;IACjF;EACJ;AACJ;AACAZ,OAAO,CAACE,UAAU,GAAGA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}