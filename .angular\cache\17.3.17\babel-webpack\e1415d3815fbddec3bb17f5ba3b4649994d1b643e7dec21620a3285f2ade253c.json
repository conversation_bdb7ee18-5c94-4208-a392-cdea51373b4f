{"ast": null, "code": "var isObject = require('./isObject');\n\n/**\n * Checks if `value` is suitable for strict equality comparisons, i.e. `===`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` if suitable for strict\n *  equality comparisons, else `false`.\n */\nfunction isStrictComparable(value) {\n  return value === value && !isObject(value);\n}\nmodule.exports = isStrictComparable;", "map": {"version": 3, "names": ["isObject", "require", "isStrictComparable", "value", "module", "exports"], "sources": ["D:/workspace/formtest_aug/node_modules/lodash/_isStrictComparable.js"], "sourcesContent": ["var isObject = require('./isObject');\n\n/**\n * Checks if `value` is suitable for strict equality comparisons, i.e. `===`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` if suitable for strict\n *  equality comparisons, else `false`.\n */\nfunction isStrictComparable(value) {\n  return value === value && !isObject(value);\n}\n\nmodule.exports = isStrictComparable;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAOA,KAAK,KAAKA,KAAK,IAAI,CAACH,QAAQ,CAACG,KAAK,CAAC;AAC5C;AAEAC,MAAM,CAACC,OAAO,GAAGH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}