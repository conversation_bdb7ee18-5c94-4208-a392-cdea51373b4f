{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.VALIDATION_ERRORS = void 0;\nconst en_1 = require(\"./en\");\nexports.VALIDATION_ERRORS = {\n  en: en_1.EN_ERRORS\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "VALIDATION_ERRORS", "en_1", "require", "en", "EN_ERRORS"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/i18n/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.VALIDATION_ERRORS = void 0;\nconst en_1 = require(\"./en\");\nexports.VALIDATION_ERRORS = {\n    en: en_1.EN_ERRORS,\n};\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,iBAAiB,GAAG,KAAK,CAAC;AAClC,MAAMC,IAAI,GAAGC,OAAO,CAAC,MAAM,CAAC;AAC5BJ,OAAO,CAACE,iBAAiB,GAAG;EACxBG,EAAE,EAAEF,IAAI,CAACG;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}