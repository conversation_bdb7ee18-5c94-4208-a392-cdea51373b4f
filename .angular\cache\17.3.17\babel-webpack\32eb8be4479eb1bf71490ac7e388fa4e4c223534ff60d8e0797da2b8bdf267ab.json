{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ProcessorError = void 0;\nclass ProcessorError extends Error {\n  constructor(message, context, processor = 'unknown') {\n    super(message);\n    this.message = `${message}\\nin ${processor} at ${context.path}`;\n    const {\n      component,\n      path,\n      data,\n      row\n    } = context;\n    this.context = {\n      component,\n      path,\n      data,\n      row\n    };\n  }\n}\nexports.ProcessorError = ProcessorError;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ProcessorError", "Error", "constructor", "message", "context", "processor", "path", "component", "data", "row"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/error/ProcessorError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ProcessorError = void 0;\nclass ProcessorError extends Error {\n    constructor(message, context, processor = 'unknown') {\n        super(message);\n        this.message = `${message}\\nin ${processor} at ${context.path}`;\n        const { component, path, data, row } = context;\n        this.context = { component, path, data, row };\n    }\n}\nexports.ProcessorError = ProcessorError;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,cAAc,GAAG,KAAK,CAAC;AAC/B,MAAMA,cAAc,SAASC,KAAK,CAAC;EAC/BC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAEC,SAAS,GAAG,SAAS,EAAE;IACjD,KAAK,CAACF,OAAO,CAAC;IACd,IAAI,CAACA,OAAO,GAAG,GAAGA,OAAO,QAAQE,SAAS,OAAOD,OAAO,CAACE,IAAI,EAAE;IAC/D,MAAM;MAAEC,SAAS;MAAED,IAAI;MAAEE,IAAI;MAAEC;IAAI,CAAC,GAAGL,OAAO;IAC9C,IAAI,CAACA,OAAO,GAAG;MAAEG,SAAS;MAAED,IAAI;MAAEE,IAAI;MAAEC;IAAI,CAAC;EACjD;AACJ;AACAX,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}