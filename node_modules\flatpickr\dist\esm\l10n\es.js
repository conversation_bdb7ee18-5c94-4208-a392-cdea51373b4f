var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Spanish = {
    weekdays: {
        shorthand: ["<PERSON>", "<PERSON>n", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
        longhand: [
            "<PERSON>",
            "<PERSON><PERSON>",
            "Mart<PERSON>",
            "Miércoles",
            "Jueves",
            "Viernes",
            "Sábado",
        ],
    },
    months: {
        shorthand: [
            "Ene",
            "Feb",
            "Mar",
            "Abr",
            "May",
            "Jun",
            "Jul",
            "Ago",
            "Sep",
            "Oct",
            "Nov",
            "Dic",
        ],
        longhand: [
            "<PERSON><PERSON>",
            "Febrero",
            "<PERSON><PERSON>",
            "<PERSON>bri<PERSON>",
            "Mayo",
            "Jun<PERSON>",
            "Julio",
            "Agosto",
            "Septiembre",
            "Octubre",
            "Noviembre",
            "Diciembre",
        ],
    },
    ordinal: function () {
        return "º";
    },
    firstDayOfWeek: 1,
    rangeSeparator: " a ",
    time_24hr: true,
};
fp.l10ns.es = Spanish;
export default fp.l10ns;
