{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateAvailableItemsInfo = exports.validateAvailableItemsSync = exports.shouldValidate = exports.validateAvailableItems = void 0;\nconst lodash_1 = require(\"lodash\");\nconst error_1 = require(\"../../../error\");\nconst utils_1 = require(\"../../../utils\");\nconst util_1 = require(\"../util\");\nconst error_2 = require(\"../../../utils/error\");\nfunction isValidatableRadioComponent(component) {\n  var _a;\n  return component && component.type === 'radio' && !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.onlyAvailableItems);\n}\nfunction isValidateableSelectComponent(component) {\n  var _a;\n  return component && !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.onlyAvailableItems) && component.type === 'select' && component.dataSrc !== 'resource';\n}\nfunction isValidateableSelectBoxesComponent(component) {\n  var _a;\n  return component && !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.onlyAvailableItems) && component.type === 'selectboxes';\n}\nfunction mapDynamicValues(component, values) {\n  return values.map(value => {\n    if (component.valueProperty) {\n      return value[component.valueProperty];\n    }\n    return value;\n  });\n}\nfunction mapStaticValues(values) {\n  return values.map(obj => obj.value);\n}\nconst getAvailableDynamicValues = (component, context) => __awaiter(void 0, void 0, void 0, function* () {\n  let _fetch = null;\n  try {\n    _fetch = context.fetch ? context.fetch : fetch;\n  } catch (ignoreErr) {\n    _fetch = null;\n  }\n  try {\n    if (!_fetch) {\n      console.log('You must provide a fetch interface to the fetch processor.');\n      return null;\n    }\n    const response = yield _fetch(component.data.url, {\n      method: 'GET'\n    });\n    const data = yield response.json();\n    return data ? mapDynamicValues(component, data) : null;\n  } catch (err) {\n    console.error((0, error_2.getErrorMessage)(err));\n    return null;\n  }\n});\nfunction getAvailableSelectValues(component, context) {\n  return __awaiter(this, void 0, void 0, function* () {\n    if ((0, lodash_1.isUndefined)(component.dataSrc) && component.data.hasOwnProperty('values')) {\n      component.dataSrc = 'values';\n    }\n    switch (component.dataSrc) {\n      case 'values':\n        if (Array.isArray(component.data.values)) {\n          return mapStaticValues(component.data.values);\n        }\n        throw new error_1.ProcessorError(`Failed to validate available values in static values select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n      case 'json':\n        {\n          if (typeof component.data.json === 'string') {\n            try {\n              return mapDynamicValues(component, JSON.parse(component.data.json));\n            } catch (err) {\n              throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': ${err}`, context, 'validate:validateAvailableItems');\n            }\n          } else if (Array.isArray(component.data.json)) {\n            // TODO: need to retype this\n            return mapDynamicValues(component, component.data.json);\n          } else {\n            throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n          }\n        }\n      case 'custom':\n        {\n          const customItems = (0, utils_1.evaluate)(component.data.custom, {\n            values: []\n          }, 'values');\n          if ((0, util_1.isPromise)(customItems)) {\n            const resolvedCustomItems = yield customItems;\n            if (Array.isArray(resolvedCustomItems)) {\n              return resolvedCustomItems;\n            }\n            throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n          }\n          if (Array.isArray(customItems)) {\n            return customItems;\n          } else {\n            throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n          }\n        }\n      case 'url':\n        return yield getAvailableDynamicValues(component, context);\n      default:\n        throw new error_1.ProcessorError(`Failed to validate available values in select component '${component.key}': data source ${component.dataSrc} is not valid}`, context, 'validate:validateAvailableItems');\n    }\n  });\n}\nfunction getAvailableSelectValuesSync(component, context) {\n  var _a;\n  if ((0, lodash_1.isUndefined)(component.dataSrc) && component.data.hasOwnProperty('values')) {\n    component.dataSrc = 'values';\n  }\n  switch (component.dataSrc) {\n    case 'values':\n      if (Array.isArray((_a = component.data) === null || _a === void 0 ? void 0 : _a.values)) {\n        return mapStaticValues(component.data.values);\n      }\n      throw new error_1.ProcessorError(`Failed to validate available values in static values select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n    case 'json':\n      {\n        if (typeof component.data.json === 'string') {\n          try {\n            return mapDynamicValues(component, JSON.parse(component.data.json));\n          } catch (err) {\n            throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': ${err}`, context, 'validate:validateAvailableItems');\n          }\n        } else if (Array.isArray(component.data.json)) {\n          // TODO: need to retype this\n          return mapDynamicValues(component, component.data.json);\n        } else {\n          throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n        }\n      }\n    case 'custom':\n      {\n        const customItems = (0, utils_1.evaluate)(component.data.custom, {\n          values: []\n        }, 'values');\n        if (Array.isArray(customItems)) {\n          return customItems;\n        } else {\n          throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n        }\n      }\n    case 'url':\n      return null;\n    default:\n      throw new error_1.ProcessorError(`Failed to validate available values in select component '${component.key}': data source ${component.dataSrc} is not valid}`, context, 'validate:validateAvailableItems');\n  }\n}\nfunction compareComplexValues(valueA, valueB, context) {\n  if (!(0, util_1.isObject)(valueA) || !(0, util_1.isObject)(valueB)) {\n    return false;\n  }\n  try {\n    // TODO: we need to have normalized values here at this moment, otherwise\n    // this won't work\n    return JSON.stringify(valueA) === JSON.stringify(valueB);\n  } catch (err) {\n    throw new error_1.ProcessorError(`Error while comparing available values: ${err}`, context, 'validate:validateAvailableItems');\n  }\n}\nconst validateAvailableItems = context => __awaiter(void 0, void 0, void 0, function* () {\n  const {\n    component,\n    value\n  } = context;\n  const error = new error_1.FieldError('invalidOption', context, 'onlyAvailableItems');\n  try {\n    if (isValidatableRadioComponent(component)) {\n      if (value == null || (0, lodash_1.isEmpty)(value)) {\n        return null;\n      }\n      const values = component.dataSrc === 'url' ? yield getAvailableDynamicValues(component, context) : component.values;\n      if (values) {\n        if ((0, util_1.isObject)(value)) {\n          return values.find(optionValue => compareComplexValues(optionValue, value, context)) !== undefined ? null : error;\n        }\n        return values.find(optionValue => optionValue.value === value || optionValue === value) !== undefined ? null : error;\n      }\n      return null;\n    } else if (isValidateableSelectComponent(component)) {\n      if (value == null || (0, lodash_1.isEmpty)(value)) {\n        return null;\n      }\n      const values = yield getAvailableSelectValues(component, context);\n      if (values) {\n        if ((0, util_1.isObject)(value)) {\n          return values.find(optionValue => compareComplexValues(optionValue, value, context)) !== undefined ? null : error;\n        }\n        return values.find(optionValue => optionValue === value) !== undefined ? null : error;\n      }\n    } else if (isValidateableSelectBoxesComponent(component)) {\n      if (value == null || (0, lodash_1.isEmpty)(value) || !(0, util_1.isObject)(value)) {\n        return null;\n      }\n      const values = component.dataSrc === 'url' ? yield getAvailableDynamicValues(component, context) : component.values.map(val => val.value);\n      if (values) {\n        return (0, lodash_1.difference)(Object.keys(value), values).length ? error : null;\n      }\n    }\n  } catch (err) {\n    throw new error_1.ProcessorError(err.message || err, context, 'validate:validateAvailableItems');\n  }\n  return null;\n});\nexports.validateAvailableItems = validateAvailableItems;\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (value == null || (0, lodash_1.isEmpty)(value)) {\n    return false;\n  }\n  if (isValidatableRadioComponent(component)) {\n    return true;\n  }\n  if (isValidateableSelectComponent(component)) {\n    return true;\n  }\n  if (isValidateableSelectBoxesComponent(component)) {\n    return true;\n  }\n  return false;\n};\nexports.shouldValidate = shouldValidate;\nconst validateAvailableItemsSync = context => {\n  const {\n    component,\n    value\n  } = context;\n  const error = new error_1.FieldError('invalidOption', context, 'onlyAvailableItems');\n  try {\n    if (!(0, exports.shouldValidate)(context)) {\n      return null;\n    }\n    if (isValidatableRadioComponent(component) && component.dataSrc !== 'url') {\n      const values = component.values;\n      if (values) {\n        return values.findIndex(({\n          value: optionValue\n        }) => optionValue === value) !== -1 ? null : error;\n      }\n      return null;\n    } else if (isValidateableSelectComponent(component)) {\n      const values = getAvailableSelectValuesSync(component, context);\n      if (values) {\n        if ((0, util_1.isObject)(value)) {\n          return values.find(optionValue => compareComplexValues(optionValue, value, context)) !== undefined ? null : error;\n        }\n        return values.find(optionValue => optionValue === value) !== undefined ? null : error;\n      }\n    } else if (isValidateableSelectBoxesComponent(component) && component.dataSrc !== 'url') {\n      if (value == null || (0, lodash_1.isEmpty)(value) || !(0, util_1.isObject)(value)) {\n        return null;\n      }\n      const values = component.values.map(val => val.value);\n      if (values) {\n        return (0, lodash_1.difference)(Object.keys(value), values).length ? error : null;\n      }\n    }\n  } catch (err) {\n    throw new error_1.ProcessorError(err.message || err, context, 'validate:validateAvailableItems');\n  }\n  return null;\n};\nexports.validateAvailableItemsSync = validateAvailableItemsSync;\nexports.validateAvailableItemsInfo = {\n  name: 'validateAvailableItems',\n  process: exports.validateAvailableItems,\n  processSync: exports.validateAvailableItemsSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateAvailableItemsInfo", "validateAvailableItemsSync", "shouldValidate", "validateAvailableItems", "lodash_1", "require", "error_1", "utils_1", "util_1", "error_2", "isValidatableRadioComponent", "component", "_a", "type", "validate", "onlyAvailableItems", "isValidateableSelectComponent", "dataSrc", "isValidateableSelectBoxesComponent", "mapDynamicValues", "values", "map", "valueProperty", "mapStaticValues", "obj", "getAvailableDynamicValues", "context", "_fetch", "fetch", "ignoreErr", "console", "log", "response", "data", "url", "method", "json", "err", "error", "getErrorMessage", "getAvailableSelectValues", "isUndefined", "hasOwnProperty", "Array", "isArray", "ProcessorError", "key", "JSON", "parse", "customItems", "evaluate", "custom", "isPromise", "resolvedCustomItems", "getAvailableSelectValuesSync", "compareComplexValues", "valueA", "valueB", "isObject", "stringify", "FieldError", "isEmpty", "find", "optionValue", "undefined", "val", "difference", "keys", "length", "message", "findIndex", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateAvailableItems.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateAvailableItemsInfo = exports.validateAvailableItemsSync = exports.shouldValidate = exports.validateAvailableItems = void 0;\nconst lodash_1 = require(\"lodash\");\nconst error_1 = require(\"../../../error\");\nconst utils_1 = require(\"../../../utils\");\nconst util_1 = require(\"../util\");\nconst error_2 = require(\"../../../utils/error\");\nfunction isValidatableRadioComponent(component) {\n    var _a;\n    return component && component.type === 'radio' && !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.onlyAvailableItems);\n}\nfunction isValidateableSelectComponent(component) {\n    var _a;\n    return (component &&\n        !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.onlyAvailableItems) &&\n        component.type === 'select' &&\n        component.dataSrc !== 'resource');\n}\nfunction isValidateableSelectBoxesComponent(component) {\n    var _a;\n    return component && !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.onlyAvailableItems) && component.type === 'selectboxes';\n}\nfunction mapDynamicValues(component, values) {\n    return values.map((value) => {\n        if (component.valueProperty) {\n            return value[component.valueProperty];\n        }\n        return value;\n    });\n}\nfunction mapStaticValues(values) {\n    return values.map((obj) => obj.value);\n}\nconst getAvailableDynamicValues = (component, context) => __awaiter(void 0, void 0, void 0, function* () {\n    let _fetch = null;\n    try {\n        _fetch = context.fetch ? context.fetch : fetch;\n    }\n    catch (ignoreErr) {\n        _fetch = null;\n    }\n    try {\n        if (!_fetch) {\n            console.log('You must provide a fetch interface to the fetch processor.');\n            return null;\n        }\n        const response = yield _fetch(component.data.url, { method: 'GET' });\n        const data = yield response.json();\n        return data ? mapDynamicValues(component, data) : null;\n    }\n    catch (err) {\n        console.error((0, error_2.getErrorMessage)(err));\n        return null;\n    }\n});\nfunction getAvailableSelectValues(component, context) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if ((0, lodash_1.isUndefined)(component.dataSrc) && component.data.hasOwnProperty('values')) {\n            component.dataSrc = 'values';\n        }\n        switch (component.dataSrc) {\n            case 'values':\n                if (Array.isArray(component.data.values)) {\n                    return mapStaticValues(component.data.values);\n                }\n                throw new error_1.ProcessorError(`Failed to validate available values in static values select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n            case 'json': {\n                if (typeof component.data.json === 'string') {\n                    try {\n                        return mapDynamicValues(component, JSON.parse(component.data.json));\n                    }\n                    catch (err) {\n                        throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': ${err}`, context, 'validate:validateAvailableItems');\n                    }\n                }\n                else if (Array.isArray(component.data.json)) {\n                    // TODO: need to retype this\n                    return mapDynamicValues(component, component.data.json);\n                }\n                else {\n                    throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n                }\n            }\n            case 'custom': {\n                const customItems = (0, utils_1.evaluate)(component.data.custom, {\n                    values: [],\n                }, 'values');\n                if ((0, util_1.isPromise)(customItems)) {\n                    const resolvedCustomItems = yield customItems;\n                    if (Array.isArray(resolvedCustomItems)) {\n                        return resolvedCustomItems;\n                    }\n                    throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n                }\n                if (Array.isArray(customItems)) {\n                    return customItems;\n                }\n                else {\n                    throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n                }\n            }\n            case 'url':\n                return yield getAvailableDynamicValues(component, context);\n            default:\n                throw new error_1.ProcessorError(`Failed to validate available values in select component '${component.key}': data source ${component.dataSrc} is not valid}`, context, 'validate:validateAvailableItems');\n        }\n    });\n}\nfunction getAvailableSelectValuesSync(component, context) {\n    var _a;\n    if ((0, lodash_1.isUndefined)(component.dataSrc) && component.data.hasOwnProperty('values')) {\n        component.dataSrc = 'values';\n    }\n    switch (component.dataSrc) {\n        case 'values':\n            if (Array.isArray((_a = component.data) === null || _a === void 0 ? void 0 : _a.values)) {\n                return mapStaticValues(component.data.values);\n            }\n            throw new error_1.ProcessorError(`Failed to validate available values in static values select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n        case 'json': {\n            if (typeof component.data.json === 'string') {\n                try {\n                    return mapDynamicValues(component, JSON.parse(component.data.json));\n                }\n                catch (err) {\n                    throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': ${err}`, context, 'validate:validateAvailableItems');\n                }\n            }\n            else if (Array.isArray(component.data.json)) {\n                // TODO: need to retype this\n                return mapDynamicValues(component, component.data.json);\n            }\n            else {\n                throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n            }\n        }\n        case 'custom': {\n            const customItems = (0, utils_1.evaluate)(component.data.custom, {\n                values: [],\n            }, 'values');\n            if (Array.isArray(customItems)) {\n                return customItems;\n            }\n            else {\n                throw new error_1.ProcessorError(`Failed to validate available values in JSON select component '${component.key}': the values are not an array`, context, 'validate:validateAvailableItems');\n            }\n        }\n        case 'url':\n            return null;\n        default:\n            throw new error_1.ProcessorError(`Failed to validate available values in select component '${component.key}': data source ${component.dataSrc} is not valid}`, context, 'validate:validateAvailableItems');\n    }\n}\nfunction compareComplexValues(valueA, valueB, context) {\n    if (!(0, util_1.isObject)(valueA) || !(0, util_1.isObject)(valueB)) {\n        return false;\n    }\n    try {\n        // TODO: we need to have normalized values here at this moment, otherwise\n        // this won't work\n        return JSON.stringify(valueA) === JSON.stringify(valueB);\n    }\n    catch (err) {\n        throw new error_1.ProcessorError(`Error while comparing available values: ${err}`, context, 'validate:validateAvailableItems');\n    }\n}\nconst validateAvailableItems = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    const { component, value } = context;\n    const error = new error_1.FieldError('invalidOption', context, 'onlyAvailableItems');\n    try {\n        if (isValidatableRadioComponent(component)) {\n            if (value == null || (0, lodash_1.isEmpty)(value)) {\n                return null;\n            }\n            const values = component.dataSrc === 'url'\n                ? yield getAvailableDynamicValues(component, context)\n                : component.values;\n            if (values) {\n                if ((0, util_1.isObject)(value)) {\n                    return values.find((optionValue) => compareComplexValues(optionValue, value, context)) !==\n                        undefined\n                        ? null\n                        : error;\n                }\n                return values.find((optionValue) => optionValue.value === value || optionValue === value) !== undefined\n                    ? null\n                    : error;\n            }\n            return null;\n        }\n        else if (isValidateableSelectComponent(component)) {\n            if (value == null || (0, lodash_1.isEmpty)(value)) {\n                return null;\n            }\n            const values = yield getAvailableSelectValues(component, context);\n            if (values) {\n                if ((0, util_1.isObject)(value)) {\n                    return values.find((optionValue) => compareComplexValues(optionValue, value, context)) !==\n                        undefined\n                        ? null\n                        : error;\n                }\n                return values.find((optionValue) => optionValue === value) !== undefined ? null : error;\n            }\n        }\n        else if (isValidateableSelectBoxesComponent(component)) {\n            if (value == null || (0, lodash_1.isEmpty)(value) || !(0, util_1.isObject)(value)) {\n                return null;\n            }\n            const values = component.dataSrc === 'url'\n                ? yield getAvailableDynamicValues(component, context)\n                : component.values.map((val) => val.value);\n            if (values) {\n                return (0, lodash_1.difference)(Object.keys(value), values).length ? error : null;\n            }\n        }\n    }\n    catch (err) {\n        throw new error_1.ProcessorError(err.message || err, context, 'validate:validateAvailableItems');\n    }\n    return null;\n});\nexports.validateAvailableItems = validateAvailableItems;\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (value == null || (0, lodash_1.isEmpty)(value)) {\n        return false;\n    }\n    if (isValidatableRadioComponent(component)) {\n        return true;\n    }\n    if (isValidateableSelectComponent(component)) {\n        return true;\n    }\n    if (isValidateableSelectBoxesComponent(component)) {\n        return true;\n    }\n    return false;\n};\nexports.shouldValidate = shouldValidate;\nconst validateAvailableItemsSync = (context) => {\n    const { component, value } = context;\n    const error = new error_1.FieldError('invalidOption', context, 'onlyAvailableItems');\n    try {\n        if (!(0, exports.shouldValidate)(context)) {\n            return null;\n        }\n        if (isValidatableRadioComponent(component) && component.dataSrc !== 'url') {\n            const values = component.values;\n            if (values) {\n                return values.findIndex(({ value: optionValue }) => optionValue === value) !== -1\n                    ? null\n                    : error;\n            }\n            return null;\n        }\n        else if (isValidateableSelectComponent(component)) {\n            const values = getAvailableSelectValuesSync(component, context);\n            if (values) {\n                if ((0, util_1.isObject)(value)) {\n                    return values.find((optionValue) => compareComplexValues(optionValue, value, context)) !==\n                        undefined\n                        ? null\n                        : error;\n                }\n                return values.find((optionValue) => optionValue === value) !== undefined ? null : error;\n            }\n        }\n        else if (isValidateableSelectBoxesComponent(component) && component.dataSrc !== 'url') {\n            if (value == null || (0, lodash_1.isEmpty)(value) || !(0, util_1.isObject)(value)) {\n                return null;\n            }\n            const values = component.values.map((val) => val.value);\n            if (values) {\n                return (0, lodash_1.difference)(Object.keys(value), values).length ? error : null;\n            }\n        }\n    }\n    catch (err) {\n        throw new error_1.ProcessorError(err.message || err, context, 'validate:validateAvailableItems');\n    }\n    return null;\n};\nexports.validateAvailableItemsSync = validateAvailableItemsSync;\nexports.validateAvailableItemsInfo = {\n    name: 'validateAvailableItems',\n    process: exports.validateAvailableItems,\n    processSync: exports.validateAvailableItemsSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,0BAA0B,GAAGD,OAAO,CAACE,0BAA0B,GAAGF,OAAO,CAACG,cAAc,GAAGH,OAAO,CAACI,sBAAsB,GAAG,KAAK,CAAC;AAC1I,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,OAAO,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAME,OAAO,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMG,MAAM,GAAGH,OAAO,CAAC,SAAS,CAAC;AACjC,MAAMI,OAAO,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AAC/C,SAASK,2BAA2BA,CAACC,SAAS,EAAE;EAC5C,IAAIC,EAAE;EACN,OAAOD,SAAS,IAAIA,SAAS,CAACE,IAAI,KAAK,OAAO,IAAI,CAAC,EAAE,CAACD,EAAE,GAAGD,SAAS,CAACG,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,kBAAkB,CAAC;AAC9I;AACA,SAASC,6BAA6BA,CAACL,SAAS,EAAE;EAC9C,IAAIC,EAAE;EACN,OAAQD,SAAS,IACb,CAAC,EAAE,CAACC,EAAE,GAAGD,SAAS,CAACG,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,kBAAkB,CAAC,IACxFJ,SAAS,CAACE,IAAI,KAAK,QAAQ,IAC3BF,SAAS,CAACM,OAAO,KAAK,UAAU;AACxC;AACA,SAASC,kCAAkCA,CAACP,SAAS,EAAE;EACnD,IAAIC,EAAE;EACN,OAAOD,SAAS,IAAI,CAAC,EAAE,CAACC,EAAE,GAAGD,SAAS,CAACG,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,kBAAkB,CAAC,IAAIJ,SAAS,CAACE,IAAI,KAAK,aAAa;AACpJ;AACA,SAASM,gBAAgBA,CAACR,SAAS,EAAES,MAAM,EAAE;EACzC,OAAOA,MAAM,CAACC,GAAG,CAAErC,KAAK,IAAK;IACzB,IAAI2B,SAAS,CAACW,aAAa,EAAE;MACzB,OAAOtC,KAAK,CAAC2B,SAAS,CAACW,aAAa,CAAC;IACzC;IACA,OAAOtC,KAAK;EAChB,CAAC,CAAC;AACN;AACA,SAASuC,eAAeA,CAACH,MAAM,EAAE;EAC7B,OAAOA,MAAM,CAACC,GAAG,CAAEG,GAAG,IAAKA,GAAG,CAACxC,KAAK,CAAC;AACzC;AACA,MAAMyC,yBAAyB,GAAGA,CAACd,SAAS,EAAEe,OAAO,KAAKhD,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACrG,IAAIiD,MAAM,GAAG,IAAI;EACjB,IAAI;IACAA,MAAM,GAAGD,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACE,KAAK,GAAGA,KAAK;EAClD,CAAC,CACD,OAAOC,SAAS,EAAE;IACdF,MAAM,GAAG,IAAI;EACjB;EACA,IAAI;IACA,IAAI,CAACA,MAAM,EAAE;MACTG,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,OAAO,IAAI;IACf;IACA,MAAMC,QAAQ,GAAG,MAAML,MAAM,CAAChB,SAAS,CAACsB,IAAI,CAACC,GAAG,EAAE;MAAEC,MAAM,EAAE;IAAM,CAAC,CAAC;IACpE,MAAMF,IAAI,GAAG,MAAMD,QAAQ,CAACI,IAAI,CAAC,CAAC;IAClC,OAAOH,IAAI,GAAGd,gBAAgB,CAACR,SAAS,EAAEsB,IAAI,CAAC,GAAG,IAAI;EAC1D,CAAC,CACD,OAAOI,GAAG,EAAE;IACRP,OAAO,CAACQ,KAAK,CAAC,CAAC,CAAC,EAAE7B,OAAO,CAAC8B,eAAe,EAAEF,GAAG,CAAC,CAAC;IAChD,OAAO,IAAI;EACf;AACJ,CAAC,CAAC;AACF,SAASG,wBAAwBA,CAAC7B,SAAS,EAAEe,OAAO,EAAE;EAClD,OAAOhD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAChD,IAAI,CAAC,CAAC,EAAE0B,QAAQ,CAACqC,WAAW,EAAE9B,SAAS,CAACM,OAAO,CAAC,IAAIN,SAAS,CAACsB,IAAI,CAACS,cAAc,CAAC,QAAQ,CAAC,EAAE;MACzF/B,SAAS,CAACM,OAAO,GAAG,QAAQ;IAChC;IACA,QAAQN,SAAS,CAACM,OAAO;MACrB,KAAK,QAAQ;QACT,IAAI0B,KAAK,CAACC,OAAO,CAACjC,SAAS,CAACsB,IAAI,CAACb,MAAM,CAAC,EAAE;UACtC,OAAOG,eAAe,CAACZ,SAAS,CAACsB,IAAI,CAACb,MAAM,CAAC;QACjD;QACA,MAAM,IAAId,OAAO,CAACuC,cAAc,CAAC,0EAA0ElC,SAAS,CAACmC,GAAG,gCAAgC,EAAEpB,OAAO,EAAE,iCAAiC,CAAC;MACzM,KAAK,MAAM;QAAE;UACT,IAAI,OAAOf,SAAS,CAACsB,IAAI,CAACG,IAAI,KAAK,QAAQ,EAAE;YACzC,IAAI;cACA,OAAOjB,gBAAgB,CAACR,SAAS,EAAEoC,IAAI,CAACC,KAAK,CAACrC,SAAS,CAACsB,IAAI,CAACG,IAAI,CAAC,CAAC;YACvE,CAAC,CACD,OAAOC,GAAG,EAAE;cACR,MAAM,IAAI/B,OAAO,CAACuC,cAAc,CAAC,iEAAiElC,SAAS,CAACmC,GAAG,MAAMT,GAAG,EAAE,EAAEX,OAAO,EAAE,iCAAiC,CAAC;YAC3K;UACJ,CAAC,MACI,IAAIiB,KAAK,CAACC,OAAO,CAACjC,SAAS,CAACsB,IAAI,CAACG,IAAI,CAAC,EAAE;YACzC;YACA,OAAOjB,gBAAgB,CAACR,SAAS,EAAEA,SAAS,CAACsB,IAAI,CAACG,IAAI,CAAC;UAC3D,CAAC,MACI;YACD,MAAM,IAAI9B,OAAO,CAACuC,cAAc,CAAC,iEAAiElC,SAAS,CAACmC,GAAG,gCAAgC,EAAEpB,OAAO,EAAE,iCAAiC,CAAC;UAChM;QACJ;MACA,KAAK,QAAQ;QAAE;UACX,MAAMuB,WAAW,GAAG,CAAC,CAAC,EAAE1C,OAAO,CAAC2C,QAAQ,EAAEvC,SAAS,CAACsB,IAAI,CAACkB,MAAM,EAAE;YAC7D/B,MAAM,EAAE;UACZ,CAAC,EAAE,QAAQ,CAAC;UACZ,IAAI,CAAC,CAAC,EAAEZ,MAAM,CAAC4C,SAAS,EAAEH,WAAW,CAAC,EAAE;YACpC,MAAMI,mBAAmB,GAAG,MAAMJ,WAAW;YAC7C,IAAIN,KAAK,CAACC,OAAO,CAACS,mBAAmB,CAAC,EAAE;cACpC,OAAOA,mBAAmB;YAC9B;YACA,MAAM,IAAI/C,OAAO,CAACuC,cAAc,CAAC,iEAAiElC,SAAS,CAACmC,GAAG,gCAAgC,EAAEpB,OAAO,EAAE,iCAAiC,CAAC;UAChM;UACA,IAAIiB,KAAK,CAACC,OAAO,CAACK,WAAW,CAAC,EAAE;YAC5B,OAAOA,WAAW;UACtB,CAAC,MACI;YACD,MAAM,IAAI3C,OAAO,CAACuC,cAAc,CAAC,iEAAiElC,SAAS,CAACmC,GAAG,gCAAgC,EAAEpB,OAAO,EAAE,iCAAiC,CAAC;UAChM;QACJ;MACA,KAAK,KAAK;QACN,OAAO,MAAMD,yBAAyB,CAACd,SAAS,EAAEe,OAAO,CAAC;MAC9D;QACI,MAAM,IAAIpB,OAAO,CAACuC,cAAc,CAAC,4DAA4DlC,SAAS,CAACmC,GAAG,kBAAkBnC,SAAS,CAACM,OAAO,gBAAgB,EAAES,OAAO,EAAE,iCAAiC,CAAC;IAClN;EACJ,CAAC,CAAC;AACN;AACA,SAAS4B,4BAA4BA,CAAC3C,SAAS,EAAEe,OAAO,EAAE;EACtD,IAAId,EAAE;EACN,IAAI,CAAC,CAAC,EAAER,QAAQ,CAACqC,WAAW,EAAE9B,SAAS,CAACM,OAAO,CAAC,IAAIN,SAAS,CAACsB,IAAI,CAACS,cAAc,CAAC,QAAQ,CAAC,EAAE;IACzF/B,SAAS,CAACM,OAAO,GAAG,QAAQ;EAChC;EACA,QAAQN,SAAS,CAACM,OAAO;IACrB,KAAK,QAAQ;MACT,IAAI0B,KAAK,CAACC,OAAO,CAAC,CAAChC,EAAE,GAAGD,SAAS,CAACsB,IAAI,MAAM,IAAI,IAAIrB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,MAAM,CAAC,EAAE;QACrF,OAAOG,eAAe,CAACZ,SAAS,CAACsB,IAAI,CAACb,MAAM,CAAC;MACjD;MACA,MAAM,IAAId,OAAO,CAACuC,cAAc,CAAC,0EAA0ElC,SAAS,CAACmC,GAAG,gCAAgC,EAAEpB,OAAO,EAAE,iCAAiC,CAAC;IACzM,KAAK,MAAM;MAAE;QACT,IAAI,OAAOf,SAAS,CAACsB,IAAI,CAACG,IAAI,KAAK,QAAQ,EAAE;UACzC,IAAI;YACA,OAAOjB,gBAAgB,CAACR,SAAS,EAAEoC,IAAI,CAACC,KAAK,CAACrC,SAAS,CAACsB,IAAI,CAACG,IAAI,CAAC,CAAC;UACvE,CAAC,CACD,OAAOC,GAAG,EAAE;YACR,MAAM,IAAI/B,OAAO,CAACuC,cAAc,CAAC,iEAAiElC,SAAS,CAACmC,GAAG,MAAMT,GAAG,EAAE,EAAEX,OAAO,EAAE,iCAAiC,CAAC;UAC3K;QACJ,CAAC,MACI,IAAIiB,KAAK,CAACC,OAAO,CAACjC,SAAS,CAACsB,IAAI,CAACG,IAAI,CAAC,EAAE;UACzC;UACA,OAAOjB,gBAAgB,CAACR,SAAS,EAAEA,SAAS,CAACsB,IAAI,CAACG,IAAI,CAAC;QAC3D,CAAC,MACI;UACD,MAAM,IAAI9B,OAAO,CAACuC,cAAc,CAAC,iEAAiElC,SAAS,CAACmC,GAAG,gCAAgC,EAAEpB,OAAO,EAAE,iCAAiC,CAAC;QAChM;MACJ;IACA,KAAK,QAAQ;MAAE;QACX,MAAMuB,WAAW,GAAG,CAAC,CAAC,EAAE1C,OAAO,CAAC2C,QAAQ,EAAEvC,SAAS,CAACsB,IAAI,CAACkB,MAAM,EAAE;UAC7D/B,MAAM,EAAE;QACZ,CAAC,EAAE,QAAQ,CAAC;QACZ,IAAIuB,KAAK,CAACC,OAAO,CAACK,WAAW,CAAC,EAAE;UAC5B,OAAOA,WAAW;QACtB,CAAC,MACI;UACD,MAAM,IAAI3C,OAAO,CAACuC,cAAc,CAAC,iEAAiElC,SAAS,CAACmC,GAAG,gCAAgC,EAAEpB,OAAO,EAAE,iCAAiC,CAAC;QAChM;MACJ;IACA,KAAK,KAAK;MACN,OAAO,IAAI;IACf;MACI,MAAM,IAAIpB,OAAO,CAACuC,cAAc,CAAC,4DAA4DlC,SAAS,CAACmC,GAAG,kBAAkBnC,SAAS,CAACM,OAAO,gBAAgB,EAAES,OAAO,EAAE,iCAAiC,CAAC;EAClN;AACJ;AACA,SAAS6B,oBAAoBA,CAACC,MAAM,EAAEC,MAAM,EAAE/B,OAAO,EAAE;EACnD,IAAI,CAAC,CAAC,CAAC,EAAElB,MAAM,CAACkD,QAAQ,EAAEF,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEhD,MAAM,CAACkD,QAAQ,EAAED,MAAM,CAAC,EAAE;IAChE,OAAO,KAAK;EAChB;EACA,IAAI;IACA;IACA;IACA,OAAOV,IAAI,CAACY,SAAS,CAACH,MAAM,CAAC,KAAKT,IAAI,CAACY,SAAS,CAACF,MAAM,CAAC;EAC5D,CAAC,CACD,OAAOpB,GAAG,EAAE;IACR,MAAM,IAAI/B,OAAO,CAACuC,cAAc,CAAC,2CAA2CR,GAAG,EAAE,EAAEX,OAAO,EAAE,iCAAiC,CAAC;EAClI;AACJ;AACA,MAAMvB,sBAAsB,GAAIuB,OAAO,IAAKhD,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACvF,MAAM;IAAEiC,SAAS;IAAE3B;EAAM,CAAC,GAAG0C,OAAO;EACpC,MAAMY,KAAK,GAAG,IAAIhC,OAAO,CAACsD,UAAU,CAAC,eAAe,EAAElC,OAAO,EAAE,oBAAoB,CAAC;EACpF,IAAI;IACA,IAAIhB,2BAA2B,CAACC,SAAS,CAAC,EAAE;MACxC,IAAI3B,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,EAAEoB,QAAQ,CAACyD,OAAO,EAAE7E,KAAK,CAAC,EAAE;QAC/C,OAAO,IAAI;MACf;MACA,MAAMoC,MAAM,GAAGT,SAAS,CAACM,OAAO,KAAK,KAAK,GACpC,MAAMQ,yBAAyB,CAACd,SAAS,EAAEe,OAAO,CAAC,GACnDf,SAAS,CAACS,MAAM;MACtB,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC,CAAC,EAAEZ,MAAM,CAACkD,QAAQ,EAAE1E,KAAK,CAAC,EAAE;UAC7B,OAAOoC,MAAM,CAAC0C,IAAI,CAAEC,WAAW,IAAKR,oBAAoB,CAACQ,WAAW,EAAE/E,KAAK,EAAE0C,OAAO,CAAC,CAAC,KAClFsC,SAAS,GACP,IAAI,GACJ1B,KAAK;QACf;QACA,OAAOlB,MAAM,CAAC0C,IAAI,CAAEC,WAAW,IAAKA,WAAW,CAAC/E,KAAK,KAAKA,KAAK,IAAI+E,WAAW,KAAK/E,KAAK,CAAC,KAAKgF,SAAS,GACjG,IAAI,GACJ1B,KAAK;MACf;MACA,OAAO,IAAI;IACf,CAAC,MACI,IAAItB,6BAA6B,CAACL,SAAS,CAAC,EAAE;MAC/C,IAAI3B,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,EAAEoB,QAAQ,CAACyD,OAAO,EAAE7E,KAAK,CAAC,EAAE;QAC/C,OAAO,IAAI;MACf;MACA,MAAMoC,MAAM,GAAG,MAAMoB,wBAAwB,CAAC7B,SAAS,EAAEe,OAAO,CAAC;MACjE,IAAIN,MAAM,EAAE;QACR,IAAI,CAAC,CAAC,EAAEZ,MAAM,CAACkD,QAAQ,EAAE1E,KAAK,CAAC,EAAE;UAC7B,OAAOoC,MAAM,CAAC0C,IAAI,CAAEC,WAAW,IAAKR,oBAAoB,CAACQ,WAAW,EAAE/E,KAAK,EAAE0C,OAAO,CAAC,CAAC,KAClFsC,SAAS,GACP,IAAI,GACJ1B,KAAK;QACf;QACA,OAAOlB,MAAM,CAAC0C,IAAI,CAAEC,WAAW,IAAKA,WAAW,KAAK/E,KAAK,CAAC,KAAKgF,SAAS,GAAG,IAAI,GAAG1B,KAAK;MAC3F;IACJ,CAAC,MACI,IAAIpB,kCAAkC,CAACP,SAAS,CAAC,EAAE;MACpD,IAAI3B,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,EAAEoB,QAAQ,CAACyD,OAAO,EAAE7E,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEwB,MAAM,CAACkD,QAAQ,EAAE1E,KAAK,CAAC,EAAE;QAC/E,OAAO,IAAI;MACf;MACA,MAAMoC,MAAM,GAAGT,SAAS,CAACM,OAAO,KAAK,KAAK,GACpC,MAAMQ,yBAAyB,CAACd,SAAS,EAAEe,OAAO,CAAC,GACnDf,SAAS,CAACS,MAAM,CAACC,GAAG,CAAE4C,GAAG,IAAKA,GAAG,CAACjF,KAAK,CAAC;MAC9C,IAAIoC,MAAM,EAAE;QACR,OAAO,CAAC,CAAC,EAAEhB,QAAQ,CAAC8D,UAAU,EAAErE,MAAM,CAACsE,IAAI,CAACnF,KAAK,CAAC,EAAEoC,MAAM,CAAC,CAACgD,MAAM,GAAG9B,KAAK,GAAG,IAAI;MACrF;IACJ;EACJ,CAAC,CACD,OAAOD,GAAG,EAAE;IACR,MAAM,IAAI/B,OAAO,CAACuC,cAAc,CAACR,GAAG,CAACgC,OAAO,IAAIhC,GAAG,EAAEX,OAAO,EAAE,iCAAiC,CAAC;EACpG;EACA,OAAO,IAAI;AACf,CAAC,CAAC;AACF3B,OAAO,CAACI,sBAAsB,GAAGA,sBAAsB;AACvD,MAAMD,cAAc,GAAIwB,OAAO,IAAK;EAChC,MAAM;IAAEf,SAAS;IAAE3B;EAAM,CAAC,GAAG0C,OAAO;EACpC,IAAI1C,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,EAAEoB,QAAQ,CAACyD,OAAO,EAAE7E,KAAK,CAAC,EAAE;IAC/C,OAAO,KAAK;EAChB;EACA,IAAI0B,2BAA2B,CAACC,SAAS,CAAC,EAAE;IACxC,OAAO,IAAI;EACf;EACA,IAAIK,6BAA6B,CAACL,SAAS,CAAC,EAAE;IAC1C,OAAO,IAAI;EACf;EACA,IAAIO,kCAAkC,CAACP,SAAS,CAAC,EAAE;IAC/C,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACDZ,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvC,MAAMD,0BAA0B,GAAIyB,OAAO,IAAK;EAC5C,MAAM;IAAEf,SAAS;IAAE3B;EAAM,CAAC,GAAG0C,OAAO;EACpC,MAAMY,KAAK,GAAG,IAAIhC,OAAO,CAACsD,UAAU,CAAC,eAAe,EAAElC,OAAO,EAAE,oBAAoB,CAAC;EACpF,IAAI;IACA,IAAI,CAAC,CAAC,CAAC,EAAE3B,OAAO,CAACG,cAAc,EAAEwB,OAAO,CAAC,EAAE;MACvC,OAAO,IAAI;IACf;IACA,IAAIhB,2BAA2B,CAACC,SAAS,CAAC,IAAIA,SAAS,CAACM,OAAO,KAAK,KAAK,EAAE;MACvE,MAAMG,MAAM,GAAGT,SAAS,CAACS,MAAM;MAC/B,IAAIA,MAAM,EAAE;QACR,OAAOA,MAAM,CAACkD,SAAS,CAAC,CAAC;UAAEtF,KAAK,EAAE+E;QAAY,CAAC,KAAKA,WAAW,KAAK/E,KAAK,CAAC,KAAK,CAAC,CAAC,GAC3E,IAAI,GACJsD,KAAK;MACf;MACA,OAAO,IAAI;IACf,CAAC,MACI,IAAItB,6BAA6B,CAACL,SAAS,CAAC,EAAE;MAC/C,MAAMS,MAAM,GAAGkC,4BAA4B,CAAC3C,SAAS,EAAEe,OAAO,CAAC;MAC/D,IAAIN,MAAM,EAAE;QACR,IAAI,CAAC,CAAC,EAAEZ,MAAM,CAACkD,QAAQ,EAAE1E,KAAK,CAAC,EAAE;UAC7B,OAAOoC,MAAM,CAAC0C,IAAI,CAAEC,WAAW,IAAKR,oBAAoB,CAACQ,WAAW,EAAE/E,KAAK,EAAE0C,OAAO,CAAC,CAAC,KAClFsC,SAAS,GACP,IAAI,GACJ1B,KAAK;QACf;QACA,OAAOlB,MAAM,CAAC0C,IAAI,CAAEC,WAAW,IAAKA,WAAW,KAAK/E,KAAK,CAAC,KAAKgF,SAAS,GAAG,IAAI,GAAG1B,KAAK;MAC3F;IACJ,CAAC,MACI,IAAIpB,kCAAkC,CAACP,SAAS,CAAC,IAAIA,SAAS,CAACM,OAAO,KAAK,KAAK,EAAE;MACnF,IAAIjC,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,EAAEoB,QAAQ,CAACyD,OAAO,EAAE7E,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEwB,MAAM,CAACkD,QAAQ,EAAE1E,KAAK,CAAC,EAAE;QAC/E,OAAO,IAAI;MACf;MACA,MAAMoC,MAAM,GAAGT,SAAS,CAACS,MAAM,CAACC,GAAG,CAAE4C,GAAG,IAAKA,GAAG,CAACjF,KAAK,CAAC;MACvD,IAAIoC,MAAM,EAAE;QACR,OAAO,CAAC,CAAC,EAAEhB,QAAQ,CAAC8D,UAAU,EAAErE,MAAM,CAACsE,IAAI,CAACnF,KAAK,CAAC,EAAEoC,MAAM,CAAC,CAACgD,MAAM,GAAG9B,KAAK,GAAG,IAAI;MACrF;IACJ;EACJ,CAAC,CACD,OAAOD,GAAG,EAAE;IACR,MAAM,IAAI/B,OAAO,CAACuC,cAAc,CAACR,GAAG,CAACgC,OAAO,IAAIhC,GAAG,EAAEX,OAAO,EAAE,iCAAiC,CAAC;EACpG;EACA,OAAO,IAAI;AACf,CAAC;AACD3B,OAAO,CAACE,0BAA0B,GAAGA,0BAA0B;AAC/DF,OAAO,CAACC,0BAA0B,GAAG;EACjCuE,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,EAAEzE,OAAO,CAACI,sBAAsB;EACvCsE,WAAW,EAAE1E,OAAO,CAACE,0BAA0B;EAC/CyE,aAAa,EAAE3E,OAAO,CAACG;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}