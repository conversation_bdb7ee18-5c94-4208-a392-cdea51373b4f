{"ast": null, "code": "/*!\n * https://github.com/Starcounter-Jack/JSON-Patch\n * (c) 2017-2022 <PERSON>\n * MIT licensed\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar _hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function hasOwnProperty(obj, key) {\n  return _hasOwnProperty.call(obj, key);\n}\nexport function _objectKeys(obj) {\n  if (Array.isArray(obj)) {\n    var keys_1 = new Array(obj.length);\n    for (var k = 0; k < keys_1.length; k++) {\n      keys_1[k] = \"\" + k;\n    }\n    return keys_1;\n  }\n  if (Object.keys) {\n    return Object.keys(obj);\n  }\n  var keys = [];\n  for (var i in obj) {\n    if (hasOwnProperty(obj, i)) {\n      keys.push(i);\n    }\n  }\n  return keys;\n}\n;\n/**\n* Deeply clone the object.\n* https://jsperf.com/deep-copy-vs-json-stringify-json-parse/25 (recursiveDeepCopy)\n* @param  {any} obj value to clone\n* @return {any} cloned obj\n*/\nexport function _deepClone(obj) {\n  switch (typeof obj) {\n    case \"object\":\n      return JSON.parse(JSON.stringify(obj));\n    //Faster than ES5 clone - http://jsperf.com/deep-cloning-of-objects/5\n    case \"undefined\":\n      return null;\n    //this is how JSON.stringify behaves for array items\n    default:\n      return obj;\n    //no need to clone primitives\n  }\n}\n//3x faster than cached /^\\d+$/.test(str)\nexport function isInteger(str) {\n  var i = 0;\n  var len = str.length;\n  var charCode;\n  while (i < len) {\n    charCode = str.charCodeAt(i);\n    if (charCode >= 48 && charCode <= 57) {\n      i++;\n      continue;\n    }\n    return false;\n  }\n  return true;\n}\n/**\n* Escapes a json pointer path\n* @param path The raw pointer\n* @return the Escaped path\n*/\nexport function escapePathComponent(path) {\n  if (path.indexOf('/') === -1 && path.indexOf('~') === -1) return path;\n  return path.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n/**\n * Unescapes a json pointer path\n * @param path The escaped pointer\n * @return The unescaped path\n */\nexport function unescapePathComponent(path) {\n  return path.replace(/~1/g, '/').replace(/~0/g, '~');\n}\nexport function _getPathRecursive(root, obj) {\n  var found;\n  for (var key in root) {\n    if (hasOwnProperty(root, key)) {\n      if (root[key] === obj) {\n        return escapePathComponent(key) + '/';\n      } else if (typeof root[key] === 'object') {\n        found = _getPathRecursive(root[key], obj);\n        if (found != '') {\n          return escapePathComponent(key) + '/' + found;\n        }\n      }\n    }\n  }\n  return '';\n}\nexport function getPath(root, obj) {\n  if (root === obj) {\n    return '/';\n  }\n  var path = _getPathRecursive(root, obj);\n  if (path === '') {\n    throw new Error(\"Object not found in root\");\n  }\n  return \"/\" + path;\n}\n/**\n* Recursively checks whether an object has any undefined values inside.\n*/\nexport function hasUndefined(obj) {\n  if (obj === undefined) {\n    return true;\n  }\n  if (obj) {\n    if (Array.isArray(obj)) {\n      for (var i_1 = 0, len = obj.length; i_1 < len; i_1++) {\n        if (hasUndefined(obj[i_1])) {\n          return true;\n        }\n      }\n    } else if (typeof obj === \"object\") {\n      var objKeys = _objectKeys(obj);\n      var objKeysLength = objKeys.length;\n      for (var i = 0; i < objKeysLength; i++) {\n        if (hasUndefined(obj[objKeys[i]])) {\n          return true;\n        }\n      }\n    }\n  }\n  return false;\n}\nfunction patchErrorMessageFormatter(message, args) {\n  var messageParts = [message];\n  for (var key in args) {\n    var value = typeof args[key] === 'object' ? JSON.stringify(args[key], null, 2) : args[key]; // pretty print\n    if (typeof value !== 'undefined') {\n      messageParts.push(key + \": \" + value);\n    }\n  }\n  return messageParts.join('\\n');\n}\nvar PatchError = /** @class */function (_super) {\n  __extends(PatchError, _super);\n  function PatchError(message, name, index, operation, tree) {\n    var _newTarget = this.constructor;\n    var _this = _super.call(this, patchErrorMessageFormatter(message, {\n      name: name,\n      index: index,\n      operation: operation,\n      tree: tree\n    })) || this;\n    _this.name = name;\n    _this.index = index;\n    _this.operation = operation;\n    _this.tree = tree;\n    Object.setPrototypeOf(_this, _newTarget.prototype); // restore prototype chain, see https://stackoverflow.com/a/48342359\n    _this.message = patchErrorMessageFormatter(message, {\n      name: name,\n      index: index,\n      operation: operation,\n      tree: tree\n    });\n    return _this;\n  }\n  return PatchError;\n}(Error);\nexport { PatchError };", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "_hasOwnProperty", "obj", "key", "call", "_objectKeys", "isArray", "keys_1", "length", "k", "keys", "i", "push", "_deepClone", "JSON", "parse", "stringify", "isInteger", "str", "len", "charCode", "charCodeAt", "escapePathComponent", "path", "indexOf", "replace", "unescapePathComponent", "_getPathRecursive", "root", "found", "<PERSON><PERSON><PERSON>", "Error", "hasUndefined", "undefined", "i_1", "ob<PERSON><PERSON><PERSON><PERSON>", "obj<PERSON><PERSON>s<PERSON><PERSON><PERSON>", "patchErrorMessageFormatter", "message", "args", "messageParts", "value", "join", "Patch<PERSON><PERSON>r", "_super", "name", "index", "operation", "tree", "_newTarget", "_this"], "sources": ["D:/workspace/formtest_aug/node_modules/fast-json-patch/module/helpers.mjs"], "sourcesContent": ["/*!\n * https://github.com/Starcounter-Jack/JSON-Patch\n * (c) 2017-2022 <PERSON>\n * MIT licensed\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar _hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function hasOwnProperty(obj, key) {\n    return _hasOwnProperty.call(obj, key);\n}\nexport function _objectKeys(obj) {\n    if (Array.isArray(obj)) {\n        var keys_1 = new Array(obj.length);\n        for (var k = 0; k < keys_1.length; k++) {\n            keys_1[k] = \"\" + k;\n        }\n        return keys_1;\n    }\n    if (Object.keys) {\n        return Object.keys(obj);\n    }\n    var keys = [];\n    for (var i in obj) {\n        if (hasOwnProperty(obj, i)) {\n            keys.push(i);\n        }\n    }\n    return keys;\n}\n;\n/**\n* Deeply clone the object.\n* https://jsperf.com/deep-copy-vs-json-stringify-json-parse/25 (recursiveDeepCopy)\n* @param  {any} obj value to clone\n* @return {any} cloned obj\n*/\nexport function _deepClone(obj) {\n    switch (typeof obj) {\n        case \"object\":\n            return JSON.parse(JSON.stringify(obj)); //Faster than ES5 clone - http://jsperf.com/deep-cloning-of-objects/5\n        case \"undefined\":\n            return null; //this is how JSON.stringify behaves for array items\n        default:\n            return obj; //no need to clone primitives\n    }\n}\n//3x faster than cached /^\\d+$/.test(str)\nexport function isInteger(str) {\n    var i = 0;\n    var len = str.length;\n    var charCode;\n    while (i < len) {\n        charCode = str.charCodeAt(i);\n        if (charCode >= 48 && charCode <= 57) {\n            i++;\n            continue;\n        }\n        return false;\n    }\n    return true;\n}\n/**\n* Escapes a json pointer path\n* @param path The raw pointer\n* @return the Escaped path\n*/\nexport function escapePathComponent(path) {\n    if (path.indexOf('/') === -1 && path.indexOf('~') === -1)\n        return path;\n    return path.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n/**\n * Unescapes a json pointer path\n * @param path The escaped pointer\n * @return The unescaped path\n */\nexport function unescapePathComponent(path) {\n    return path.replace(/~1/g, '/').replace(/~0/g, '~');\n}\nexport function _getPathRecursive(root, obj) {\n    var found;\n    for (var key in root) {\n        if (hasOwnProperty(root, key)) {\n            if (root[key] === obj) {\n                return escapePathComponent(key) + '/';\n            }\n            else if (typeof root[key] === 'object') {\n                found = _getPathRecursive(root[key], obj);\n                if (found != '') {\n                    return escapePathComponent(key) + '/' + found;\n                }\n            }\n        }\n    }\n    return '';\n}\nexport function getPath(root, obj) {\n    if (root === obj) {\n        return '/';\n    }\n    var path = _getPathRecursive(root, obj);\n    if (path === '') {\n        throw new Error(\"Object not found in root\");\n    }\n    return \"/\" + path;\n}\n/**\n* Recursively checks whether an object has any undefined values inside.\n*/\nexport function hasUndefined(obj) {\n    if (obj === undefined) {\n        return true;\n    }\n    if (obj) {\n        if (Array.isArray(obj)) {\n            for (var i_1 = 0, len = obj.length; i_1 < len; i_1++) {\n                if (hasUndefined(obj[i_1])) {\n                    return true;\n                }\n            }\n        }\n        else if (typeof obj === \"object\") {\n            var objKeys = _objectKeys(obj);\n            var objKeysLength = objKeys.length;\n            for (var i = 0; i < objKeysLength; i++) {\n                if (hasUndefined(obj[objKeys[i]])) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}\nfunction patchErrorMessageFormatter(message, args) {\n    var messageParts = [message];\n    for (var key in args) {\n        var value = typeof args[key] === 'object' ? JSON.stringify(args[key], null, 2) : args[key]; // pretty print\n        if (typeof value !== 'undefined') {\n            messageParts.push(key + \": \" + value);\n        }\n    }\n    return messageParts.join('\\n');\n}\nvar PatchError = /** @class */ (function (_super) {\n    __extends(PatchError, _super);\n    function PatchError(message, name, index, operation, tree) {\n        var _newTarget = this.constructor;\n        var _this = _super.call(this, patchErrorMessageFormatter(message, { name: name, index: index, operation: operation, tree: tree })) || this;\n        _this.name = name;\n        _this.index = index;\n        _this.operation = operation;\n        _this.tree = tree;\n        Object.setPrototypeOf(_this, _newTarget.prototype); // restore prototype chain, see https://stackoverflow.com/a/48342359\n        _this.message = patchErrorMessageFormatter(message, { name: name, index: index, operation: operation, tree: tree });\n        return _this;\n    }\n    return PatchError;\n}(Error));\nexport { PatchError };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,eAAe,GAAGV,MAAM,CAACQ,SAAS,CAACH,cAAc;AACrD,OAAO,SAASA,cAAcA,CAACM,GAAG,EAAEC,GAAG,EAAE;EACrC,OAAOF,eAAe,CAACG,IAAI,CAACF,GAAG,EAAEC,GAAG,CAAC;AACzC;AACA,OAAO,SAASE,WAAWA,CAACH,GAAG,EAAE;EAC7B,IAAIR,KAAK,CAACY,OAAO,CAACJ,GAAG,CAAC,EAAE;IACpB,IAAIK,MAAM,GAAG,IAAIb,KAAK,CAACQ,GAAG,CAACM,MAAM,CAAC;IAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACC,MAAM,EAAEC,CAAC,EAAE,EAAE;MACpCF,MAAM,CAACE,CAAC,CAAC,GAAG,EAAE,GAAGA,CAAC;IACtB;IACA,OAAOF,MAAM;EACjB;EACA,IAAIhB,MAAM,CAACmB,IAAI,EAAE;IACb,OAAOnB,MAAM,CAACmB,IAAI,CAACR,GAAG,CAAC;EAC3B;EACA,IAAIQ,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,CAAC,IAAIT,GAAG,EAAE;IACf,IAAIN,cAAc,CAACM,GAAG,EAAES,CAAC,CAAC,EAAE;MACxBD,IAAI,CAACE,IAAI,CAACD,CAAC,CAAC;IAChB;EACJ;EACA,OAAOD,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,UAAUA,CAACX,GAAG,EAAE;EAC5B,QAAQ,OAAOA,GAAG;IACd,KAAK,QAAQ;MACT,OAAOY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACd,GAAG,CAAC,CAAC;IAAE;IAC5C,KAAK,WAAW;MACZ,OAAO,IAAI;IAAE;IACjB;MACI,OAAOA,GAAG;IAAE;EACpB;AACJ;AACA;AACA,OAAO,SAASe,SAASA,CAACC,GAAG,EAAE;EAC3B,IAAIP,CAAC,GAAG,CAAC;EACT,IAAIQ,GAAG,GAAGD,GAAG,CAACV,MAAM;EACpB,IAAIY,QAAQ;EACZ,OAAOT,CAAC,GAAGQ,GAAG,EAAE;IACZC,QAAQ,GAAGF,GAAG,CAACG,UAAU,CAACV,CAAC,CAAC;IAC5B,IAAIS,QAAQ,IAAI,EAAE,IAAIA,QAAQ,IAAI,EAAE,EAAE;MAClCT,CAAC,EAAE;MACH;IACJ;IACA,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,mBAAmBA,CAACC,IAAI,EAAE;EACtC,IAAIA,IAAI,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAID,IAAI,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EACpD,OAAOD,IAAI;EACf,OAAOA,IAAI,CAACE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACH,IAAI,EAAE;EACxC,OAAOA,IAAI,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACvD;AACA,OAAO,SAASE,iBAAiBA,CAACC,IAAI,EAAE1B,GAAG,EAAE;EACzC,IAAI2B,KAAK;EACT,KAAK,IAAI1B,GAAG,IAAIyB,IAAI,EAAE;IAClB,IAAIhC,cAAc,CAACgC,IAAI,EAAEzB,GAAG,CAAC,EAAE;MAC3B,IAAIyB,IAAI,CAACzB,GAAG,CAAC,KAAKD,GAAG,EAAE;QACnB,OAAOoB,mBAAmB,CAACnB,GAAG,CAAC,GAAG,GAAG;MACzC,CAAC,MACI,IAAI,OAAOyB,IAAI,CAACzB,GAAG,CAAC,KAAK,QAAQ,EAAE;QACpC0B,KAAK,GAAGF,iBAAiB,CAACC,IAAI,CAACzB,GAAG,CAAC,EAAED,GAAG,CAAC;QACzC,IAAI2B,KAAK,IAAI,EAAE,EAAE;UACb,OAAOP,mBAAmB,CAACnB,GAAG,CAAC,GAAG,GAAG,GAAG0B,KAAK;QACjD;MACJ;IACJ;EACJ;EACA,OAAO,EAAE;AACb;AACA,OAAO,SAASC,OAAOA,CAACF,IAAI,EAAE1B,GAAG,EAAE;EAC/B,IAAI0B,IAAI,KAAK1B,GAAG,EAAE;IACd,OAAO,GAAG;EACd;EACA,IAAIqB,IAAI,GAAGI,iBAAiB,CAACC,IAAI,EAAE1B,GAAG,CAAC;EACvC,IAAIqB,IAAI,KAAK,EAAE,EAAE;IACb,MAAM,IAAIQ,KAAK,CAAC,0BAA0B,CAAC;EAC/C;EACA,OAAO,GAAG,GAAGR,IAAI;AACrB;AACA;AACA;AACA;AACA,OAAO,SAASS,YAAYA,CAAC9B,GAAG,EAAE;EAC9B,IAAIA,GAAG,KAAK+B,SAAS,EAAE;IACnB,OAAO,IAAI;EACf;EACA,IAAI/B,GAAG,EAAE;IACL,IAAIR,KAAK,CAACY,OAAO,CAACJ,GAAG,CAAC,EAAE;MACpB,KAAK,IAAIgC,GAAG,GAAG,CAAC,EAAEf,GAAG,GAAGjB,GAAG,CAACM,MAAM,EAAE0B,GAAG,GAAGf,GAAG,EAAEe,GAAG,EAAE,EAAE;QAClD,IAAIF,YAAY,CAAC9B,GAAG,CAACgC,GAAG,CAAC,CAAC,EAAE;UACxB,OAAO,IAAI;QACf;MACJ;IACJ,CAAC,MACI,IAAI,OAAOhC,GAAG,KAAK,QAAQ,EAAE;MAC9B,IAAIiC,OAAO,GAAG9B,WAAW,CAACH,GAAG,CAAC;MAC9B,IAAIkC,aAAa,GAAGD,OAAO,CAAC3B,MAAM;MAClC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,aAAa,EAAEzB,CAAC,EAAE,EAAE;QACpC,IAAIqB,YAAY,CAAC9B,GAAG,CAACiC,OAAO,CAACxB,CAAC,CAAC,CAAC,CAAC,EAAE;UAC/B,OAAO,IAAI;QACf;MACJ;IACJ;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAAS0B,0BAA0BA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC/C,IAAIC,YAAY,GAAG,CAACF,OAAO,CAAC;EAC5B,KAAK,IAAInC,GAAG,IAAIoC,IAAI,EAAE;IAClB,IAAIE,KAAK,GAAG,OAAOF,IAAI,CAACpC,GAAG,CAAC,KAAK,QAAQ,GAAGW,IAAI,CAACE,SAAS,CAACuB,IAAI,CAACpC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGoC,IAAI,CAACpC,GAAG,CAAC,CAAC,CAAC;IAC5F,IAAI,OAAOsC,KAAK,KAAK,WAAW,EAAE;MAC9BD,YAAY,CAAC5B,IAAI,CAACT,GAAG,GAAG,IAAI,GAAGsC,KAAK,CAAC;IACzC;EACJ;EACA,OAAOD,YAAY,CAACE,IAAI,CAAC,IAAI,CAAC;AAClC;AACA,IAAIC,UAAU,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC9CzD,SAAS,CAACwD,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAACL,OAAO,EAAEO,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,IAAI,EAAE;IACvD,IAAIC,UAAU,GAAG,IAAI,CAACnD,WAAW;IACjC,IAAIoD,KAAK,GAAGN,MAAM,CAACxC,IAAI,CAAC,IAAI,EAAEiC,0BAA0B,CAACC,OAAO,EAAE;MAAEO,IAAI,EAAEA,IAAI;MAAEC,KAAK,EAAEA,KAAK;MAAEC,SAAS,EAAEA,SAAS;MAAEC,IAAI,EAAEA;IAAK,CAAC,CAAC,CAAC,IAAI,IAAI;IAC1IE,KAAK,CAACL,IAAI,GAAGA,IAAI;IACjBK,KAAK,CAACJ,KAAK,GAAGA,KAAK;IACnBI,KAAK,CAACH,SAAS,GAAGA,SAAS;IAC3BG,KAAK,CAACF,IAAI,GAAGA,IAAI;IACjBzD,MAAM,CAACC,cAAc,CAAC0D,KAAK,EAAED,UAAU,CAAClD,SAAS,CAAC,CAAC,CAAC;IACpDmD,KAAK,CAACZ,OAAO,GAAGD,0BAA0B,CAACC,OAAO,EAAE;MAAEO,IAAI,EAAEA,IAAI;MAAEC,KAAK,EAAEA,KAAK;MAAEC,SAAS,EAAEA,SAAS;MAAEC,IAAI,EAAEA;IAAK,CAAC,CAAC;IACnH,OAAOE,KAAK;EAChB;EACA,OAAOP,UAAU;AACrB,CAAC,CAACZ,KAAK,CAAE;AACT,SAASY,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}