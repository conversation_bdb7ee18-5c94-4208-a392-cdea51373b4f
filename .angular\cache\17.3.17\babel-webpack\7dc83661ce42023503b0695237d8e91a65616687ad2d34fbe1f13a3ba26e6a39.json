{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst ConditionOperator_1 = __importDefault(require(\"./ConditionOperator\"));\nconst moment_1 = __importDefault(require(\"moment\"));\nconst date_1 = require(\"../../utils/date\");\nclass DateGeaterThan extends ConditionOperator_1.default {\n  static get operatorKey() {\n    return 'dateGreaterThan';\n  }\n  static get displayedName() {\n    return 'Greater Than';\n  }\n  getFormattedDates({\n    value,\n    comparedValue,\n    conditionTriggerComponent\n  }) {\n    const validationFormat = conditionTriggerComponent && conditionTriggerComponent.component.type === 'day' ? (0, date_1.getDateValidationFormat)(conditionTriggerComponent.component) : null;\n    const date = validationFormat ? (0, moment_1.default)(value, validationFormat) : (0, moment_1.default)(value);\n    const comparedDate = validationFormat ? (0, moment_1.default)(comparedValue, validationFormat) : (0, moment_1.default)(comparedValue);\n    return {\n      date,\n      comparedDate\n    };\n  }\n  execute(options, functionName = 'isAfter') {\n    const {\n      value,\n      instance,\n      conditionComponentPath\n    } = options;\n    if (!value) {\n      return false;\n    }\n    let conditionTriggerComponent = null;\n    if (instance && instance.root) {\n      conditionTriggerComponent = instance.root.getComponent(conditionComponentPath);\n    }\n    if (conditionTriggerComponent && conditionTriggerComponent.component.type === 'day' && (0, date_1.isPartialDay)(conditionTriggerComponent.component, value)) {\n      return false;\n    }\n    const {\n      date,\n      comparedDate\n    } = this.getFormattedDates(Object.assign(Object.assign({}, options), {\n      conditionTriggerComponent\n    }));\n    return date[functionName](comparedDate);\n  }\n}\nexports.default = DateGeaterThan;", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "ConditionOperator_1", "require", "moment_1", "date_1", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "default", "operatorKey", "displayedName", "getFormattedDates", "comparedValue", "conditionTriggerComponent", "validationFormat", "component", "type", "getDateValidationFormat", "date", "comparedDate", "execute", "options", "functionName", "instance", "conditionComponentPath", "root", "getComponent", "isPartialDay", "assign"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/utils/operators/DateGreaterThan.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst ConditionOperator_1 = __importDefault(require(\"./ConditionOperator\"));\nconst moment_1 = __importDefault(require(\"moment\"));\nconst date_1 = require(\"../../utils/date\");\nclass DateGeaterThan extends ConditionOperator_1.default {\n    static get operatorKey() {\n        return 'dateGreaterThan';\n    }\n    static get displayedName() {\n        return 'Greater Than';\n    }\n    getFormattedDates({ value, comparedValue, conditionTriggerComponent }) {\n        const validationFormat = conditionTriggerComponent && conditionTriggerComponent.component.type === 'day'\n            ? (0, date_1.getDateValidationFormat)(conditionTriggerComponent.component)\n            : null;\n        const date = validationFormat ? (0, moment_1.default)(value, validationFormat) : (0, moment_1.default)(value);\n        const comparedDate = validationFormat\n            ? (0, moment_1.default)(comparedValue, validationFormat)\n            : (0, moment_1.default)(comparedValue);\n        return { date, comparedDate };\n    }\n    execute(options, functionName = 'isAfter') {\n        const { value, instance, conditionComponentPath } = options;\n        if (!value) {\n            return false;\n        }\n        let conditionTriggerComponent = null;\n        if (instance && instance.root) {\n            conditionTriggerComponent = instance.root.getComponent(conditionComponentPath);\n        }\n        if (conditionTriggerComponent &&\n            conditionTriggerComponent.component.type === 'day' &&\n            (0, date_1.isPartialDay)(conditionTriggerComponent.component, value)) {\n            return false;\n        }\n        const { date, comparedDate } = this.getFormattedDates(Object.assign(Object.assign({}, options), { conditionTriggerComponent }));\n        return date[functionName](comparedDate);\n    }\n}\nexports.default = DateGeaterThan;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,MAAMC,mBAAmB,GAAGP,eAAe,CAACQ,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC3E,MAAMC,QAAQ,GAAGT,eAAe,CAACQ,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnD,MAAME,MAAM,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAC1C,MAAMG,cAAc,SAASJ,mBAAmB,CAACK,OAAO,CAAC;EACrD,WAAWC,WAAWA,CAAA,EAAG;IACrB,OAAO,iBAAiB;EAC5B;EACA,WAAWC,aAAaA,CAAA,EAAG;IACvB,OAAO,cAAc;EACzB;EACAC,iBAAiBA,CAAC;IAAET,KAAK;IAAEU,aAAa;IAAEC;EAA0B,CAAC,EAAE;IACnE,MAAMC,gBAAgB,GAAGD,yBAAyB,IAAIA,yBAAyB,CAACE,SAAS,CAACC,IAAI,KAAK,KAAK,GAClG,CAAC,CAAC,EAAEV,MAAM,CAACW,uBAAuB,EAAEJ,yBAAyB,CAACE,SAAS,CAAC,GACxE,IAAI;IACV,MAAMG,IAAI,GAAGJ,gBAAgB,GAAG,CAAC,CAAC,EAAET,QAAQ,CAACG,OAAO,EAAEN,KAAK,EAAEY,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAET,QAAQ,CAACG,OAAO,EAAEN,KAAK,CAAC;IAC7G,MAAMiB,YAAY,GAAGL,gBAAgB,GAC/B,CAAC,CAAC,EAAET,QAAQ,CAACG,OAAO,EAAEI,aAAa,EAAEE,gBAAgB,CAAC,GACtD,CAAC,CAAC,EAAET,QAAQ,CAACG,OAAO,EAAEI,aAAa,CAAC;IAC1C,OAAO;MAAEM,IAAI;MAAEC;IAAa,CAAC;EACjC;EACAC,OAAOA,CAACC,OAAO,EAAEC,YAAY,GAAG,SAAS,EAAE;IACvC,MAAM;MAAEpB,KAAK;MAAEqB,QAAQ;MAAEC;IAAuB,CAAC,GAAGH,OAAO;IAC3D,IAAI,CAACnB,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;IACA,IAAIW,yBAAyB,GAAG,IAAI;IACpC,IAAIU,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;MAC3BZ,yBAAyB,GAAGU,QAAQ,CAACE,IAAI,CAACC,YAAY,CAACF,sBAAsB,CAAC;IAClF;IACA,IAAIX,yBAAyB,IACzBA,yBAAyB,CAACE,SAAS,CAACC,IAAI,KAAK,KAAK,IAClD,CAAC,CAAC,EAAEV,MAAM,CAACqB,YAAY,EAAEd,yBAAyB,CAACE,SAAS,EAAEb,KAAK,CAAC,EAAE;MACtE,OAAO,KAAK;IAChB;IACA,MAAM;MAAEgB,IAAI;MAAEC;IAAa,CAAC,GAAG,IAAI,CAACR,iBAAiB,CAACZ,MAAM,CAAC6B,MAAM,CAAC7B,MAAM,CAAC6B,MAAM,CAAC,CAAC,CAAC,EAAEP,OAAO,CAAC,EAAE;MAAER;IAA0B,CAAC,CAAC,CAAC;IAC/H,OAAOK,IAAI,CAACI,YAAY,CAAC,CAACH,YAAY,CAAC;EAC3C;AACJ;AACAlB,OAAO,CAACO,OAAO,GAAGD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}