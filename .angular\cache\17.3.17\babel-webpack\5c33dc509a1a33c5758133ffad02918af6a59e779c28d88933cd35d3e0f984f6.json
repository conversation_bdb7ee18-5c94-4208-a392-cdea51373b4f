{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateUrlInfo = exports.validateUrl = exports.validateUrlSync = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isUrlComponent = component => {\n  return component && component.type === 'url';\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!isUrlComponent(component)) {\n    return false;\n  }\n  if (component.multiple && Array.isArray(value) && value.length === 0) {\n    return false;\n  }\n  if (!value) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateUrlSync = context => {\n  const {\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  const error = new error_1.FieldError('invalid_url', context, 'url');\n  if (typeof value !== 'string') {\n    return error;\n  }\n  // From https://stackoverflow.com/questions/8667070/javascript-regular-expression-to-validate-url\n  const re = /^(?:(?:(?:https?|ftp):)?\\/\\/)?(?:\\S+(?::\\S*)?@)?(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))(?::\\d{2,5})?(?:[/?#]\\S*)?$/i;\n  // From http://stackoverflow.com/questions/46155/validate-email-address-in-javascript\n  const emailRe = /^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\n  // Allow urls to be valid if the component is pristine and no value is provided.\n  return re.test(value) && !emailRe.test(value) ? null : error;\n};\nexports.validateUrlSync = validateUrlSync;\nconst validateUrl = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateUrlSync)(context);\n});\nexports.validateUrl = validateUrl;\nexports.validateUrlInfo = {\n  name: 'validateUrl',\n  process: exports.validateUrl,\n  processSync: exports.validateUrlSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateUrlInfo", "validateUrl", "validateUrlSync", "shouldValidate", "error_1", "require", "isUrlComponent", "component", "type", "context", "multiple", "Array", "isArray", "length", "error", "FieldError", "re", "emailRe", "test", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateUrl.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateUrlInfo = exports.validateUrl = exports.validateUrlSync = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isUrlComponent = (component) => {\n    return component && component.type === 'url';\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!isUrlComponent(component)) {\n        return false;\n    }\n    if (component.multiple && Array.isArray(value) && value.length === 0) {\n        return false;\n    }\n    if (!value) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateUrlSync = (context) => {\n    const { value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    const error = new error_1.FieldError('invalid_url', context, 'url');\n    if (typeof value !== 'string') {\n        return error;\n    }\n    // From https://stackoverflow.com/questions/8667070/javascript-regular-expression-to-validate-url\n    const re = /^(?:(?:(?:https?|ftp):)?\\/\\/)?(?:\\S+(?::\\S*)?@)?(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))(?::\\d{2,5})?(?:[/?#]\\S*)?$/i;\n    // From http://stackoverflow.com/questions/46155/validate-email-address-in-javascript\n    const emailRe = /^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\n    // Allow urls to be valid if the component is pristine and no value is provided.\n    return re.test(value) && !emailRe.test(value) ? null : error;\n};\nexports.validateUrlSync = validateUrlSync;\nconst validateUrl = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateUrlSync)(context);\n});\nexports.validateUrl = validateUrl;\nexports.validateUrlInfo = {\n    name: 'validateUrl',\n    process: exports.validateUrl,\n    processSync: exports.validateUrlSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,eAAe,GAAGD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACG,eAAe,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACzG,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,cAAc,GAAIC,SAAS,IAAK;EAClC,OAAOA,SAAS,IAAIA,SAAS,CAACC,IAAI,KAAK,KAAK;AAChD,CAAC;AACD,MAAML,cAAc,GAAIM,OAAO,IAAK;EAChC,MAAM;IAAEF,SAAS;IAAEvB;EAAM,CAAC,GAAGyB,OAAO;EACpC,IAAI,CAACH,cAAc,CAACC,SAAS,CAAC,EAAE;IAC5B,OAAO,KAAK;EAChB;EACA,IAAIA,SAAS,CAACG,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAAC5B,KAAK,CAAC,IAAIA,KAAK,CAAC6B,MAAM,KAAK,CAAC,EAAE;IAClE,OAAO,KAAK;EAChB;EACA,IAAI,CAAC7B,KAAK,EAAE;IACR,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDe,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,eAAe,GAAIO,OAAO,IAAK;EACjC,MAAM;IAAEzB;EAAM,CAAC,GAAGyB,OAAO;EACzB,IAAI,CAAC,CAAC,CAAC,EAAEV,OAAO,CAACI,cAAc,EAAEM,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,MAAMK,KAAK,GAAG,IAAIV,OAAO,CAACW,UAAU,CAAC,aAAa,EAAEN,OAAO,EAAE,KAAK,CAAC;EACnE,IAAI,OAAOzB,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAO8B,KAAK;EAChB;EACA;EACA,MAAME,EAAE,GAAG,ycAAyc;EACpd;EACA,MAAMC,OAAO,GAAG,sJAAsJ;EACtK;EACA,OAAOD,EAAE,CAACE,IAAI,CAAClC,KAAK,CAAC,IAAI,CAACiC,OAAO,CAACC,IAAI,CAAClC,KAAK,CAAC,GAAG,IAAI,GAAG8B,KAAK;AAChE,CAAC;AACDf,OAAO,CAACG,eAAe,GAAGA,eAAe;AACzC,MAAMD,WAAW,GAAIQ,OAAO,IAAK/B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC5E,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACG,eAAe,EAAEO,OAAO,CAAC;AAChD,CAAC,CAAC;AACFV,OAAO,CAACE,WAAW,GAAGA,WAAW;AACjCF,OAAO,CAACC,eAAe,GAAG;EACtBmB,IAAI,EAAE,aAAa;EACnBC,OAAO,EAAErB,OAAO,CAACE,WAAW;EAC5BoB,WAAW,EAAEtB,OAAO,CAACG,eAAe;EACpCoB,aAAa,EAAEvB,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}