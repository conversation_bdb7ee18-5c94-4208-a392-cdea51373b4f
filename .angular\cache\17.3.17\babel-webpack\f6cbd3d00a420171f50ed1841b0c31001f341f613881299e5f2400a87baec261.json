{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateDayInfo = exports.validateDaySync = exports.validateDay = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isLeapYear = year => {\n  // Year is leap if it is evenly divisible by 400 or evenly divisible by 4 and not evenly divisible by 100.\n  return !(year % 400) || !!(year % 100) && !(year % 4);\n};\nconst getDaysInMonthCount = (month, year) => {\n  switch (month) {\n    case 1: // January\n    case 3: // March\n    case 5: // May\n    case 7: // July\n    case 8: // August\n    case 10: // October\n    case 12:\n      // December\n      return 31;\n    case 4: // April\n    case 6: // June\n    case 9: // September\n    case 11:\n      // November\n      return 30;\n    case 2:\n      // February\n      return isLeapYear(year) ? 29 : 28;\n    default:\n      return 31;\n  }\n};\nconst isDayComponent = component => {\n  return component && component.type === 'day';\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!value) {\n    return false;\n  }\n  if (!isDayComponent(component)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateDay = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateDaySync)(context);\n});\nexports.validateDay = validateDay;\nconst validateDaySync = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context) || !isDayComponent(component)) {\n    return null;\n  }\n  const error = new error_1.FieldError('invalidDay', context, 'day');\n  if (typeof value !== 'string') {\n    return error;\n  }\n  let [DAY, MONTH, YEAR] = component.dayFirst ? [0, 1, 2] : [1, 0, 2];\n  const values = value.split('/').map(x => parseInt(x, 10));\n  let day = values[DAY];\n  let month = values[MONTH];\n  let year = values[YEAR];\n  if (values.length !== 3) {\n    if (component.fields.day.hide) {\n      MONTH = MONTH === 0 ? 0 : MONTH - 1;\n      YEAR = YEAR - 1;\n      day = 0;\n      month = values[MONTH];\n      year = values[YEAR];\n    }\n    if (component.fields.month.hide) {\n      DAY = DAY === 0 ? 0 : DAY - 1;\n      YEAR = YEAR - 1;\n      day = component.fields.day.hide && day === 0 ? 0 : values[DAY];\n      month = 0;\n      year = values[YEAR];\n    }\n    if (component.fields.year.hide) {\n      day = component.fields.day.hide && day === 0 ? 0 : values[DAY];\n      month = component.fields.month.hide && month === 0 ? 0 : values[MONTH];\n      year = 0;\n    }\n  }\n  const maxDay = getDaysInMonthCount(month, year);\n  if (isNaN(day) || day < 0 || day > maxDay) {\n    return error;\n  }\n  if (isNaN(month) || month < 0 || month > 12) {\n    return error;\n  }\n  if (isNaN(year) || year < 0 || year > 9999) {\n    return error;\n  }\n  return null;\n};\nexports.validateDaySync = validateDaySync;\nexports.validateDayInfo = {\n  name: 'validateDay',\n  process: exports.validateDay,\n  processSync: exports.validateDaySync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateDayInfo", "validateDaySync", "validateDay", "shouldValidate", "error_1", "require", "isLeapYear", "year", "getDaysInMonthCount", "month", "isDayComponent", "component", "type", "context", "error", "FieldError", "DAY", "MONTH", "YEAR", "<PERSON><PERSON><PERSON><PERSON>", "values", "split", "map", "x", "parseInt", "day", "length", "fields", "hide", "maxDay", "isNaN", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateDay.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateDayInfo = exports.validateDaySync = exports.validateDay = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isLeapYear = (year) => {\n    // Year is leap if it is evenly divisible by 400 or evenly divisible by 4 and not evenly divisible by 100.\n    return !(year % 400) || (!!(year % 100) && !(year % 4));\n};\nconst getDaysInMonthCount = (month, year) => {\n    switch (month) {\n        case 1: // January\n        case 3: // March\n        case 5: // May\n        case 7: // July\n        case 8: // August\n        case 10: // October\n        case 12: // December\n            return 31;\n        case 4: // April\n        case 6: // June\n        case 9: // September\n        case 11: // November\n            return 30;\n        case 2: // February\n            return isLeapYear(year) ? 29 : 28;\n        default:\n            return 31;\n    }\n};\nconst isDayComponent = (component) => {\n    return component && component.type === 'day';\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!value) {\n        return false;\n    }\n    if (!isDayComponent(component)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateDay = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateDaySync)(context);\n});\nexports.validateDay = validateDay;\nconst validateDaySync = (context) => {\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context) || !isDayComponent(component)) {\n        return null;\n    }\n    const error = new error_1.FieldError('invalidDay', context, 'day');\n    if (typeof value !== 'string') {\n        return error;\n    }\n    let [DAY, MONTH, YEAR] = component.dayFirst ? [0, 1, 2] : [1, 0, 2];\n    const values = value.split('/').map((x) => parseInt(x, 10));\n    let day = values[DAY];\n    let month = values[MONTH];\n    let year = values[YEAR];\n    if (values.length !== 3) {\n        if (component.fields.day.hide) {\n            MONTH = MONTH === 0 ? 0 : MONTH - 1;\n            YEAR = YEAR - 1;\n            day = 0;\n            month = values[MONTH];\n            year = values[YEAR];\n        }\n        if (component.fields.month.hide) {\n            DAY = DAY === 0 ? 0 : DAY - 1;\n            YEAR = YEAR - 1;\n            day = component.fields.day.hide && day === 0 ? 0 : values[DAY];\n            month = 0;\n            year = values[YEAR];\n        }\n        if (component.fields.year.hide) {\n            day = component.fields.day.hide && day === 0 ? 0 : values[DAY];\n            month = component.fields.month.hide && month === 0 ? 0 : values[MONTH];\n            year = 0;\n        }\n    }\n    const maxDay = getDaysInMonthCount(month, year);\n    if (isNaN(day) || day < 0 || day > maxDay) {\n        return error;\n    }\n    if (isNaN(month) || month < 0 || month > 12) {\n        return error;\n    }\n    if (isNaN(year) || year < 0 || year > 9999) {\n        return error;\n    }\n    return null;\n};\nexports.validateDaySync = validateDaySync;\nexports.validateDayInfo = {\n    name: 'validateDay',\n    process: exports.validateDay,\n    processSync: exports.validateDaySync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,eAAe,GAAGD,OAAO,CAACE,eAAe,GAAGF,OAAO,CAACG,WAAW,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACzG,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,UAAU,GAAIC,IAAI,IAAK;EACzB;EACA,OAAO,EAAEA,IAAI,GAAG,GAAG,CAAC,IAAK,CAAC,EAAEA,IAAI,GAAG,GAAG,CAAC,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAE;AAC3D,CAAC;AACD,MAAMC,mBAAmB,GAAGA,CAACC,KAAK,EAAEF,IAAI,KAAK;EACzC,QAAQE,KAAK;IACT,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,EAAE,CAAC,CAAC;IACT,KAAK,EAAE;MAAE;MACL,OAAO,EAAE;IACb,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,EAAE;MAAE;MACL,OAAO,EAAE;IACb,KAAK,CAAC;MAAE;MACJ,OAAOH,UAAU,CAACC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE;IACrC;MACI,OAAO,EAAE;EACjB;AACJ,CAAC;AACD,MAAMG,cAAc,GAAIC,SAAS,IAAK;EAClC,OAAOA,SAAS,IAAIA,SAAS,CAACC,IAAI,KAAK,KAAK;AAChD,CAAC;AACD,MAAMT,cAAc,GAAIU,OAAO,IAAK;EAChC,MAAM;IAAEF,SAAS;IAAE3B;EAAM,CAAC,GAAG6B,OAAO;EACpC,IAAI,CAAC7B,KAAK,EAAE;IACR,OAAO,KAAK;EAChB;EACA,IAAI,CAAC0B,cAAc,CAACC,SAAS,CAAC,EAAE;IAC5B,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDZ,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,WAAW,GAAIW,OAAO,IAAKnC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC5E,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,eAAe,EAAEY,OAAO,CAAC;AAChD,CAAC,CAAC;AACFd,OAAO,CAACG,WAAW,GAAGA,WAAW;AACjC,MAAMD,eAAe,GAAIY,OAAO,IAAK;EACjC,MAAM;IAAEF,SAAS;IAAE3B;EAAM,CAAC,GAAG6B,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAEd,OAAO,CAACI,cAAc,EAAEU,OAAO,CAAC,IAAI,CAACH,cAAc,CAACC,SAAS,CAAC,EAAE;IACrE,OAAO,IAAI;EACf;EACA,MAAMG,KAAK,GAAG,IAAIV,OAAO,CAACW,UAAU,CAAC,YAAY,EAAEF,OAAO,EAAE,KAAK,CAAC;EAClE,IAAI,OAAO7B,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAO8B,KAAK;EAChB;EACA,IAAI,CAACE,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAGP,SAAS,CAACQ,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnE,MAAMC,MAAM,GAAGpC,KAAK,CAACqC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC;EAC3D,IAAIE,GAAG,GAAGL,MAAM,CAACJ,GAAG,CAAC;EACrB,IAAIP,KAAK,GAAGW,MAAM,CAACH,KAAK,CAAC;EACzB,IAAIV,IAAI,GAAGa,MAAM,CAACF,IAAI,CAAC;EACvB,IAAIE,MAAM,CAACM,MAAM,KAAK,CAAC,EAAE;IACrB,IAAIf,SAAS,CAACgB,MAAM,CAACF,GAAG,CAACG,IAAI,EAAE;MAC3BX,KAAK,GAAGA,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;MACnCC,IAAI,GAAGA,IAAI,GAAG,CAAC;MACfO,GAAG,GAAG,CAAC;MACPhB,KAAK,GAAGW,MAAM,CAACH,KAAK,CAAC;MACrBV,IAAI,GAAGa,MAAM,CAACF,IAAI,CAAC;IACvB;IACA,IAAIP,SAAS,CAACgB,MAAM,CAAClB,KAAK,CAACmB,IAAI,EAAE;MAC7BZ,GAAG,GAAGA,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC;MAC7BE,IAAI,GAAGA,IAAI,GAAG,CAAC;MACfO,GAAG,GAAGd,SAAS,CAACgB,MAAM,CAACF,GAAG,CAACG,IAAI,IAAIH,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGL,MAAM,CAACJ,GAAG,CAAC;MAC9DP,KAAK,GAAG,CAAC;MACTF,IAAI,GAAGa,MAAM,CAACF,IAAI,CAAC;IACvB;IACA,IAAIP,SAAS,CAACgB,MAAM,CAACpB,IAAI,CAACqB,IAAI,EAAE;MAC5BH,GAAG,GAAGd,SAAS,CAACgB,MAAM,CAACF,GAAG,CAACG,IAAI,IAAIH,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGL,MAAM,CAACJ,GAAG,CAAC;MAC9DP,KAAK,GAAGE,SAAS,CAACgB,MAAM,CAAClB,KAAK,CAACmB,IAAI,IAAInB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGW,MAAM,CAACH,KAAK,CAAC;MACtEV,IAAI,GAAG,CAAC;IACZ;EACJ;EACA,MAAMsB,MAAM,GAAGrB,mBAAmB,CAACC,KAAK,EAAEF,IAAI,CAAC;EAC/C,IAAIuB,KAAK,CAACL,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAGI,MAAM,EAAE;IACvC,OAAOf,KAAK;EAChB;EACA,IAAIgB,KAAK,CAACrB,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,EAAE,EAAE;IACzC,OAAOK,KAAK;EAChB;EACA,IAAIgB,KAAK,CAACvB,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,IAAI,EAAE;IACxC,OAAOO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDf,OAAO,CAACE,eAAe,GAAGA,eAAe;AACzCF,OAAO,CAACC,eAAe,GAAG;EACtB+B,IAAI,EAAE,aAAa;EACnBC,OAAO,EAAEjC,OAAO,CAACG,WAAW;EAC5B+B,WAAW,EAAElC,OAAO,CAACE,eAAe;EACpCiC,aAAa,EAAEnC,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}