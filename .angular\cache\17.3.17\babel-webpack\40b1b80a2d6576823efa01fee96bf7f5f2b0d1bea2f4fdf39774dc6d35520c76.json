{"ast": null, "code": "\"use strict\";\n\nvar __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n    desc = {\n      enumerable: true,\n      get: function () {\n        return m[k];\n      }\n    };\n  }\n  Object.defineProperty(o, k2, desc);\n} : function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\nvar __exportStar = this && this.__exportStar || function (m, exports) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n__exportStar(require(\"./DefaultValueContext\"), exports);\n__exportStar(require(\"./DefaultValueScope\"), exports);", "map": {"version": 3, "names": ["__createBinding", "Object", "create", "o", "m", "k", "k2", "undefined", "desc", "getOwnPropertyDescriptor", "__esModule", "writable", "configurable", "enumerable", "get", "defineProperty", "__exportStar", "exports", "p", "prototype", "hasOwnProperty", "call", "value", "require"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/types/process/defaultValue/index.js"], "sourcesContent": ["\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__exportStar(require(\"./DefaultValueContext\"), exports);\n__exportStar(require(\"./DefaultValueScope\"), exports);\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,KAAMC,MAAM,CAACC,MAAM,GAAI,UAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;EAC5F,IAAIA,EAAE,KAAKC,SAAS,EAAED,EAAE,GAAGD,CAAC;EAC5B,IAAIG,IAAI,GAAGP,MAAM,CAACQ,wBAAwB,CAACL,CAAC,EAAEC,CAAC,CAAC;EAChD,IAAI,CAACG,IAAI,KAAK,KAAK,IAAIA,IAAI,GAAG,CAACJ,CAAC,CAACM,UAAU,GAAGF,IAAI,CAACG,QAAQ,IAAIH,IAAI,CAACI,YAAY,CAAC,EAAE;IACjFJ,IAAI,GAAG;MAAEK,UAAU,EAAE,IAAI;MAAEC,GAAG,EAAE,SAAAA,CAAA,EAAW;QAAE,OAAOV,CAAC,CAACC,CAAC,CAAC;MAAE;IAAE,CAAC;EAC/D;EACAJ,MAAM,CAACc,cAAc,CAACZ,CAAC,EAAEG,EAAE,EAAEE,IAAI,CAAC;AACtC,CAAC,GAAK,UAASL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;EACxB,IAAIA,EAAE,KAAKC,SAAS,EAAED,EAAE,GAAGD,CAAC;EAC5BF,CAAC,CAACG,EAAE,CAAC,GAAGF,CAAC,CAACC,CAAC,CAAC;AAChB,CAAE,CAAC;AACH,IAAIW,YAAY,GAAI,IAAI,IAAI,IAAI,CAACA,YAAY,IAAK,UAASZ,CAAC,EAAEa,OAAO,EAAE;EACnE,KAAK,IAAIC,CAAC,IAAId,CAAC,EAAE,IAAIc,CAAC,KAAK,SAAS,IAAI,CAACjB,MAAM,CAACkB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,OAAO,EAAEC,CAAC,CAAC,EAAElB,eAAe,CAACiB,OAAO,EAAEb,CAAC,EAAEc,CAAC,CAAC;AAC7H,CAAC;AACDjB,MAAM,CAACc,cAAc,CAACE,OAAO,EAAE,YAAY,EAAE;EAAEK,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DN,YAAY,CAACO,OAAO,CAAC,uBAAuB,CAAC,EAAEN,OAAO,CAAC;AACvDD,YAAY,CAACO,OAAO,CAAC,qBAAqB,CAAC,EAAEN,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}