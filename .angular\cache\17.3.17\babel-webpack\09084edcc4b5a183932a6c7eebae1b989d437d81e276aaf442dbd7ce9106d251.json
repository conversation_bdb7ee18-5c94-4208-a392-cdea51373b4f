{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ComponentPath = void 0;\n/**\n * Defines the Component paths used for every component within a form. This allows for\n * quick reference to either the \"form\" path or the \"data\" path of a component. These paths are\n * defined as follows.\n *\n *  - Form Path: The path to a component within the Form JSON. This path is used to locate a component provided a nested Form JSON object.\n *  - Data Path: The path to the data value of a component within the data model for the form. This path is used to provide the value path provided the Submission JSON object.\n *\n * These paths can also be broken into two different path \"types\".   Local and Full paths.\n *\n *  - Local Path: This is the path relative to the \"current\" form. This is used inside of a nested form to identify components and values relative to the current form in context.\n *  - Full Path: This is the path that is absolute to the root form object. Any nested form paths will include the parent form path as part of the value for the provided path.\n */\nvar ComponentPath;\n(function (ComponentPath) {\n  /**\n   * The \"form\" path to the component including all parent paths (exclusive of layout components). This path is used to uniquely identify component within a form inclusive of any parent form paths.\n   *\n   * For example: Suppose you have the following form structure.\n   *    - Root\n   *      - Panel 1 (panel)\n   *        - Form (form)\n   *          - Panel 2 (panel2)\n   *            - Data Grid (dataGrid)\n   *              - Panel 3 (panel3)\n   *                - TextField (textField)\n   *\n   * The \"path\" to the TextField component from the perspective of a configuration within the Form, would be \"form.dataGrid.textField\"\n   */\n  ComponentPath[\"path\"] = \"path\";\n  /**\n   * The \"form\" path to the component including all parent paths (inclusive of layout componnts). This path is used to uniquely identify component within a form inclusive of any parent form paths.\n   *\n   * For example: Suppose you have the following form structure.\n   *    - Root\n   *      - Panel 1 (panel)\n   *        - Form (form)\n   *          - Panel 2 (panel2)\n   *            - Data Grid (dataGrid)\n   *              - Panel 3 (panel3)\n   *                - TextField (textField)\n   *\n   * The \"fullPath\" to the TextField component from the perspective of a configuration within the Form, would be \"panel1.form.panel2.dataGrid.panel3.textField\"\n   */\n  ComponentPath[\"fullPath\"] = \"fullPath\";\n  /**\n   * The local \"form\" path to the component. This is the local path to any component within a form. This\n   * path is consistent no matter if this form is nested within another form or not. All form configurations\n   * are in relation to this path since forms are configured independently. The difference between a form path\n   * and a dataPath is that this includes any parent layout components to locate the component provided a form JSON.\n   * This path does NOT include any layout components.\n   *\n   * For example: Suppose you have the following form structure.\n   *    - Root\n   *      - Panel 1 (panel)\n   *        - Form (form)\n   *          - Panel 2 (panel2)\n   *            - Data Grid (dataGrid)\n   *              - Panel 3 (panel3)\n   *                - TextField (textField)\n   *\n   * The \"path\" to the TextField component from the perspective of a configuration within the Form, would be \"dataGrid.textField\"\n   */\n  ComponentPath[\"localPath\"] = \"localPath\";\n  /**\n   * The local \"form\" path to the component. This is the local path to any component within a form. This\n   * path is consistent no matter if this form is nested within another form or not. All form configurations\n   * are in relation to this path since forms are configured independently. The difference between a form path\n   * and a dataPath is that this includes any parent layout components to locate the component provided a form JSON.\n   * This path does NOT include any layout components.\n   *\n   * For example: Suppose you have the following form structure.\n   *    - Root\n   *      - Panel 1 (panel)\n   *        - Form (form)\n   *          - Panel 2 (panel2)\n   *            - Data Grid (dataGrid)\n   *              - Panel 3 (panel3)\n   *                - TextField (textField)\n   *\n   * The \"path\" to the TextField component from the perspective of a configuration within the Form, would be \"panel2.dataGrid.panel3.textField\"\n   */\n  ComponentPath[\"fullLocalPath\"] = \"fullLocalPath\";\n  /**\n   *  The \"data\" path to the component including all parent paths. This path is used to fetch the data value of a component within a data model, inclusive of any parent data paths of nested forms.\n   *\n   * For example: Suppose you have the following form structure.\n   *    - Root\n   *      - Panel 1 (panel)\n   *        - Form (form)\n   *          - Panel 2 (panel2)\n   *            - Data Grid (dataGrid)\n   *              - Panel 3 (panel3)\n   *                - TextField (textField)\n   *\n   * The \"dataPath\" to the TextField component would be \"form.data.dataGrid[1].textField\"\n   */\n  ComponentPath[\"dataPath\"] = \"dataPath\";\n  /**\n   * The \"data\" path is the local path to the data value for any component. The difference between this path\n   * and the \"path\" is that this path is used to locate the data value for a component within the data model.\n   * and does not include any keys for layout components.\n   *\n   * For example: Suppose you have the following form structure.\n   *    - Root\n   *      - Panel 1 (panel)\n   *        - Form (form)\n   *          - Panel 2 (panel2)\n   *            - Data Grid (dataGrid)\n   *              - Panel 3 (panel3)\n   *                - TextField (textField)\n   *\n   * The \"localDataPath\" to the TextField component from the perspective of a configuration within the Form, would be \"dataGrid[1].textField\"\n   */\n  ComponentPath[\"localDataPath\"] = \"localDataPath\";\n})(ComponentPath || (exports.ComponentPath = ComponentPath = {}));", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ComponentPath"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/types/formUtil.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ComponentPath = void 0;\n/**\n * Defines the Component paths used for every component within a form. This allows for\n * quick reference to either the \"form\" path or the \"data\" path of a component. These paths are\n * defined as follows.\n *\n *  - Form Path: The path to a component within the Form JSON. This path is used to locate a component provided a nested Form JSON object.\n *  - Data Path: The path to the data value of a component within the data model for the form. This path is used to provide the value path provided the Submission JSON object.\n *\n * These paths can also be broken into two different path \"types\".   Local and Full paths.\n *\n *  - Local Path: This is the path relative to the \"current\" form. This is used inside of a nested form to identify components and values relative to the current form in context.\n *  - Full Path: This is the path that is absolute to the root form object. Any nested form paths will include the parent form path as part of the value for the provided path.\n */\nvar ComponentPath;\n(function (ComponentPath) {\n    /**\n     * The \"form\" path to the component including all parent paths (exclusive of layout components). This path is used to uniquely identify component within a form inclusive of any parent form paths.\n     *\n     * For example: Suppose you have the following form structure.\n     *    - Root\n     *      - Panel 1 (panel)\n     *        - Form (form)\n     *          - Panel 2 (panel2)\n     *            - Data Grid (dataGrid)\n     *              - Panel 3 (panel3)\n     *                - TextField (textField)\n     *\n     * The \"path\" to the TextField component from the perspective of a configuration within the Form, would be \"form.dataGrid.textField\"\n     */\n    ComponentPath[\"path\"] = \"path\";\n    /**\n     * The \"form\" path to the component including all parent paths (inclusive of layout componnts). This path is used to uniquely identify component within a form inclusive of any parent form paths.\n     *\n     * For example: Suppose you have the following form structure.\n     *    - Root\n     *      - Panel 1 (panel)\n     *        - Form (form)\n     *          - Panel 2 (panel2)\n     *            - Data Grid (dataGrid)\n     *              - Panel 3 (panel3)\n     *                - TextField (textField)\n     *\n     * The \"fullPath\" to the TextField component from the perspective of a configuration within the Form, would be \"panel1.form.panel2.dataGrid.panel3.textField\"\n     */\n    ComponentPath[\"fullPath\"] = \"fullPath\";\n    /**\n     * The local \"form\" path to the component. This is the local path to any component within a form. This\n     * path is consistent no matter if this form is nested within another form or not. All form configurations\n     * are in relation to this path since forms are configured independently. The difference between a form path\n     * and a dataPath is that this includes any parent layout components to locate the component provided a form JSON.\n     * This path does NOT include any layout components.\n     *\n     * For example: Suppose you have the following form structure.\n     *    - Root\n     *      - Panel 1 (panel)\n     *        - Form (form)\n     *          - Panel 2 (panel2)\n     *            - Data Grid (dataGrid)\n     *              - Panel 3 (panel3)\n     *                - TextField (textField)\n     *\n     * The \"path\" to the TextField component from the perspective of a configuration within the Form, would be \"dataGrid.textField\"\n     */\n    ComponentPath[\"localPath\"] = \"localPath\";\n    /**\n     * The local \"form\" path to the component. This is the local path to any component within a form. This\n     * path is consistent no matter if this form is nested within another form or not. All form configurations\n     * are in relation to this path since forms are configured independently. The difference between a form path\n     * and a dataPath is that this includes any parent layout components to locate the component provided a form JSON.\n     * This path does NOT include any layout components.\n     *\n     * For example: Suppose you have the following form structure.\n     *    - Root\n     *      - Panel 1 (panel)\n     *        - Form (form)\n     *          - Panel 2 (panel2)\n     *            - Data Grid (dataGrid)\n     *              - Panel 3 (panel3)\n     *                - TextField (textField)\n     *\n     * The \"path\" to the TextField component from the perspective of a configuration within the Form, would be \"panel2.dataGrid.panel3.textField\"\n     */\n    ComponentPath[\"fullLocalPath\"] = \"fullLocalPath\";\n    /**\n     *  The \"data\" path to the component including all parent paths. This path is used to fetch the data value of a component within a data model, inclusive of any parent data paths of nested forms.\n     *\n     * For example: Suppose you have the following form structure.\n     *    - Root\n     *      - Panel 1 (panel)\n     *        - Form (form)\n     *          - Panel 2 (panel2)\n     *            - Data Grid (dataGrid)\n     *              - Panel 3 (panel3)\n     *                - TextField (textField)\n     *\n     * The \"dataPath\" to the TextField component would be \"form.data.dataGrid[1].textField\"\n     */\n    ComponentPath[\"dataPath\"] = \"dataPath\";\n    /**\n     * The \"data\" path is the local path to the data value for any component. The difference between this path\n     * and the \"path\" is that this path is used to locate the data value for a component within the data model.\n     * and does not include any keys for layout components.\n     *\n     * For example: Suppose you have the following form structure.\n     *    - Root\n     *      - Panel 1 (panel)\n     *        - Form (form)\n     *          - Panel 2 (panel2)\n     *            - Data Grid (dataGrid)\n     *              - Panel 3 (panel3)\n     *                - TextField (textField)\n     *\n     * The \"localDataPath\" to the TextField component from the perspective of a configuration within the Form, would be \"dataGrid[1].textField\"\n     */\n    ComponentPath[\"localDataPath\"] = \"localDataPath\";\n})(ComponentPath || (exports.ComponentPath = ComponentPath = {}));\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,aAAa,GAAG,KAAK,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,MAAM,CAAC,GAAG,MAAM;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,UAAU,CAAC,GAAG,UAAU;EACtC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,WAAW,CAAC,GAAG,WAAW;EACxC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,eAAe,CAAC,GAAG,eAAe;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,UAAU,CAAC,GAAG,UAAU;EACtC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,aAAa,CAAC,eAAe,CAAC,GAAG,eAAe;AACpD,CAAC,EAAEA,aAAa,KAAKF,OAAO,CAACE,aAAa,GAAGA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}