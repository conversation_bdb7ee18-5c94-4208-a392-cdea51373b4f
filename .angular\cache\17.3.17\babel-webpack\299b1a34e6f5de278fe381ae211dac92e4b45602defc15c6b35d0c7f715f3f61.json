{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMaximumYearInfo = exports.validateMaximumYearSync = exports.validateMaximumYear = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableDayComponent = component => {\n  var _a, _b;\n  return component && component.type === 'day' && (component.hasOwnProperty('maxYear') || ((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.year) === null || _b === void 0 ? void 0 : _b.hasOwnProperty('maxYear')));\n};\nconst shouldValidate = context => {\n  const {\n    component\n  } = context;\n  if (!isValidatableDayComponent(component)) {\n    return false;\n  }\n  if (!component.maxYear && !component.fields.year.maxYear) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMaximumYear = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMaximumYearSync)(context);\n});\nexports.validateMaximumYear = validateMaximumYear;\nconst validateMaximumYearSync = context => {\n  var _a, _b;\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  if (typeof value !== 'string' && typeof value !== 'number') {\n    throw new error_1.ProcessorError(`Cannot validate maximum year for value ${value}`, context, 'validate:validateMaximumYear');\n  }\n  const testValue = typeof value === 'string' ? value : String(value);\n  const testArr = /\\d{4}$/.exec(testValue);\n  const year = testArr ? testArr[0] : null;\n  if (component.maxYear && ((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.year) === null || _b === void 0 ? void 0 : _b.maxYear) && component.maxYear !== component.fields.year.maxYear) {\n    throw new error_1.ProcessorError('Cannot validate maximum year, component.maxYear and component.fields.year.maxYear are not equal', context, 'validate:validateMaximumYear');\n  }\n  const maxYear = component.maxYear || component.fields.year.maxYear;\n  if (!maxYear || !year) {\n    return null;\n  }\n  return +year <= +maxYear ? null : new error_1.FieldError('maxYear', Object.assign(Object.assign({}, context), {\n    maxYear: String(maxYear),\n    setting: String(maxYear)\n  }));\n};\nexports.validateMaximumYearSync = validateMaximumYearSync;\nexports.validateMaximumYearInfo = {\n  name: 'validateMaximumYear',\n  process: exports.validateMaximumYear,\n  processSync: exports.validateMaximumYearSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateMaximumYearInfo", "validateMaximumYearSync", "validateMaximumYear", "shouldValidate", "error_1", "require", "isValidatableDayComponent", "component", "_a", "_b", "type", "hasOwnProperty", "fields", "year", "context", "maxYear", "ProcessorError", "testValue", "String", "testArr", "exec", "FieldError", "assign", "setting", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMaximumYear.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMaximumYearInfo = exports.validateMaximumYearSync = exports.validateMaximumYear = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableDayComponent = (component) => {\n    var _a, _b;\n    return (component &&\n        component.type === 'day' &&\n        (component.hasOwnProperty('maxYear') || ((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.year) === null || _b === void 0 ? void 0 : _b.hasOwnProperty('maxYear'))));\n};\nconst shouldValidate = (context) => {\n    const { component } = context;\n    if (!isValidatableDayComponent(component)) {\n        return false;\n    }\n    if (!component.maxYear && !component.fields.year.maxYear) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMaximumYear = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMaximumYearSync)(context);\n});\nexports.validateMaximumYear = validateMaximumYear;\nconst validateMaximumYearSync = (context) => {\n    var _a, _b;\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    if (typeof value !== 'string' && typeof value !== 'number') {\n        throw new error_1.ProcessorError(`Cannot validate maximum year for value ${value}`, context, 'validate:validateMaximumYear');\n    }\n    const testValue = typeof value === 'string' ? value : String(value);\n    const testArr = /\\d{4}$/.exec(testValue);\n    const year = testArr ? testArr[0] : null;\n    if (component.maxYear &&\n        ((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.year) === null || _b === void 0 ? void 0 : _b.maxYear) &&\n        component.maxYear !== component.fields.year.maxYear) {\n        throw new error_1.ProcessorError('Cannot validate maximum year, component.maxYear and component.fields.year.maxYear are not equal', context, 'validate:validateMaximumYear');\n    }\n    const maxYear = component.maxYear || component.fields.year.maxYear;\n    if (!maxYear || !year) {\n        return null;\n    }\n    return +year <= +maxYear\n        ? null\n        : new error_1.FieldError('maxYear', Object.assign(Object.assign({}, context), { maxYear: String(maxYear), setting: String(maxYear) }));\n};\nexports.validateMaximumYearSync = validateMaximumYearSync;\nexports.validateMaximumYearInfo = {\n    name: 'validateMaximumYear',\n    process: exports.validateMaximumYear,\n    processSync: exports.validateMaximumYearSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,uBAAuB,GAAGD,OAAO,CAACE,uBAAuB,GAAGF,OAAO,CAACG,mBAAmB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACjI,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,yBAAyB,GAAIC,SAAS,IAAK;EAC7C,IAAIC,EAAE,EAAEC,EAAE;EACV,OAAQF,SAAS,IACbA,SAAS,CAACG,IAAI,KAAK,KAAK,KACvBH,SAAS,CAACI,cAAc,CAAC,SAAS,CAAC,KAAK,CAACF,EAAE,GAAG,CAACD,EAAE,GAAGD,SAAS,CAACK,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;AAChM,CAAC;AACD,MAAMR,cAAc,GAAIW,OAAO,IAAK;EAChC,MAAM;IAAEP;EAAU,CAAC,GAAGO,OAAO;EAC7B,IAAI,CAACR,yBAAyB,CAACC,SAAS,CAAC,EAAE;IACvC,OAAO,KAAK;EAChB;EACA,IAAI,CAACA,SAAS,CAACQ,OAAO,IAAI,CAACR,SAAS,CAACK,MAAM,CAACC,IAAI,CAACE,OAAO,EAAE;IACtD,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDhB,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,mBAAmB,GAAIY,OAAO,IAAKpC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACpF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,uBAAuB,EAAEa,OAAO,CAAC;AACxD,CAAC,CAAC;AACFf,OAAO,CAACG,mBAAmB,GAAGA,mBAAmB;AACjD,MAAMD,uBAAuB,GAAIa,OAAO,IAAK;EACzC,IAAIN,EAAE,EAAEC,EAAE;EACV,MAAM;IAAEF,SAAS;IAAEvB;EAAM,CAAC,GAAG8B,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAEf,OAAO,CAACI,cAAc,EAAEW,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAI,OAAO9B,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACxD,MAAM,IAAIoB,OAAO,CAACY,cAAc,CAAC,0CAA0ChC,KAAK,EAAE,EAAE8B,OAAO,EAAE,8BAA8B,CAAC;EAChI;EACA,MAAMG,SAAS,GAAG,OAAOjC,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGkC,MAAM,CAAClC,KAAK,CAAC;EACnE,MAAMmC,OAAO,GAAG,QAAQ,CAACC,IAAI,CAACH,SAAS,CAAC;EACxC,MAAMJ,IAAI,GAAGM,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EACxC,IAAIZ,SAAS,CAACQ,OAAO,KAChB,CAACN,EAAE,GAAG,CAACD,EAAE,GAAGD,SAAS,CAACK,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,IAAI,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,OAAO,CAAC,IAC7HR,SAAS,CAACQ,OAAO,KAAKR,SAAS,CAACK,MAAM,CAACC,IAAI,CAACE,OAAO,EAAE;IACrD,MAAM,IAAIX,OAAO,CAACY,cAAc,CAAC,iGAAiG,EAAEF,OAAO,EAAE,8BAA8B,CAAC;EAChL;EACA,MAAMC,OAAO,GAAGR,SAAS,CAACQ,OAAO,IAAIR,SAAS,CAACK,MAAM,CAACC,IAAI,CAACE,OAAO;EAClE,IAAI,CAACA,OAAO,IAAI,CAACF,IAAI,EAAE;IACnB,OAAO,IAAI;EACf;EACA,OAAO,CAACA,IAAI,IAAI,CAACE,OAAO,GAClB,IAAI,GACJ,IAAIX,OAAO,CAACiB,UAAU,CAAC,SAAS,EAAExB,MAAM,CAACyB,MAAM,CAACzB,MAAM,CAACyB,MAAM,CAAC,CAAC,CAAC,EAAER,OAAO,CAAC,EAAE;IAAEC,OAAO,EAAEG,MAAM,CAACH,OAAO,CAAC;IAAEQ,OAAO,EAAEL,MAAM,CAACH,OAAO;EAAE,CAAC,CAAC,CAAC;AAC9I,CAAC;AACDhB,OAAO,CAACE,uBAAuB,GAAGA,uBAAuB;AACzDF,OAAO,CAACC,uBAAuB,GAAG;EAC9BwB,IAAI,EAAE,qBAAqB;EAC3BC,OAAO,EAAE1B,OAAO,CAACG,mBAAmB;EACpCwB,WAAW,EAAE3B,OAAO,CAACE,uBAAuB;EAC5C0B,aAAa,EAAE5B,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}