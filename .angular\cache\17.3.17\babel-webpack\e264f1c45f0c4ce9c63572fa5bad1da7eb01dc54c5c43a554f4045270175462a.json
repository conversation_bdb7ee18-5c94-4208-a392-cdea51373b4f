{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst formUtil_1 = require(\"../formUtil\");\nconst ConditionOperator_1 = __importDefault(require(\"./ConditionOperator\"));\nconst lodash_1 = require(\"lodash\");\nclass IsEqualTo extends ConditionOperator_1.default {\n  static get operatorKey() {\n    return 'isEqual';\n  }\n  static get displayedName() {\n    return 'Is Equal To';\n  }\n  execute({\n    value,\n    comparedValue,\n    conditionComponent\n  }) {\n    // special check for select boxes\n    if ((conditionComponent === null || conditionComponent === void 0 ? void 0 : conditionComponent.type) === 'selectboxes' && (0, lodash_1.isObject)(value)) {\n      return (0, lodash_1.get)(value, comparedValue, false);\n    }\n    if (value && comparedValue && typeof value !== typeof comparedValue && (0, lodash_1.isString)(comparedValue)) {\n      try {\n        comparedValue = JSON.parse(comparedValue);\n      } catch (ignoreErr) {\n        // do nothing\n      }\n    }\n    if (conditionComponent && (0, formUtil_1.isSelectResourceWithObjectValue)(conditionComponent) && conditionComponent.template) {\n      return (0, formUtil_1.compareSelectResourceWithObjectTypeValues)(value, comparedValue, conditionComponent);\n    }\n    return (0, lodash_1.isEqual)(value, comparedValue);\n  }\n}\nexports.default = IsEqualTo;", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "formUtil_1", "require", "ConditionOperator_1", "lodash_1", "IsEqualTo", "default", "operatorKey", "displayedName", "execute", "comparedValue", "conditionComponent", "type", "isObject", "get", "isString", "JSON", "parse", "ignoreErr", "isSelectResourceWithObjectValue", "template", "compareSelectResourceWithObjectTypeValues", "isEqual"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/utils/operators/IsEqualTo.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst formUtil_1 = require(\"../formUtil\");\nconst ConditionOperator_1 = __importDefault(require(\"./ConditionOperator\"));\nconst lodash_1 = require(\"lodash\");\nclass IsEqualTo extends ConditionOperator_1.default {\n    static get operatorKey() {\n        return 'isEqual';\n    }\n    static get displayedName() {\n        return 'Is Equal To';\n    }\n    execute({ value, comparedValue, conditionComponent }) {\n        // special check for select boxes\n        if ((conditionComponent === null || conditionComponent === void 0 ? void 0 : conditionComponent.type) === 'selectboxes' && (0, lodash_1.isObject)(value)) {\n            return (0, lodash_1.get)(value, comparedValue, false);\n        }\n        if (value &&\n            comparedValue &&\n            typeof value !== typeof comparedValue &&\n            (0, lodash_1.isString)(comparedValue)) {\n            try {\n                comparedValue = JSON.parse(comparedValue);\n            }\n            catch (ignoreErr) {\n                // do nothing\n            }\n        }\n        if (conditionComponent &&\n            (0, formUtil_1.isSelectResourceWithObjectValue)(conditionComponent) &&\n            conditionComponent.template) {\n            return (0, formUtil_1.compareSelectResourceWithObjectTypeValues)(value, comparedValue, conditionComponent);\n        }\n        return (0, lodash_1.isEqual)(value, comparedValue);\n    }\n}\nexports.default = IsEqualTo;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,MAAMC,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;AACzC,MAAMC,mBAAmB,GAAGT,eAAe,CAACQ,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC3E,MAAME,QAAQ,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMG,SAAS,SAASF,mBAAmB,CAACG,OAAO,CAAC;EAChD,WAAWC,WAAWA,CAAA,EAAG;IACrB,OAAO,SAAS;EACpB;EACA,WAAWC,aAAaA,CAAA,EAAG;IACvB,OAAO,aAAa;EACxB;EACAC,OAAOA,CAAC;IAAET,KAAK;IAAEU,aAAa;IAAEC;EAAmB,CAAC,EAAE;IAClD;IACA,IAAI,CAACA,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,IAAI,MAAM,aAAa,IAAI,CAAC,CAAC,EAAER,QAAQ,CAACS,QAAQ,EAAEb,KAAK,CAAC,EAAE;MACtJ,OAAO,CAAC,CAAC,EAAEI,QAAQ,CAACU,GAAG,EAAEd,KAAK,EAAEU,aAAa,EAAE,KAAK,CAAC;IACzD;IACA,IAAIV,KAAK,IACLU,aAAa,IACb,OAAOV,KAAK,KAAK,OAAOU,aAAa,IACrC,CAAC,CAAC,EAAEN,QAAQ,CAACW,QAAQ,EAAEL,aAAa,CAAC,EAAE;MACvC,IAAI;QACAA,aAAa,GAAGM,IAAI,CAACC,KAAK,CAACP,aAAa,CAAC;MAC7C,CAAC,CACD,OAAOQ,SAAS,EAAE;QACd;MAAA;IAER;IACA,IAAIP,kBAAkB,IAClB,CAAC,CAAC,EAAEV,UAAU,CAACkB,+BAA+B,EAAER,kBAAkB,CAAC,IACnEA,kBAAkB,CAACS,QAAQ,EAAE;MAC7B,OAAO,CAAC,CAAC,EAAEnB,UAAU,CAACoB,yCAAyC,EAAErB,KAAK,EAAEU,aAAa,EAAEC,kBAAkB,CAAC;IAC9G;IACA,OAAO,CAAC,CAAC,EAAEP,QAAQ,CAACkB,OAAO,EAAEtB,KAAK,EAAEU,aAAa,CAAC;EACtD;AACJ;AACAX,OAAO,CAACO,OAAO,GAAGD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}