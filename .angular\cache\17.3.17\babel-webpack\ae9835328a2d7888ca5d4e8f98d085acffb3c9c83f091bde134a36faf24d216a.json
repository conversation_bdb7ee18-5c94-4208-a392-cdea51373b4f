{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ProcessorType = void 0;\nvar ProcessorType;\n(function (ProcessorType) {\n  ProcessorType[\"Validate\"] = \"validate\";\n  ProcessorType[\"Custom\"] = \"custom\";\n})(ProcessorType || (exports.ProcessorType = ProcessorType = {}));", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ProcessorType"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/types/process/ProcessorType.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ProcessorType = void 0;\nvar ProcessorType;\n(function (ProcessorType) {\n    ProcessorType[\"Validate\"] = \"validate\";\n    ProcessorType[\"Custom\"] = \"custom\";\n})(ProcessorType || (exports.ProcessorType = ProcessorType = {}));\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,aAAa,GAAG,KAAK,CAAC;AAC9B,IAAIA,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtBA,aAAa,CAAC,UAAU,CAAC,GAAG,UAAU;EACtCA,aAAa,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACtC,CAAC,EAAEA,aAAa,KAAKF,OAAO,CAACE,aAAa,GAAGA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}