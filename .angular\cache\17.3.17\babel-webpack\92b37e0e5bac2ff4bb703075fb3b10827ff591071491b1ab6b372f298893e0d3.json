{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateEmailInfo = exports.validateEmailSync = exports.validateEmail = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableEmailComponent = component => {\n  return component && component.type === 'email';\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!value) {\n    return false;\n  }\n  if (!isValidatableEmailComponent(component)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateEmail = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateEmailSync)(context);\n});\nexports.validateEmail = validateEmail;\nconst validateEmailSync = context => {\n  const error = new error_1.FieldError('invalid_email', context, 'email');\n  const {\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  // From http://stackoverflow.com/questions/46155/validate-email-address-in-javascript\n  const emailRegex = /^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\n  // Allow emails to be valid if the component is pristine and no value is provided.\n  if (typeof value === 'string' && !emailRegex.test(value)) {\n    return error;\n  }\n  return null;\n};\nexports.validateEmailSync = validateEmailSync;\nexports.validateEmailInfo = {\n  name: 'validateEmail',\n  process: exports.validateEmail,\n  processSync: exports.validateEmailSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateEmailInfo", "validateEmailSync", "validateEmail", "shouldValidate", "error_1", "require", "isValidatableEmailComponent", "component", "type", "context", "error", "FieldError", "emailRegex", "test", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateEmail.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateEmailInfo = exports.validateEmailSync = exports.validateEmail = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableEmailComponent = (component) => {\n    return component && component.type === 'email';\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!value) {\n        return false;\n    }\n    if (!isValidatableEmailComponent(component)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateEmail = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateEmailSync)(context);\n});\nexports.validateEmail = validateEmail;\nconst validateEmailSync = (context) => {\n    const error = new error_1.FieldError('invalid_email', context, 'email');\n    const { value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    // From http://stackoverflow.com/questions/46155/validate-email-address-in-javascript\n    const emailRegex = /^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\n    // Allow emails to be valid if the component is pristine and no value is provided.\n    if (typeof value === 'string' && !emailRegex.test(value)) {\n        return error;\n    }\n    return null;\n};\nexports.validateEmailSync = validateEmailSync;\nexports.validateEmailInfo = {\n    name: 'validateEmail',\n    process: exports.validateEmail,\n    processSync: exports.validateEmailSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,iBAAiB,GAAGD,OAAO,CAACE,iBAAiB,GAAGF,OAAO,CAACG,aAAa,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AAC/G,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,2BAA2B,GAAIC,SAAS,IAAK;EAC/C,OAAOA,SAAS,IAAIA,SAAS,CAACC,IAAI,KAAK,OAAO;AAClD,CAAC;AACD,MAAML,cAAc,GAAIM,OAAO,IAAK;EAChC,MAAM;IAAEF,SAAS;IAAEvB;EAAM,CAAC,GAAGyB,OAAO;EACpC,IAAI,CAACzB,KAAK,EAAE;IACR,OAAO,KAAK;EAChB;EACA,IAAI,CAACsB,2BAA2B,CAACC,SAAS,CAAC,EAAE;IACzC,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDR,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,aAAa,GAAIO,OAAO,IAAK/B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC9E,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,iBAAiB,EAAEQ,OAAO,CAAC;AAClD,CAAC,CAAC;AACFV,OAAO,CAACG,aAAa,GAAGA,aAAa;AACrC,MAAMD,iBAAiB,GAAIQ,OAAO,IAAK;EACnC,MAAMC,KAAK,GAAG,IAAIN,OAAO,CAACO,UAAU,CAAC,eAAe,EAAEF,OAAO,EAAE,OAAO,CAAC;EACvE,MAAM;IAAEzB;EAAM,CAAC,GAAGyB,OAAO;EACzB,IAAI,CAAC,CAAC,CAAC,EAAEV,OAAO,CAACI,cAAc,EAAEM,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA;EACA,MAAMG,UAAU,GAAG,sJAAsJ;EACzK;EACA,IAAI,OAAO5B,KAAK,KAAK,QAAQ,IAAI,CAAC4B,UAAU,CAACC,IAAI,CAAC7B,KAAK,CAAC,EAAE;IACtD,OAAO0B,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDX,OAAO,CAACE,iBAAiB,GAAGA,iBAAiB;AAC7CF,OAAO,CAACC,iBAAiB,GAAG;EACxBc,IAAI,EAAE,eAAe;EACrBC,OAAO,EAAEhB,OAAO,CAACG,aAAa;EAC9Bc,WAAW,EAAEjB,OAAO,CAACE,iBAAiB;EACtCgB,aAAa,EAAElB,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}