{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateCustomInfo = exports.validateCustomSync = exports.shouldValidate = exports.validateCustom = void 0;\nconst error_1 = require(\"../../../error\");\nconst utils_1 = require(\"../../../utils\");\nconst validateCustom = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateCustomSync)(context);\n});\nexports.validateCustom = validateCustom;\nconst shouldValidate = context => {\n  var _a;\n  const {\n    component\n  } = context;\n  const customValidation = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.custom;\n  if (!customValidation) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateCustomSync = context => {\n  var _a;\n  const {\n    component,\n    index,\n    instance,\n    value,\n    data,\n    row\n  } = context;\n  const customValidation = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.custom;\n  try {\n    if (!(0, exports.shouldValidate)(context) || !customValidation) {\n      return null;\n    }\n    const validationContext = (instance === null || instance === void 0 ? void 0 : instance.evalContext) ? instance.evalContext() : context;\n    // We have to augment some of the evalContext values here if the evalContext comes from the instance\n    const isValid = (0, utils_1.evaluate)(customValidation, validationContext, 'valid', true, context => {\n      context.component = component;\n      context.data = data;\n      context.row = row;\n      context.rowIndex = typeof index === 'number' ? index : validationContext.rowIndex;\n      context.instance = instance;\n      context.valid = true;\n      context.input = value;\n    });\n    if (isValid === null || isValid === true) {\n      return null;\n    }\n    return new error_1.FieldError(typeof isValid === 'string' ? isValid : 'custom', Object.assign(Object.assign({}, context), {\n      hasLabel: false,\n      setting: customValidation\n    }), 'custom');\n  } catch (err) {\n    throw new error_1.ProcessorError(err.message || err, context, 'validate:validateCustom');\n  }\n};\nexports.validateCustomSync = validateCustomSync;\nexports.validateCustomInfo = {\n  name: 'validateCustom',\n  process: exports.validateCustom,\n  processSync: exports.validateCustomSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateCustomInfo", "validateCustomSync", "shouldValidate", "validateCustom", "error_1", "require", "utils_1", "context", "_a", "component", "customValidation", "validate", "custom", "index", "instance", "data", "row", "validationContext", "evalContext", "<PERSON><PERSON><PERSON><PERSON>", "evaluate", "rowIndex", "valid", "input", "FieldError", "assign", "<PERSON><PERSON><PERSON><PERSON>", "setting", "err", "ProcessorError", "message", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateCustom.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateCustomInfo = exports.validateCustomSync = exports.shouldValidate = exports.validateCustom = void 0;\nconst error_1 = require(\"../../../error\");\nconst utils_1 = require(\"../../../utils\");\nconst validateCustom = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateCustomSync)(context);\n});\nexports.validateCustom = validateCustom;\nconst shouldValidate = (context) => {\n    var _a;\n    const { component } = context;\n    const customValidation = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.custom;\n    if (!customValidation) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateCustomSync = (context) => {\n    var _a;\n    const { component, index, instance, value, data, row } = context;\n    const customValidation = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.custom;\n    try {\n        if (!(0, exports.shouldValidate)(context) || !customValidation) {\n            return null;\n        }\n        const validationContext = (instance === null || instance === void 0 ? void 0 : instance.evalContext) ? instance.evalContext() : context;\n        // We have to augment some of the evalContext values here if the evalContext comes from the instance\n        const isValid = (0, utils_1.evaluate)(customValidation, validationContext, 'valid', true, (context) => {\n            context.component = component;\n            context.data = data;\n            context.row = row;\n            context.rowIndex = typeof index === 'number' ? index : validationContext.rowIndex;\n            context.instance = instance;\n            context.valid = true;\n            context.input = value;\n        });\n        if (isValid === null || isValid === true) {\n            return null;\n        }\n        return new error_1.FieldError(typeof isValid === 'string' ? isValid : 'custom', Object.assign(Object.assign({}, context), { hasLabel: false, setting: customValidation }), 'custom');\n    }\n    catch (err) {\n        throw new error_1.ProcessorError(err.message || err, context, 'validate:validateCustom');\n    }\n};\nexports.validateCustomSync = validateCustomSync;\nexports.validateCustomInfo = {\n    name: 'validateCustom',\n    process: exports.validateCustom,\n    processSync: exports.validateCustomSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,kBAAkB,GAAGD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,cAAc,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AAClH,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,OAAO,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMF,cAAc,GAAII,OAAO,IAAK7B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC/E,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,kBAAkB,EAAEM,OAAO,CAAC;AACnD,CAAC,CAAC;AACFR,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,cAAc,GAAIK,OAAO,IAAK;EAChC,IAAIC,EAAE;EACN,MAAM;IAAEC;EAAU,CAAC,GAAGF,OAAO;EAC7B,MAAMG,gBAAgB,GAAG,CAACF,EAAE,GAAGC,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,MAAM;EACjG,IAAI,CAACF,gBAAgB,EAAE;IACnB,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDX,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvC,MAAMD,kBAAkB,GAAIM,OAAO,IAAK;EACpC,IAAIC,EAAE;EACN,MAAM;IAAEC,SAAS;IAAEI,KAAK;IAAEC,QAAQ;IAAE9B,KAAK;IAAE+B,IAAI;IAAEC;EAAI,CAAC,GAAGT,OAAO;EAChE,MAAMG,gBAAgB,GAAG,CAACF,EAAE,GAAGC,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,MAAM;EACjG,IAAI;IACA,IAAI,CAAC,CAAC,CAAC,EAAEb,OAAO,CAACG,cAAc,EAAEK,OAAO,CAAC,IAAI,CAACG,gBAAgB,EAAE;MAC5D,OAAO,IAAI;IACf;IACA,MAAMO,iBAAiB,GAAG,CAACH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACI,WAAW,IAAIJ,QAAQ,CAACI,WAAW,CAAC,CAAC,GAAGX,OAAO;IACvI;IACA,MAAMY,OAAO,GAAG,CAAC,CAAC,EAAEb,OAAO,CAACc,QAAQ,EAAEV,gBAAgB,EAAEO,iBAAiB,EAAE,OAAO,EAAE,IAAI,EAAGV,OAAO,IAAK;MACnGA,OAAO,CAACE,SAAS,GAAGA,SAAS;MAC7BF,OAAO,CAACQ,IAAI,GAAGA,IAAI;MACnBR,OAAO,CAACS,GAAG,GAAGA,GAAG;MACjBT,OAAO,CAACc,QAAQ,GAAG,OAAOR,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGI,iBAAiB,CAACI,QAAQ;MACjFd,OAAO,CAACO,QAAQ,GAAGA,QAAQ;MAC3BP,OAAO,CAACe,KAAK,GAAG,IAAI;MACpBf,OAAO,CAACgB,KAAK,GAAGvC,KAAK;IACzB,CAAC,CAAC;IACF,IAAImC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,IAAI,EAAE;MACtC,OAAO,IAAI;IACf;IACA,OAAO,IAAIf,OAAO,CAACoB,UAAU,CAAC,OAAOL,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG,QAAQ,EAAEtB,MAAM,CAAC4B,MAAM,CAAC5B,MAAM,CAAC4B,MAAM,CAAC,CAAC,CAAC,EAAElB,OAAO,CAAC,EAAE;MAAEmB,QAAQ,EAAE,KAAK;MAAEC,OAAO,EAAEjB;IAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC;EACxL,CAAC,CACD,OAAOkB,GAAG,EAAE;IACR,MAAM,IAAIxB,OAAO,CAACyB,cAAc,CAACD,GAAG,CAACE,OAAO,IAAIF,GAAG,EAAErB,OAAO,EAAE,yBAAyB,CAAC;EAC5F;AACJ,CAAC;AACDR,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/CF,OAAO,CAACC,kBAAkB,GAAG;EACzB+B,IAAI,EAAE,gBAAgB;EACtBC,OAAO,EAAEjC,OAAO,CAACI,cAAc;EAC/B8B,WAAW,EAAElC,OAAO,CAACE,kBAAkB;EACvCiC,aAAa,EAAEnC,OAAO,CAACG;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}