{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateRequiredInfo = exports.validateRequiredSync = exports.validateRequired = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst util_1 = require(\"../util\");\nconst formUtil_1 = require(\"../../../utils/formUtil\");\nconst isAddressComponent = component => {\n  return component.type === 'address';\n};\nconst isDayComponent = component => {\n  return component.type === 'day';\n};\nconst isAddressComponentDataObject = value => {\n  return value !== null && typeof value === 'object' && value.mode && value.address && typeof value.address === 'object';\n};\n// Checkboxes and selectboxes consider false to be falsy, whereas other components with\n// settable values (e.g. radio, select, datamap, container, etc.) consider it truthy\nconst isComponentThatCannotHaveFalseValue = component => {\n  return component.type === 'checkbox' || component.type === 'selectboxes';\n};\nconst valueIsPresent = (value, considerFalseTruthy, isNestedDatatype) => {\n  // Evaluate for 3 out of 6 falsy values (\"\", null, undefined), don't check for 0\n  // and only check for false under certain conditions\n  if (value === null || value === undefined || value === '' || !considerFalseTruthy && value === false) {\n    return false;\n  }\n  // Evaluate for empty object\n  else if ((0, util_1.isEmptyObject)(value)) {\n    return false;\n  }\n  // Evaluate for empty array\n  else if (Array.isArray(value) && value.length === 0) {\n    return false;\n  }\n  // Recursively evaluate\n  else if (typeof value === 'object' && !isNestedDatatype) {\n    return Object.values(value).some(val => valueIsPresent(val, considerFalseTruthy, isNestedDatatype));\n  }\n  // If value is an array, check it's children have value\n  else if (Array.isArray(value) && value.length) {\n    return (0, util_1.doesArrayDataHaveValue)(value);\n  }\n  return true;\n};\nconst shouldValidate = context => {\n  var _a;\n  const {\n    component\n  } = context;\n  return !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.required);\n};\nexports.shouldValidate = shouldValidate;\nconst validateRequired = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateRequiredSync)(context);\n});\nexports.validateRequired = validateRequired;\nconst validateRequiredSync = context => {\n  const error = new error_1.FieldError('required', Object.assign(Object.assign({}, context), {\n    setting: true\n  }));\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  if (isAddressComponent(component) && isAddressComponentDataObject(value)) {\n    return (0, util_1.isEmptyObject)(value.address) ? error : Object.values(value.address).every(val => !!val) ? null : error;\n  } else if (isDayComponent(component) && value === '00/00/0000') {\n    return error;\n  } else if (isComponentThatCannotHaveFalseValue(component)) {\n    return !valueIsPresent(value, false, (0, formUtil_1.isComponentNestedDataType)(component)) ? error : null;\n  }\n  return !valueIsPresent(value, true, (0, formUtil_1.isComponentNestedDataType)(component)) ? error : null;\n};\nexports.validateRequiredSync = validateRequiredSync;\nexports.validateRequiredInfo = {\n  name: 'validateRequired',\n  process: exports.validateRequired,\n  processSync: exports.validateRequiredSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateRequiredInfo", "validateRequiredSync", "validateRequired", "shouldValidate", "error_1", "require", "util_1", "formUtil_1", "isAddressComponent", "component", "type", "isDayComponent", "isAddressComponentDataObject", "mode", "address", "isComponentThatCannotHaveFalseValue", "valueIsPresent", "considerFalseTruthy", "isNestedDatatype", "undefined", "isEmptyObject", "Array", "isArray", "length", "values", "some", "val", "doesArrayDataHaveValue", "context", "_a", "validate", "required", "error", "FieldError", "assign", "setting", "every", "isComponentNestedDataType", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateRequired.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateRequiredInfo = exports.validateRequiredSync = exports.validateRequired = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst util_1 = require(\"../util\");\nconst formUtil_1 = require(\"../../../utils/formUtil\");\nconst isAddressComponent = (component) => {\n    return component.type === 'address';\n};\nconst isDayComponent = (component) => {\n    return component.type === 'day';\n};\nconst isAddressComponentDataObject = (value) => {\n    return (value !== null &&\n        typeof value === 'object' &&\n        value.mode &&\n        value.address &&\n        typeof value.address === 'object');\n};\n// Checkboxes and selectboxes consider false to be falsy, whereas other components with\n// settable values (e.g. radio, select, datamap, container, etc.) consider it truthy\nconst isComponentThatCannotHaveFalseValue = (component) => {\n    return component.type === 'checkbox' || component.type === 'selectboxes';\n};\nconst valueIsPresent = (value, considerFalseTruthy, isNestedDatatype) => {\n    // Evaluate for 3 out of 6 falsy values (\"\", null, undefined), don't check for 0\n    // and only check for false under certain conditions\n    if (value === null ||\n        value === undefined ||\n        value === '' ||\n        (!considerFalseTruthy && value === false)) {\n        return false;\n    }\n    // Evaluate for empty object\n    else if ((0, util_1.isEmptyObject)(value)) {\n        return false;\n    }\n    // Evaluate for empty array\n    else if (Array.isArray(value) && value.length === 0) {\n        return false;\n    }\n    // Recursively evaluate\n    else if (typeof value === 'object' && !isNestedDatatype) {\n        return Object.values(value).some((val) => valueIsPresent(val, considerFalseTruthy, isNestedDatatype));\n    }\n    // If value is an array, check it's children have value\n    else if (Array.isArray(value) && value.length) {\n        return (0, util_1.doesArrayDataHaveValue)(value);\n    }\n    return true;\n};\nconst shouldValidate = (context) => {\n    var _a;\n    const { component } = context;\n    return !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.required);\n};\nexports.shouldValidate = shouldValidate;\nconst validateRequired = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateRequiredSync)(context);\n});\nexports.validateRequired = validateRequired;\nconst validateRequiredSync = (context) => {\n    const error = new error_1.FieldError('required', Object.assign(Object.assign({}, context), { setting: true }));\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    if (isAddressComponent(component) && isAddressComponentDataObject(value)) {\n        return (0, util_1.isEmptyObject)(value.address)\n            ? error\n            : Object.values(value.address).every((val) => !!val)\n                ? null\n                : error;\n    }\n    else if (isDayComponent(component) && value === '00/00/0000') {\n        return error;\n    }\n    else if (isComponentThatCannotHaveFalseValue(component)) {\n        return !valueIsPresent(value, false, (0, formUtil_1.isComponentNestedDataType)(component)) ? error : null;\n    }\n    return !valueIsPresent(value, true, (0, formUtil_1.isComponentNestedDataType)(component)) ? error : null;\n};\nexports.validateRequiredSync = validateRequiredSync;\nexports.validateRequiredInfo = {\n    name: 'validateRequired',\n    process: exports.validateRequired,\n    processSync: exports.validateRequiredSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,oBAAoB,GAAGD,OAAO,CAACE,oBAAoB,GAAGF,OAAO,CAACG,gBAAgB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACxH,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,MAAM,GAAGD,OAAO,CAAC,SAAS,CAAC;AACjC,MAAME,UAAU,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACrD,MAAMG,kBAAkB,GAAIC,SAAS,IAAK;EACtC,OAAOA,SAAS,CAACC,IAAI,KAAK,SAAS;AACvC,CAAC;AACD,MAAMC,cAAc,GAAIF,SAAS,IAAK;EAClC,OAAOA,SAAS,CAACC,IAAI,KAAK,KAAK;AACnC,CAAC;AACD,MAAME,4BAA4B,GAAI5B,KAAK,IAAK;EAC5C,OAAQA,KAAK,KAAK,IAAI,IAClB,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,CAAC6B,IAAI,IACV7B,KAAK,CAAC8B,OAAO,IACb,OAAO9B,KAAK,CAAC8B,OAAO,KAAK,QAAQ;AACzC,CAAC;AACD;AACA;AACA,MAAMC,mCAAmC,GAAIN,SAAS,IAAK;EACvD,OAAOA,SAAS,CAACC,IAAI,KAAK,UAAU,IAAID,SAAS,CAACC,IAAI,KAAK,aAAa;AAC5E,CAAC;AACD,MAAMM,cAAc,GAAGA,CAAChC,KAAK,EAAEiC,mBAAmB,EAAEC,gBAAgB,KAAK;EACrE;EACA;EACA,IAAIlC,KAAK,KAAK,IAAI,IACdA,KAAK,KAAKmC,SAAS,IACnBnC,KAAK,KAAK,EAAE,IACX,CAACiC,mBAAmB,IAAIjC,KAAK,KAAK,KAAM,EAAE;IAC3C,OAAO,KAAK;EAChB;EACA;EAAA,KACK,IAAI,CAAC,CAAC,EAAEsB,MAAM,CAACc,aAAa,EAAEpC,KAAK,CAAC,EAAE;IACvC,OAAO,KAAK;EAChB;EACA;EAAA,KACK,IAAIqC,KAAK,CAACC,OAAO,CAACtC,KAAK,CAAC,IAAIA,KAAK,CAACuC,MAAM,KAAK,CAAC,EAAE;IACjD,OAAO,KAAK;EAChB;EACA;EAAA,KACK,IAAI,OAAOvC,KAAK,KAAK,QAAQ,IAAI,CAACkC,gBAAgB,EAAE;IACrD,OAAOrB,MAAM,CAAC2B,MAAM,CAACxC,KAAK,CAAC,CAACyC,IAAI,CAAEC,GAAG,IAAKV,cAAc,CAACU,GAAG,EAAET,mBAAmB,EAAEC,gBAAgB,CAAC,CAAC;EACzG;EACA;EAAA,KACK,IAAIG,KAAK,CAACC,OAAO,CAACtC,KAAK,CAAC,IAAIA,KAAK,CAACuC,MAAM,EAAE;IAC3C,OAAO,CAAC,CAAC,EAAEjB,MAAM,CAACqB,sBAAsB,EAAE3C,KAAK,CAAC;EACpD;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAMmB,cAAc,GAAIyB,OAAO,IAAK;EAChC,IAAIC,EAAE;EACN,MAAM;IAAEpB;EAAU,CAAC,GAAGmB,OAAO;EAC7B,OAAO,CAAC,EAAE,CAACC,EAAE,GAAGpB,SAAS,CAACqB,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,QAAQ,CAAC;AACzF,CAAC;AACDhC,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,gBAAgB,GAAI0B,OAAO,IAAKlD,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACjF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,oBAAoB,EAAE2B,OAAO,CAAC;AACrD,CAAC,CAAC;AACF7B,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMD,oBAAoB,GAAI2B,OAAO,IAAK;EACtC,MAAMI,KAAK,GAAG,IAAI5B,OAAO,CAAC6B,UAAU,CAAC,UAAU,EAAEpC,MAAM,CAACqC,MAAM,CAACrC,MAAM,CAACqC,MAAM,CAAC,CAAC,CAAC,EAAEN,OAAO,CAAC,EAAE;IAAEO,OAAO,EAAE;EAAK,CAAC,CAAC,CAAC;EAC9G,MAAM;IAAE1B,SAAS;IAAEzB;EAAM,CAAC,GAAG4C,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAE7B,OAAO,CAACI,cAAc,EAAEyB,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAIpB,kBAAkB,CAACC,SAAS,CAAC,IAAIG,4BAA4B,CAAC5B,KAAK,CAAC,EAAE;IACtE,OAAO,CAAC,CAAC,EAAEsB,MAAM,CAACc,aAAa,EAAEpC,KAAK,CAAC8B,OAAO,CAAC,GACzCkB,KAAK,GACLnC,MAAM,CAAC2B,MAAM,CAACxC,KAAK,CAAC8B,OAAO,CAAC,CAACsB,KAAK,CAAEV,GAAG,IAAK,CAAC,CAACA,GAAG,CAAC,GAC9C,IAAI,GACJM,KAAK;EACnB,CAAC,MACI,IAAIrB,cAAc,CAACF,SAAS,CAAC,IAAIzB,KAAK,KAAK,YAAY,EAAE;IAC1D,OAAOgD,KAAK;EAChB,CAAC,MACI,IAAIjB,mCAAmC,CAACN,SAAS,CAAC,EAAE;IACrD,OAAO,CAACO,cAAc,CAAChC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,EAAEuB,UAAU,CAAC8B,yBAAyB,EAAE5B,SAAS,CAAC,CAAC,GAAGuB,KAAK,GAAG,IAAI;EAC7G;EACA,OAAO,CAAChB,cAAc,CAAChC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEuB,UAAU,CAAC8B,yBAAyB,EAAE5B,SAAS,CAAC,CAAC,GAAGuB,KAAK,GAAG,IAAI;AAC5G,CAAC;AACDjC,OAAO,CAACE,oBAAoB,GAAGA,oBAAoB;AACnDF,OAAO,CAACC,oBAAoB,GAAG;EAC3BsC,IAAI,EAAE,kBAAkB;EACxBC,OAAO,EAAExC,OAAO,CAACG,gBAAgB;EACjCsC,WAAW,EAAEzC,OAAO,CAACE,oBAAoB;EACzCwC,aAAa,EAAE1C,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}