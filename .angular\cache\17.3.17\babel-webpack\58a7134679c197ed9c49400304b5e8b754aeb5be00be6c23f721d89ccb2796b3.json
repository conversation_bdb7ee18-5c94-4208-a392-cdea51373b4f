{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateJsonInfo = exports.validateJsonSync = exports.validateJson = exports.shouldValidate = void 0;\nconst utils_1 = require(\"../../../utils\");\nconst error_1 = require(\"../../../error\");\nconst lodash_1 = require(\"lodash\");\nconst shouldValidate = context => {\n  var _a;\n  const {\n    component\n  } = context;\n  if (!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.json) || !(0, lodash_1.isObject)(component.validate.json)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateJson = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateJsonSync)(context);\n});\nexports.validateJson = validateJson;\nconst validateJsonSync = context => {\n  var _a;\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  const func = (_a = component === null || component === void 0 ? void 0 : component.validate) === null || _a === void 0 ? void 0 : _a.json;\n  const valid = (0, utils_1.evaluate)(func, context, 'valid', false, context => {\n    context.value = value || null;\n  });\n  if (valid === null) {\n    return null;\n  }\n  return valid === true ? null : new error_1.FieldError(valid || 'jsonLogic', Object.assign(Object.assign({}, context), {\n    setting: func\n  }), 'json');\n};\nexports.validateJsonSync = validateJsonSync;\nexports.validateJsonInfo = {\n  name: 'validateJson',\n  process: exports.validateJson,\n  processSync: exports.validateJsonSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateJsonInfo", "validateJsonSync", "validate<PERSON><PERSON>", "shouldValidate", "utils_1", "require", "error_1", "lodash_1", "context", "_a", "component", "validate", "json", "isObject", "func", "valid", "evaluate", "FieldError", "assign", "setting", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateJson.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateJsonInfo = exports.validateJsonSync = exports.validateJson = exports.shouldValidate = void 0;\nconst utils_1 = require(\"../../../utils\");\nconst error_1 = require(\"../../../error\");\nconst lodash_1 = require(\"lodash\");\nconst shouldValidate = (context) => {\n    var _a;\n    const { component } = context;\n    if (!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.json) || !(0, lodash_1.isObject)(component.validate.json)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateJson = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateJsonSync)(context);\n});\nexports.validateJson = validateJson;\nconst validateJsonSync = (context) => {\n    var _a;\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    const func = (_a = component === null || component === void 0 ? void 0 : component.validate) === null || _a === void 0 ? void 0 : _a.json;\n    const valid = (0, utils_1.evaluate)(func, context, 'valid', false, (context) => {\n        context.value = value || null;\n    });\n    if (valid === null) {\n        return null;\n    }\n    return valid === true\n        ? null\n        : new error_1.FieldError(valid || 'jsonLogic', Object.assign(Object.assign({}, context), { setting: func }), 'json');\n};\nexports.validateJsonSync = validateJsonSync;\nexports.validateJsonInfo = {\n    name: 'validateJson',\n    process: exports.validateJson,\n    processSync: exports.validateJsonSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,gBAAgB,GAAGD,OAAO,CAACE,gBAAgB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AAC5G,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,OAAO,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAME,QAAQ,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMF,cAAc,GAAIK,OAAO,IAAK;EAChC,IAAIC,EAAE;EACN,MAAM;IAAEC;EAAU,CAAC,GAAGF,OAAO;EAC7B,IAAI,EAAE,CAACC,EAAE,GAAGC,SAAS,CAACC,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEL,QAAQ,CAACM,QAAQ,EAAEH,SAAS,CAACC,QAAQ,CAACC,IAAI,CAAC,EAAE;IAC/H,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDb,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,YAAY,GAAIM,OAAO,IAAK9B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC7E,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,gBAAgB,EAAEO,OAAO,CAAC;AACjD,CAAC,CAAC;AACFT,OAAO,CAACG,YAAY,GAAGA,YAAY;AACnC,MAAMD,gBAAgB,GAAIO,OAAO,IAAK;EAClC,IAAIC,EAAE;EACN,MAAM;IAAEC,SAAS;IAAE1B;EAAM,CAAC,GAAGwB,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAET,OAAO,CAACI,cAAc,EAAEK,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,MAAMM,IAAI,GAAG,CAACL,EAAE,GAAGC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI;EACzI,MAAMG,KAAK,GAAG,CAAC,CAAC,EAAEX,OAAO,CAACY,QAAQ,EAAEF,IAAI,EAAEN,OAAO,EAAE,OAAO,EAAE,KAAK,EAAGA,OAAO,IAAK;IAC5EA,OAAO,CAACxB,KAAK,GAAGA,KAAK,IAAI,IAAI;EACjC,CAAC,CAAC;EACF,IAAI+B,KAAK,KAAK,IAAI,EAAE;IAChB,OAAO,IAAI;EACf;EACA,OAAOA,KAAK,KAAK,IAAI,GACf,IAAI,GACJ,IAAIT,OAAO,CAACW,UAAU,CAACF,KAAK,IAAI,WAAW,EAAElB,MAAM,CAACqB,MAAM,CAACrB,MAAM,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAEV,OAAO,CAAC,EAAE;IAAEW,OAAO,EAAEL;EAAK,CAAC,CAAC,EAAE,MAAM,CAAC;AAC5H,CAAC;AACDf,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3CF,OAAO,CAACC,gBAAgB,GAAG;EACvBoB,IAAI,EAAE,cAAc;EACpBC,OAAO,EAAEtB,OAAO,CAACG,YAAY;EAC7BoB,WAAW,EAAEvB,OAAO,CAACE,gBAAgB;EACrCsB,aAAa,EAAExB,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}