{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.hideChildrenProcessorInfo = exports.hideChildrenProcessorAsync = exports.hideChildrenProcessor = void 0;\nconst formUtil_1 = require(\"../utils/formUtil\");\n/**\n * This processor function checks components for the `hidden` property and, if children are present, sets them to hidden as well.\n */\nconst hideChildrenProcessor = context => {\n  var _a, _b, _c;\n  const {\n    component,\n    path,\n    parent,\n    scope\n  } = context;\n  // Check if there's a conditional set for the component and if it's marked as conditionally hidden\n  const isConditionallyHidden = (_a = scope.conditionals) === null || _a === void 0 ? void 0 : _a.find(cond => {\n    return path === cond.path && cond.conditionallyHidden;\n  });\n  if (!scope.conditionals) {\n    scope.conditionals = [];\n  }\n  if (isConditionallyHidden || ((_b = parent === null || parent === void 0 ? void 0 : parent.scope) === null || _b === void 0 ? void 0 : _b.conditionallyHidden)) {\n    (0, formUtil_1.setComponentScope)(component, 'conditionallyHidden', true);\n  }\n  if (component.hasOwnProperty('hidden') && !!component.hidden || ((_c = parent === null || parent === void 0 ? void 0 : parent.scope) === null || _c === void 0 ? void 0 : _c.intentionallyHidden)) {\n    (0, formUtil_1.setComponentScope)(component, 'intentionallyHidden', true);\n  }\n};\nexports.hideChildrenProcessor = hideChildrenProcessor;\nconst hideChildrenProcessorAsync = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.hideChildrenProcessor)(context);\n});\nexports.hideChildrenProcessorAsync = hideChildrenProcessorAsync;\nexports.hideChildrenProcessorInfo = {\n  name: 'hideChildren',\n  shouldProcess: () => true,\n  processSync: exports.hideChildrenProcessor,\n  process: exports.hideChildrenProcessorAsync\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "hideChildrenProcessorInfo", "hideChildrenProcessorAsync", "hideChildrenProcessor", "formUtil_1", "require", "context", "_a", "_b", "_c", "component", "path", "parent", "scope", "isConditionallyHidden", "conditionals", "find", "cond", "conditionally<PERSON><PERSON><PERSON>", "setComponentScope", "hasOwnProperty", "hidden", "intentionallyHidden", "name", "shouldProcess", "processSync", "process"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/hideChildren.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.hideChildrenProcessorInfo = exports.hideChildrenProcessorAsync = exports.hideChildrenProcessor = void 0;\nconst formUtil_1 = require(\"../utils/formUtil\");\n/**\n * This processor function checks components for the `hidden` property and, if children are present, sets them to hidden as well.\n */\nconst hideChildrenProcessor = (context) => {\n    var _a, _b, _c;\n    const { component, path, parent, scope } = context;\n    // Check if there's a conditional set for the component and if it's marked as conditionally hidden\n    const isConditionallyHidden = (_a = scope.conditionals) === null || _a === void 0 ? void 0 : _a.find((cond) => {\n        return path === cond.path && cond.conditionallyHidden;\n    });\n    if (!scope.conditionals) {\n        scope.conditionals = [];\n    }\n    if (isConditionallyHidden || ((_b = parent === null || parent === void 0 ? void 0 : parent.scope) === null || _b === void 0 ? void 0 : _b.conditionallyHidden)) {\n        (0, formUtil_1.setComponentScope)(component, 'conditionallyHidden', true);\n    }\n    if ((component.hasOwnProperty('hidden') && !!component.hidden) ||\n        ((_c = parent === null || parent === void 0 ? void 0 : parent.scope) === null || _c === void 0 ? void 0 : _c.intentionallyHidden)) {\n        (0, formUtil_1.setComponentScope)(component, 'intentionallyHidden', true);\n    }\n};\nexports.hideChildrenProcessor = hideChildrenProcessor;\nconst hideChildrenProcessorAsync = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.hideChildrenProcessor)(context);\n});\nexports.hideChildrenProcessorAsync = hideChildrenProcessorAsync;\nexports.hideChildrenProcessorInfo = {\n    name: 'hideChildren',\n    shouldProcess: () => true,\n    processSync: exports.hideChildrenProcessor,\n    process: exports.hideChildrenProcessorAsync,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,yBAAyB,GAAGD,OAAO,CAACE,0BAA0B,GAAGF,OAAO,CAACG,qBAAqB,GAAG,KAAK,CAAC;AAC/G,MAAMC,UAAU,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAC/C;AACA;AACA;AACA,MAAMF,qBAAqB,GAAIG,OAAO,IAAK;EACvC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,MAAM;IAAEC,SAAS;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGP,OAAO;EAClD;EACA,MAAMQ,qBAAqB,GAAG,CAACP,EAAE,GAAGM,KAAK,CAACE,YAAY,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,IAAI,CAAEC,IAAI,IAAK;IAC3G,OAAON,IAAI,KAAKM,IAAI,CAACN,IAAI,IAAIM,IAAI,CAACC,mBAAmB;EACzD,CAAC,CAAC;EACF,IAAI,CAACL,KAAK,CAACE,YAAY,EAAE;IACrBF,KAAK,CAACE,YAAY,GAAG,EAAE;EAC3B;EACA,IAAID,qBAAqB,KAAK,CAACN,EAAE,GAAGI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,KAAK,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,mBAAmB,CAAC,EAAE;IAC5J,CAAC,CAAC,EAAEd,UAAU,CAACe,iBAAiB,EAAET,SAAS,EAAE,qBAAqB,EAAE,IAAI,CAAC;EAC7E;EACA,IAAKA,SAAS,CAACU,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAACV,SAAS,CAACW,MAAM,KACxD,CAACZ,EAAE,GAAGG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,KAAK,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,mBAAmB,CAAC,EAAE;IACnI,CAAC,CAAC,EAAElB,UAAU,CAACe,iBAAiB,EAAET,SAAS,EAAE,qBAAqB,EAAE,IAAI,CAAC;EAC7E;AACJ,CAAC;AACDV,OAAO,CAACG,qBAAqB,GAAGA,qBAAqB;AACrD,MAAMD,0BAA0B,GAAII,OAAO,IAAK3B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC3F,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACG,qBAAqB,EAAEG,OAAO,CAAC;AACtD,CAAC,CAAC;AACFN,OAAO,CAACE,0BAA0B,GAAGA,0BAA0B;AAC/DF,OAAO,CAACC,yBAAyB,GAAG;EAChCsB,IAAI,EAAE,cAAc;EACpBC,aAAa,EAAEA,CAAA,KAAM,IAAI;EACzBC,WAAW,EAAEzB,OAAO,CAACG,qBAAqB;EAC1CuB,OAAO,EAAE1B,OAAO,CAACE;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}