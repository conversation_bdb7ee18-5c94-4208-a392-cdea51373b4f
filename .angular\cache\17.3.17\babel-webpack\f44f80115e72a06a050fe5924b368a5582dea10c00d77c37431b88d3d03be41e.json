{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.processOneSync = exports.processOne = void 0;\nconst lodash_1 = require(\"lodash\");\nconst types_1 = require(\"../types\");\nconst formUtil_1 = require(\"../utils/formUtil\");\nfunction processOne(context) {\n  return __awaiter(this, void 0, void 0, function* () {\n    const {\n      processors,\n      component,\n      paths,\n      local,\n      path\n    } = context;\n    // Create a getter for `value` that is always derived from the current data object\n    if (typeof context.value === 'undefined') {\n      const dataPath = local ? (paths === null || paths === void 0 ? void 0 : paths.localDataPath) || path : (paths === null || paths === void 0 ? void 0 : paths.dataPath) || path;\n      Object.defineProperty(context, 'value', {\n        enumerable: true,\n        get() {\n          const modelType = (0, formUtil_1.getModelType)(component);\n          if (!component.type || modelType === 'none' || modelType === 'content') {\n            return undefined;\n          }\n          return (0, lodash_1.get)(context.data, dataPath);\n        },\n        set(newValue) {\n          const modelType = (0, formUtil_1.getModelType)(component);\n          if (!component.type || modelType === 'none' || modelType === 'content') {\n            // Do not set the value if the model type is 'none' or 'content'\n            return;\n          }\n          (0, lodash_1.set)(context.data, dataPath, newValue);\n        }\n      });\n    }\n    context.processor = types_1.ProcessorType.Custom;\n    for (const processor of processors) {\n      if (processor === null || processor === void 0 ? void 0 : processor.process) {\n        yield processor.process(context);\n      }\n    }\n  });\n}\nexports.processOne = processOne;\nfunction processOneSync(context) {\n  const {\n    processors,\n    component,\n    paths,\n    local,\n    path\n  } = context;\n  // Create a getter for `value` that is always derived from the current data object\n  if (typeof context.value === 'undefined') {\n    const dataPath = local ? (paths === null || paths === void 0 ? void 0 : paths.localDataPath) || path : (paths === null || paths === void 0 ? void 0 : paths.dataPath) || path;\n    Object.defineProperty(context, 'value', {\n      enumerable: true,\n      get() {\n        const modelType = (0, formUtil_1.getModelType)(component);\n        if (!component.type || modelType === 'none' || modelType === 'content') {\n          return undefined;\n        }\n        return (0, lodash_1.get)(context.data, dataPath);\n      },\n      set(newValue) {\n        const modelType = (0, formUtil_1.getModelType)(component);\n        if (!component.type || modelType === 'none' || modelType === 'content') {\n          // Do not set the value if the model type is 'none' or 'content'\n          return;\n        }\n        (0, lodash_1.set)(context.data, dataPath, newValue);\n      }\n    });\n  }\n  // Process the components.\n  context.processor = types_1.ProcessorType.Custom;\n  for (const processor of processors) {\n    if (processor === null || processor === void 0 ? void 0 : processor.processSync) {\n      processor.processSync(context);\n    }\n  }\n}\nexports.processOneSync = processOneSync;", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "processOneSync", "processOne", "lodash_1", "require", "types_1", "formUtil_1", "context", "processors", "component", "paths", "local", "path", "dataPath", "localDataPath", "enumerable", "get", "modelType", "getModelType", "type", "undefined", "data", "set", "newValue", "processor", "ProcessorType", "Custom", "process", "processSync"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/processOne.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.processOneSync = exports.processOne = void 0;\nconst lodash_1 = require(\"lodash\");\nconst types_1 = require(\"../types\");\nconst formUtil_1 = require(\"../utils/formUtil\");\nfunction processOne(context) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const { processors, component, paths, local, path } = context;\n        // Create a getter for `value` that is always derived from the current data object\n        if (typeof context.value === 'undefined') {\n            const dataPath = local ? (paths === null || paths === void 0 ? void 0 : paths.localDataPath) || path : (paths === null || paths === void 0 ? void 0 : paths.dataPath) || path;\n            Object.defineProperty(context, 'value', {\n                enumerable: true,\n                get() {\n                    const modelType = (0, formUtil_1.getModelType)(component);\n                    if (!component.type || modelType === 'none' || modelType === 'content') {\n                        return undefined;\n                    }\n                    return (0, lodash_1.get)(context.data, dataPath);\n                },\n                set(newValue) {\n                    const modelType = (0, formUtil_1.getModelType)(component);\n                    if (!component.type || modelType === 'none' || modelType === 'content') {\n                        // Do not set the value if the model type is 'none' or 'content'\n                        return;\n                    }\n                    (0, lodash_1.set)(context.data, dataPath, newValue);\n                },\n            });\n        }\n        context.processor = types_1.ProcessorType.Custom;\n        for (const processor of processors) {\n            if (processor === null || processor === void 0 ? void 0 : processor.process) {\n                yield processor.process(context);\n            }\n        }\n    });\n}\nexports.processOne = processOne;\nfunction processOneSync(context) {\n    const { processors, component, paths, local, path } = context;\n    // Create a getter for `value` that is always derived from the current data object\n    if (typeof context.value === 'undefined') {\n        const dataPath = local ? (paths === null || paths === void 0 ? void 0 : paths.localDataPath) || path : (paths === null || paths === void 0 ? void 0 : paths.dataPath) || path;\n        Object.defineProperty(context, 'value', {\n            enumerable: true,\n            get() {\n                const modelType = (0, formUtil_1.getModelType)(component);\n                if (!component.type || modelType === 'none' || modelType === 'content') {\n                    return undefined;\n                }\n                return (0, lodash_1.get)(context.data, dataPath);\n            },\n            set(newValue) {\n                const modelType = (0, formUtil_1.getModelType)(component);\n                if (!component.type || modelType === 'none' || modelType === 'content') {\n                    // Do not set the value if the model type is 'none' or 'content'\n                    return;\n                }\n                (0, lodash_1.set)(context.data, dataPath, newValue);\n            },\n        });\n    }\n    // Process the components.\n    context.processor = types_1.ProcessorType.Custom;\n    for (const processor of processors) {\n        if (processor === null || processor === void 0 ? void 0 : processor.processSync) {\n            processor.processSync(context);\n        }\n    }\n}\nexports.processOneSync = processOneSync;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACE,UAAU,GAAG,KAAK,CAAC;AACpD,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,OAAO,GAAGD,OAAO,CAAC,UAAU,CAAC;AACnC,MAAME,UAAU,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AAC/C,SAASF,UAAUA,CAACK,OAAO,EAAE;EACzB,OAAO5B,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAChD,MAAM;MAAE6B,UAAU;MAAEC,SAAS;MAAEC,KAAK;MAAEC,KAAK;MAAEC;IAAK,CAAC,GAAGL,OAAO;IAC7D;IACA,IAAI,OAAOA,OAAO,CAACtB,KAAK,KAAK,WAAW,EAAE;MACtC,MAAM4B,QAAQ,GAAGF,KAAK,GAAG,CAACD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,aAAa,KAAKF,IAAI,GAAG,CAACF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,QAAQ,KAAKD,IAAI;MAC7Kd,MAAM,CAACC,cAAc,CAACQ,OAAO,EAAE,OAAO,EAAE;QACpCQ,UAAU,EAAE,IAAI;QAChBC,GAAGA,CAAA,EAAG;UACF,MAAMC,SAAS,GAAG,CAAC,CAAC,EAAEX,UAAU,CAACY,YAAY,EAAET,SAAS,CAAC;UACzD,IAAI,CAACA,SAAS,CAACU,IAAI,IAAIF,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,SAAS,EAAE;YACpE,OAAOG,SAAS;UACpB;UACA,OAAO,CAAC,CAAC,EAAEjB,QAAQ,CAACa,GAAG,EAAET,OAAO,CAACc,IAAI,EAAER,QAAQ,CAAC;QACpD,CAAC;QACDS,GAAGA,CAACC,QAAQ,EAAE;UACV,MAAMN,SAAS,GAAG,CAAC,CAAC,EAAEX,UAAU,CAACY,YAAY,EAAET,SAAS,CAAC;UACzD,IAAI,CAACA,SAAS,CAACU,IAAI,IAAIF,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,SAAS,EAAE;YACpE;YACA;UACJ;UACA,CAAC,CAAC,EAAEd,QAAQ,CAACmB,GAAG,EAAEf,OAAO,CAACc,IAAI,EAAER,QAAQ,EAAEU,QAAQ,CAAC;QACvD;MACJ,CAAC,CAAC;IACN;IACAhB,OAAO,CAACiB,SAAS,GAAGnB,OAAO,CAACoB,aAAa,CAACC,MAAM;IAChD,KAAK,MAAMF,SAAS,IAAIhB,UAAU,EAAE;MAChC,IAAIgB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,OAAO,EAAE;QACzE,MAAMH,SAAS,CAACG,OAAO,CAACpB,OAAO,CAAC;MACpC;IACJ;EACJ,CAAC,CAAC;AACN;AACAP,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/B,SAASD,cAAcA,CAACM,OAAO,EAAE;EAC7B,MAAM;IAAEC,UAAU;IAAEC,SAAS;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGL,OAAO;EAC7D;EACA,IAAI,OAAOA,OAAO,CAACtB,KAAK,KAAK,WAAW,EAAE;IACtC,MAAM4B,QAAQ,GAAGF,KAAK,GAAG,CAACD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,aAAa,KAAKF,IAAI,GAAG,CAACF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,QAAQ,KAAKD,IAAI;IAC7Kd,MAAM,CAACC,cAAc,CAACQ,OAAO,EAAE,OAAO,EAAE;MACpCQ,UAAU,EAAE,IAAI;MAChBC,GAAGA,CAAA,EAAG;QACF,MAAMC,SAAS,GAAG,CAAC,CAAC,EAAEX,UAAU,CAACY,YAAY,EAAET,SAAS,CAAC;QACzD,IAAI,CAACA,SAAS,CAACU,IAAI,IAAIF,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,SAAS,EAAE;UACpE,OAAOG,SAAS;QACpB;QACA,OAAO,CAAC,CAAC,EAAEjB,QAAQ,CAACa,GAAG,EAAET,OAAO,CAACc,IAAI,EAAER,QAAQ,CAAC;MACpD,CAAC;MACDS,GAAGA,CAACC,QAAQ,EAAE;QACV,MAAMN,SAAS,GAAG,CAAC,CAAC,EAAEX,UAAU,CAACY,YAAY,EAAET,SAAS,CAAC;QACzD,IAAI,CAACA,SAAS,CAACU,IAAI,IAAIF,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,SAAS,EAAE;UACpE;UACA;QACJ;QACA,CAAC,CAAC,EAAEd,QAAQ,CAACmB,GAAG,EAAEf,OAAO,CAACc,IAAI,EAAER,QAAQ,EAAEU,QAAQ,CAAC;MACvD;IACJ,CAAC,CAAC;EACN;EACA;EACAhB,OAAO,CAACiB,SAAS,GAAGnB,OAAO,CAACoB,aAAa,CAACC,MAAM;EAChD,KAAK,MAAMF,SAAS,IAAIhB,UAAU,EAAE;IAChC,IAAIgB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,WAAW,EAAE;MAC7EJ,SAAS,CAACI,WAAW,CAACrB,OAAO,CAAC;IAClC;EACJ;AACJ;AACAP,OAAO,CAACC,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}