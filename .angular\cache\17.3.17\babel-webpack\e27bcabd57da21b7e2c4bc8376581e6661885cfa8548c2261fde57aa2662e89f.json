{"ast": null, "code": "var basePick = require('./_basePick'),\n  flatRest = require('./_flatRest');\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function (object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\nmodule.exports = pick;", "map": {"version": 3, "names": ["base<PERSON>ick", "require", "flatRest", "pick", "object", "paths", "module", "exports"], "sources": ["D:/workspace/formtest_aug/node_modules/lodash/pick.js"], "sourcesContent": ["var basePick = require('./_basePick'),\n    flatRest = require('./_flatRest');\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function(object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\n\nmodule.exports = pick;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,QAAQ,GAAGD,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,IAAI,GAAGD,QAAQ,CAAC,UAASE,MAAM,EAAEC,KAAK,EAAE;EAC1C,OAAOD,MAAM,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGJ,QAAQ,CAACI,MAAM,EAAEC,KAAK,CAAC;AACtD,CAAC,CAAC;AAEFC,MAAM,CAACC,OAAO,GAAGJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}