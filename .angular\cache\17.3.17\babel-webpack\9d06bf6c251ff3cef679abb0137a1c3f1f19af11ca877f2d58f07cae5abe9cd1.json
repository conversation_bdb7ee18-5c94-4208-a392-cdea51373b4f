{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { FormioModule } from '@formio/angular';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormBuilderComponent } from './form-builder/form-builder.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, FormioModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, FormBuilderComponent],\n    imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, FormioModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "ReactiveFormsModule", "FormioModule", "AppRoutingModule", "AppComponent", "FormBuilderComponent", "AppModule", "bootstrap", "declarations", "imports"], "sources": ["D:\\workspace\\formtest_aug\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { FormioModule } from '@formio/angular';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormBuilderComponent } from './form-builder/form-builder.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    FormBuilderComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    FormioModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})\nexport class AppModule { }"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,uCAAuC;;AAkB5E,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAHRH,YAAY;IAAA;EAAA;;;gBAPtBL,aAAa,EACbI,gBAAgB,EAChBH,WAAW,EACXC,mBAAmB,EACnBC,YAAY;IAAA;EAAA;;;2EAMHI,SAAS;IAAAE,YAAA,GAdlBJ,YAAY,EACZC,oBAAoB;IAAAI,OAAA,GAGpBV,aAAa,EACbI,gBAAgB,EAChBH,WAAW,EACXC,mBAAmB,EACnBC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}