{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getErrorMessage = void 0;\nfunction getErrorMessage(error) {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return String(error);\n}\nexports.getErrorMessage = getErrorMessage;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getErrorMessage", "error", "Error", "message", "String"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/utils/error.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getErrorMessage = void 0;\nfunction getErrorMessage(error) {\n    if (error instanceof Error) {\n        return error.message;\n    }\n    return String(error);\n}\nexports.getErrorMessage = getErrorMessage;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAChC,SAASA,eAAeA,CAACC,KAAK,EAAE;EAC5B,IAAIA,KAAK,YAAYC,KAAK,EAAE;IACxB,OAAOD,KAAK,CAACE,OAAO;EACxB;EACA,OAAOC,MAAM,CAACH,KAAK,CAAC;AACxB;AACAH,OAAO,CAACE,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}