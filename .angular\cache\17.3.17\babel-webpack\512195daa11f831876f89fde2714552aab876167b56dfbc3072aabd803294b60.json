{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateValuePropertyInfo = exports.validateValuePropertySync = exports.validateValueProperty = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableListComponent = comp => {\n  return comp && comp.type && comp.type === 'selectboxes';\n};\nconst shouldValidate = context => {\n  var _a;\n  const {\n    component,\n    instance\n  } = context;\n  if (!isValidatableListComponent(component)) {\n    return false;\n  }\n  if (component.dataSrc !== 'url') {\n    return false;\n  }\n  if ((_a = instance === null || instance === void 0 ? void 0 : instance.options) === null || _a === void 0 ? void 0 : _a.building) {\n    return true;\n  }\n  return false;\n};\nexports.shouldValidate = shouldValidate;\nconst validateValueProperty = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateValuePropertySync)(context);\n});\nexports.validateValueProperty = validateValueProperty;\nconst validateValuePropertySync = context => {\n  var _a;\n  const {\n    value,\n    instance\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  const error = new error_1.FieldError('invalidValueProperty', context);\n  if (Object.entries(value).some(([key, value]) => value && (key === '[object Object]' || key === 'true' || key === 'false')) || instance && ((_a = instance.loadedOptions) === null || _a === void 0 ? void 0 : _a.some(option => option.invalid))) {\n    return error;\n  }\n  return null;\n};\nexports.validateValuePropertySync = validateValuePropertySync;\nexports.validateValuePropertyInfo = {\n  name: 'validateValueProperty',\n  process: exports.validateValueProperty,\n  processSync: exports.validateValuePropertySync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateValuePropertyInfo", "validateValuePropertySync", "validateV<PERSON>ueProperty", "shouldValidate", "error_1", "require", "isValidatableListComponent", "comp", "type", "context", "_a", "component", "instance", "dataSrc", "options", "building", "error", "FieldError", "entries", "some", "key", "loadedOptions", "option", "invalid", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateValueProperty.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateValuePropertyInfo = exports.validateValuePropertySync = exports.validateValueProperty = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableListComponent = (comp) => {\n    return comp && comp.type && comp.type === 'selectboxes';\n};\nconst shouldValidate = (context) => {\n    var _a;\n    const { component, instance } = context;\n    if (!isValidatableListComponent(component)) {\n        return false;\n    }\n    if (component.dataSrc !== 'url') {\n        return false;\n    }\n    if ((_a = instance === null || instance === void 0 ? void 0 : instance.options) === null || _a === void 0 ? void 0 : _a.building) {\n        return true;\n    }\n    return false;\n};\nexports.shouldValidate = shouldValidate;\nconst validateValueProperty = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateValuePropertySync)(context);\n});\nexports.validateValueProperty = validateValueProperty;\nconst validateValuePropertySync = (context) => {\n    var _a;\n    const { value, instance } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    const error = new error_1.FieldError('invalidValueProperty', context);\n    if (Object.entries(value).some(([key, value]) => value && (key === '[object Object]' || key === 'true' || key === 'false')) ||\n        (instance && ((_a = instance.loadedOptions) === null || _a === void 0 ? void 0 : _a.some((option) => option.invalid)))) {\n        return error;\n    }\n    return null;\n};\nexports.validateValuePropertySync = validateValuePropertySync;\nexports.validateValuePropertyInfo = {\n    name: 'validateValueProperty',\n    process: exports.validateValueProperty,\n    processSync: exports.validateValuePropertySync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,yBAAyB,GAAGD,OAAO,CAACE,yBAAyB,GAAGF,OAAO,CAACG,qBAAqB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACvI,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,0BAA0B,GAAIC,IAAI,IAAK;EACzC,OAAOA,IAAI,IAAIA,IAAI,CAACC,IAAI,IAAID,IAAI,CAACC,IAAI,KAAK,aAAa;AAC3D,CAAC;AACD,MAAML,cAAc,GAAIM,OAAO,IAAK;EAChC,IAAIC,EAAE;EACN,MAAM;IAAEC,SAAS;IAAEC;EAAS,CAAC,GAAGH,OAAO;EACvC,IAAI,CAACH,0BAA0B,CAACK,SAAS,CAAC,EAAE;IACxC,OAAO,KAAK;EAChB;EACA,IAAIA,SAAS,CAACE,OAAO,KAAK,KAAK,EAAE;IAC7B,OAAO,KAAK;EAChB;EACA,IAAI,CAACH,EAAE,GAAGE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,OAAO,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,QAAQ,EAAE;IAC9H,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACDhB,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,qBAAqB,GAAIO,OAAO,IAAK/B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACtF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,yBAAyB,EAAEQ,OAAO,CAAC;AAC1D,CAAC,CAAC;AACFV,OAAO,CAACG,qBAAqB,GAAGA,qBAAqB;AACrD,MAAMD,yBAAyB,GAAIQ,OAAO,IAAK;EAC3C,IAAIC,EAAE;EACN,MAAM;IAAE1B,KAAK;IAAE4B;EAAS,CAAC,GAAGH,OAAO;EACnC,IAAI,CAAC,CAAC,CAAC,EAAEV,OAAO,CAACI,cAAc,EAAEM,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,MAAMO,KAAK,GAAG,IAAIZ,OAAO,CAACa,UAAU,CAAC,sBAAsB,EAAER,OAAO,CAAC;EACrE,IAAIZ,MAAM,CAACqB,OAAO,CAAClC,KAAK,CAAC,CAACmC,IAAI,CAAC,CAAC,CAACC,GAAG,EAAEpC,KAAK,CAAC,KAAKA,KAAK,KAAKoC,GAAG,KAAK,iBAAiB,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,OAAO,CAAC,CAAC,IACtHR,QAAQ,KAAK,CAACF,EAAE,GAAGE,QAAQ,CAACS,aAAa,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,IAAI,CAAEG,MAAM,IAAKA,MAAM,CAACC,OAAO,CAAC,CAAE,EAAE;IACxH,OAAOP,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDjB,OAAO,CAACE,yBAAyB,GAAGA,yBAAyB;AAC7DF,OAAO,CAACC,yBAAyB,GAAG;EAChCwB,IAAI,EAAE,uBAAuB;EAC7BC,OAAO,EAAE1B,OAAO,CAACG,qBAAqB;EACtCwB,WAAW,EAAE3B,OAAO,CAACE,yBAAyB;EAC9C0B,aAAa,EAAE5B,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}