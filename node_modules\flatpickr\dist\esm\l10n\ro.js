var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var Romanian = {
    weekdays: {
        shorthand: ["Du<PERSON>", "<PERSON>n", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>ă",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "<PERSON>",
            "<PERSON>",
            "Apr",
            "<PERSON>",
            "<PERSON>un",
            "<PERSON><PERSON>",
            "Aug",
            "Sep",
            "Oct",
            "Noi",
            "Dec",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "August",
            "Sept<PERSON>brie",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
        ],
    },
    firstDayOfWeek: 1,
    time_24hr: true,
    ordinal: function () {
        return "";
    },
};
fp.l10ns.ro = Romanian;
export default fp.l10ns;
