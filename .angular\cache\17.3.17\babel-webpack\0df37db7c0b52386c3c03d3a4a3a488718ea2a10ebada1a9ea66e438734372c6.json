{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.filterProcessInfo = exports.filterPostProcess = exports.filterProcess = exports.filterProcessSync = void 0;\nconst lodash_1 = require(\"lodash\");\nconst lodash_2 = require(\"lodash\");\nconst formUtil_1 = require(\"../../utils/formUtil\");\nconst filterProcessSync = context => {\n  const {\n    scope,\n    component,\n    path\n  } = context;\n  const {\n    value\n  } = context;\n  if (!scope.filter) scope.filter = {};\n  if (value !== undefined) {\n    const modelType = (0, formUtil_1.getModelType)(component);\n    switch (modelType) {\n      case 'dataObject':\n        scope.filter[path] = {\n          compModelType: modelType,\n          include: true,\n          value: {\n            data: {}\n          }\n        };\n        break;\n      case 'nestedArray':\n        scope.filter[path] = {\n          compModelType: modelType,\n          include: true,\n          value: []\n        };\n        break;\n      case 'nestedDataArray':\n        scope.filter[path] = {\n          compModelType: modelType,\n          include: true,\n          value: Array.isArray(value) ? value.map(v => Object.assign(Object.assign({}, v), {\n            data: {}\n          })) : []\n        };\n        break;\n      case 'object':\n        scope.filter[path] = {\n          compModelType: modelType,\n          include: true,\n          value: component.type === 'address' ? false : {}\n        };\n        break;\n      default:\n        scope.filter[path] = {\n          compModelType: modelType,\n          include: true\n        };\n        break;\n    }\n  }\n};\nexports.filterProcessSync = filterProcessSync;\nconst filterProcess = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.filterProcessSync)(context);\n});\nexports.filterProcess = filterProcess;\nconst filterPostProcess = context => {\n  var _a;\n  const {\n    scope,\n    submission\n  } = context;\n  const filtered = {};\n  for (const path in scope.filter) {\n    if (scope.filter[path].include) {\n      let value = (0, lodash_2.get)(submission === null || submission === void 0 ? void 0 : submission.data, path);\n      if (scope.filter[path].value) {\n        if ((0, lodash_2.isObject)(value) && ((_a = scope.filter[path].value) === null || _a === void 0 ? void 0 : _a.data)) {\n          value = Object.assign(Object.assign({}, value), scope.filter[path].value);\n        } else {\n          value = scope.filter[path].value;\n        }\n      }\n      (0, lodash_1.set)(filtered, path, value);\n    }\n  }\n  context.data = filtered;\n};\nexports.filterPostProcess = filterPostProcess;\nexports.filterProcessInfo = {\n  name: 'filter',\n  process: exports.filterProcess,\n  processSync: exports.filterProcessSync,\n  postProcess: exports.filterPostProcess,\n  shouldProcess: () => true\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "filterProcessInfo", "filterPostProcess", "filterProcess", "filterProcessSync", "lodash_1", "require", "lodash_2", "formUtil_1", "context", "scope", "component", "path", "filter", "undefined", "modelType", "getModelType", "compModelType", "include", "data", "Array", "isArray", "map", "v", "assign", "type", "_a", "submission", "filtered", "get", "isObject", "set", "name", "process", "processSync", "postProcess", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/filter/index.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.filterProcessInfo = exports.filterPostProcess = exports.filterProcess = exports.filterProcessSync = void 0;\nconst lodash_1 = require(\"lodash\");\nconst lodash_2 = require(\"lodash\");\nconst formUtil_1 = require(\"../../utils/formUtil\");\nconst filterProcessSync = (context) => {\n    const { scope, component, path } = context;\n    const { value } = context;\n    if (!scope.filter)\n        scope.filter = {};\n    if (value !== undefined) {\n        const modelType = (0, formUtil_1.getModelType)(component);\n        switch (modelType) {\n            case 'dataObject':\n                scope.filter[path] = {\n                    compModelType: modelType,\n                    include: true,\n                    value: { data: {} },\n                };\n                break;\n            case 'nestedArray':\n                scope.filter[path] = {\n                    compModelType: modelType,\n                    include: true,\n                    value: [],\n                };\n                break;\n            case 'nestedDataArray':\n                scope.filter[path] = {\n                    compModelType: modelType,\n                    include: true,\n                    value: Array.isArray(value) ? value.map((v) => (Object.assign(Object.assign({}, v), { data: {} }))) : [],\n                };\n                break;\n            case 'object':\n                scope.filter[path] = {\n                    compModelType: modelType,\n                    include: true,\n                    value: component.type === 'address' ? false : {},\n                };\n                break;\n            default:\n                scope.filter[path] = {\n                    compModelType: modelType,\n                    include: true,\n                };\n                break;\n        }\n    }\n};\nexports.filterProcessSync = filterProcessSync;\nconst filterProcess = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.filterProcessSync)(context);\n});\nexports.filterProcess = filterProcess;\nconst filterPostProcess = (context) => {\n    var _a;\n    const { scope, submission } = context;\n    const filtered = {};\n    for (const path in scope.filter) {\n        if (scope.filter[path].include) {\n            let value = (0, lodash_2.get)(submission === null || submission === void 0 ? void 0 : submission.data, path);\n            if (scope.filter[path].value) {\n                if ((0, lodash_2.isObject)(value) && ((_a = scope.filter[path].value) === null || _a === void 0 ? void 0 : _a.data)) {\n                    value = Object.assign(Object.assign({}, value), scope.filter[path].value);\n                }\n                else {\n                    value = scope.filter[path].value;\n                }\n            }\n            (0, lodash_1.set)(filtered, path, value);\n        }\n    }\n    context.data = filtered;\n};\nexports.filterPostProcess = filterPostProcess;\nexports.filterProcessInfo = {\n    name: 'filter',\n    process: exports.filterProcess,\n    processSync: exports.filterProcessSync,\n    postProcess: exports.filterPostProcess,\n    shouldProcess: () => true,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,iBAAiB,GAAGD,OAAO,CAACE,iBAAiB,GAAGF,OAAO,CAACG,aAAa,GAAGH,OAAO,CAACI,iBAAiB,GAAG,KAAK,CAAC;AAClH,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAME,UAAU,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAClD,MAAMF,iBAAiB,GAAIK,OAAO,IAAK;EACnC,MAAM;IAAEC,KAAK;IAAEC,SAAS;IAAEC;EAAK,CAAC,GAAGH,OAAO;EAC1C,MAAM;IAAExB;EAAM,CAAC,GAAGwB,OAAO;EACzB,IAAI,CAACC,KAAK,CAACG,MAAM,EACbH,KAAK,CAACG,MAAM,GAAG,CAAC,CAAC;EACrB,IAAI5B,KAAK,KAAK6B,SAAS,EAAE;IACrB,MAAMC,SAAS,GAAG,CAAC,CAAC,EAAEP,UAAU,CAACQ,YAAY,EAAEL,SAAS,CAAC;IACzD,QAAQI,SAAS;MACb,KAAK,YAAY;QACbL,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,GAAG;UACjBK,aAAa,EAAEF,SAAS;UACxBG,OAAO,EAAE,IAAI;UACbjC,KAAK,EAAE;YAAEkC,IAAI,EAAE,CAAC;UAAE;QACtB,CAAC;QACD;MACJ,KAAK,aAAa;QACdT,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,GAAG;UACjBK,aAAa,EAAEF,SAAS;UACxBG,OAAO,EAAE,IAAI;UACbjC,KAAK,EAAE;QACX,CAAC;QACD;MACJ,KAAK,iBAAiB;QAClByB,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,GAAG;UACjBK,aAAa,EAAEF,SAAS;UACxBG,OAAO,EAAE,IAAI;UACbjC,KAAK,EAAEmC,KAAK,CAACC,OAAO,CAACpC,KAAK,CAAC,GAAGA,KAAK,CAACqC,GAAG,CAAEC,CAAC,IAAMzB,MAAM,CAAC0B,MAAM,CAAC1B,MAAM,CAAC0B,MAAM,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,EAAE;YAAEJ,IAAI,EAAE,CAAC;UAAE,CAAC,CAAE,CAAC,GAAG;QAC1G,CAAC;QACD;MACJ,KAAK,QAAQ;QACTT,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,GAAG;UACjBK,aAAa,EAAEF,SAAS;UACxBG,OAAO,EAAE,IAAI;UACbjC,KAAK,EAAE0B,SAAS,CAACc,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,CAAC;QACnD,CAAC;QACD;MACJ;QACIf,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,GAAG;UACjBK,aAAa,EAAEF,SAAS;UACxBG,OAAO,EAAE;QACb,CAAC;QACD;IACR;EACJ;AACJ,CAAC;AACDlB,OAAO,CAACI,iBAAiB,GAAGA,iBAAiB;AAC7C,MAAMD,aAAa,GAAIM,OAAO,IAAK9B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC9E,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACI,iBAAiB,EAAEK,OAAO,CAAC;AAClD,CAAC,CAAC;AACFT,OAAO,CAACG,aAAa,GAAGA,aAAa;AACrC,MAAMD,iBAAiB,GAAIO,OAAO,IAAK;EACnC,IAAIiB,EAAE;EACN,MAAM;IAAEhB,KAAK;IAAEiB;EAAW,CAAC,GAAGlB,OAAO;EACrC,MAAMmB,QAAQ,GAAG,CAAC,CAAC;EACnB,KAAK,MAAMhB,IAAI,IAAIF,KAAK,CAACG,MAAM,EAAE;IAC7B,IAAIH,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,CAACM,OAAO,EAAE;MAC5B,IAAIjC,KAAK,GAAG,CAAC,CAAC,EAAEsB,QAAQ,CAACsB,GAAG,EAAEF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACR,IAAI,EAAEP,IAAI,CAAC;MAC5G,IAAIF,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,CAAC3B,KAAK,EAAE;QAC1B,IAAI,CAAC,CAAC,EAAEsB,QAAQ,CAACuB,QAAQ,EAAE7C,KAAK,CAAC,KAAK,CAACyC,EAAE,GAAGhB,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,CAAC3B,KAAK,MAAM,IAAI,IAAIyC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACP,IAAI,CAAC,EAAE;UACjHlC,KAAK,GAAGa,MAAM,CAAC0B,MAAM,CAAC1B,MAAM,CAAC0B,MAAM,CAAC,CAAC,CAAC,EAAEvC,KAAK,CAAC,EAAEyB,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,CAAC3B,KAAK,CAAC;QAC7E,CAAC,MACI;UACDA,KAAK,GAAGyB,KAAK,CAACG,MAAM,CAACD,IAAI,CAAC,CAAC3B,KAAK;QACpC;MACJ;MACA,CAAC,CAAC,EAAEoB,QAAQ,CAAC0B,GAAG,EAAEH,QAAQ,EAAEhB,IAAI,EAAE3B,KAAK,CAAC;IAC5C;EACJ;EACAwB,OAAO,CAACU,IAAI,GAAGS,QAAQ;AAC3B,CAAC;AACD5B,OAAO,CAACE,iBAAiB,GAAGA,iBAAiB;AAC7CF,OAAO,CAACC,iBAAiB,GAAG;EACxB+B,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAEjC,OAAO,CAACG,aAAa;EAC9B+B,WAAW,EAAElC,OAAO,CAACI,iBAAiB;EACtC+B,WAAW,EAAEnC,OAAO,CAACE,iBAAiB;EACtCkC,aAAa,EAAEA,CAAA,KAAM;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}