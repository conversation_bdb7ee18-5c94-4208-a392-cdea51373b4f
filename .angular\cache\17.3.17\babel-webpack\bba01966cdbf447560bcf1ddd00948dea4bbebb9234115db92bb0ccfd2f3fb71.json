{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMinimumYearInfo = exports.validateMinimumYearSync = exports.validateMinimumYear = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableDayComponent = component => {\n  var _a;\n  return component && (component === null || component === void 0 ? void 0 : component.type) === 'day' && (component.hasOwnProperty('minYear') || ((_a = component.year) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('minYear')));\n};\nconst shouldValidate = context => {\n  var _a, _b;\n  const {\n    component\n  } = context;\n  if (!isValidatableDayComponent(component)) {\n    return false;\n  }\n  if (!component.minYear && !((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.year) === null || _b === void 0 ? void 0 : _b.minYear)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMinimumYear = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMinimumYearSync)(context);\n});\nexports.validateMinimumYear = validateMinimumYear;\nconst validateMinimumYearSync = context => {\n  var _a, _b;\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  if (typeof value !== 'string' && typeof value !== 'number') {\n    throw new error_1.ProcessorError(`Cannot validate minimum year for value ${value}`, context, 'validate:validateMinimumYear');\n  }\n  const testValue = typeof value === 'string' ? value : String(value);\n  const testArr = /\\d{4}$/.exec(testValue);\n  const year = testArr ? testArr[0] : null;\n  if (component.minYear && ((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.year) === null || _b === void 0 ? void 0 : _b.minYear) && component.minYear !== component.fields.year.minYear) {\n    throw new error_1.ProcessorError('Cannot validate minimum year, component.minYear and component.fields.year.minYear are not equal', context, 'validate:validateMinimumYear');\n  }\n  const minYear = component.minYear;\n  if (!minYear || !year) {\n    return null;\n  }\n  return +year >= +minYear ? null : new error_1.FieldError('minYear', Object.assign(Object.assign({}, context), {\n    minYear: String(minYear),\n    setting: String(minYear)\n  }));\n};\nexports.validateMinimumYearSync = validateMinimumYearSync;\nexports.validateMinimumYearInfo = {\n  name: 'validateMinimumYear',\n  process: exports.validateMinimumYear,\n  processSync: exports.validateMinimumYearSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateMinimumYearInfo", "validateMinimumYearSync", "validateMinimumYear", "shouldValidate", "error_1", "require", "isValidatableDayComponent", "component", "_a", "type", "hasOwnProperty", "year", "context", "_b", "minYear", "fields", "ProcessorError", "testValue", "String", "testArr", "exec", "FieldError", "assign", "setting", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMinimumYear.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMinimumYearInfo = exports.validateMinimumYearSync = exports.validateMinimumYear = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableDayComponent = (component) => {\n    var _a;\n    return (component &&\n        (component === null || component === void 0 ? void 0 : component.type) === 'day' &&\n        (component.hasOwnProperty('minYear') || ((_a = component.year) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('minYear'))));\n};\nconst shouldValidate = (context) => {\n    var _a, _b;\n    const { component } = context;\n    if (!isValidatableDayComponent(component)) {\n        return false;\n    }\n    if (!component.minYear && !((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.year) === null || _b === void 0 ? void 0 : _b.minYear)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMinimumYear = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMinimumYearSync)(context);\n});\nexports.validateMinimumYear = validateMinimumYear;\nconst validateMinimumYearSync = (context) => {\n    var _a, _b;\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    if (typeof value !== 'string' && typeof value !== 'number') {\n        throw new error_1.ProcessorError(`Cannot validate minimum year for value ${value}`, context, 'validate:validateMinimumYear');\n    }\n    const testValue = typeof value === 'string' ? value : String(value);\n    const testArr = /\\d{4}$/.exec(testValue);\n    const year = testArr ? testArr[0] : null;\n    if (component.minYear &&\n        ((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.year) === null || _b === void 0 ? void 0 : _b.minYear) &&\n        component.minYear !== component.fields.year.minYear) {\n        throw new error_1.ProcessorError('Cannot validate minimum year, component.minYear and component.fields.year.minYear are not equal', context, 'validate:validateMinimumYear');\n    }\n    const minYear = component.minYear;\n    if (!minYear || !year) {\n        return null;\n    }\n    return +year >= +minYear\n        ? null\n        : new error_1.FieldError('minYear', Object.assign(Object.assign({}, context), { minYear: String(minYear), setting: String(minYear) }));\n};\nexports.validateMinimumYearSync = validateMinimumYearSync;\nexports.validateMinimumYearInfo = {\n    name: 'validateMinimumYear',\n    process: exports.validateMinimumYear,\n    processSync: exports.validateMinimumYearSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,uBAAuB,GAAGD,OAAO,CAACE,uBAAuB,GAAGF,OAAO,CAACG,mBAAmB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACjI,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,yBAAyB,GAAIC,SAAS,IAAK;EAC7C,IAAIC,EAAE;EACN,OAAQD,SAAS,IACb,CAACA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,IAAI,MAAM,KAAK,KAC/EF,SAAS,CAACG,cAAc,CAAC,SAAS,CAAC,KAAK,CAACF,EAAE,GAAGD,SAAS,CAACI,IAAI,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;AAC1I,CAAC;AACD,MAAMP,cAAc,GAAIS,OAAO,IAAK;EAChC,IAAIJ,EAAE,EAAEK,EAAE;EACV,MAAM;IAAEN;EAAU,CAAC,GAAGK,OAAO;EAC7B,IAAI,CAACN,yBAAyB,CAACC,SAAS,CAAC,EAAE;IACvC,OAAO,KAAK;EAChB;EACA,IAAI,CAACA,SAAS,CAACO,OAAO,IAAI,EAAE,CAACD,EAAE,GAAG,CAACL,EAAE,GAAGD,SAAS,CAACQ,MAAM,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,OAAO,CAAC,EAAE;IACtJ,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDf,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,mBAAmB,GAAIU,OAAO,IAAKlC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACpF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,uBAAuB,EAAEW,OAAO,CAAC;AACxD,CAAC,CAAC;AACFb,OAAO,CAACG,mBAAmB,GAAGA,mBAAmB;AACjD,MAAMD,uBAAuB,GAAIW,OAAO,IAAK;EACzC,IAAIJ,EAAE,EAAEK,EAAE;EACV,MAAM;IAAEN,SAAS;IAAEvB;EAAM,CAAC,GAAG4B,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAEb,OAAO,CAACI,cAAc,EAAES,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAI,OAAO5B,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACxD,MAAM,IAAIoB,OAAO,CAACY,cAAc,CAAC,0CAA0ChC,KAAK,EAAE,EAAE4B,OAAO,EAAE,8BAA8B,CAAC;EAChI;EACA,MAAMK,SAAS,GAAG,OAAOjC,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGkC,MAAM,CAAClC,KAAK,CAAC;EACnE,MAAMmC,OAAO,GAAG,QAAQ,CAACC,IAAI,CAACH,SAAS,CAAC;EACxC,MAAMN,IAAI,GAAGQ,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;EACxC,IAAIZ,SAAS,CAACO,OAAO,KAChB,CAACD,EAAE,GAAG,CAACL,EAAE,GAAGD,SAAS,CAACQ,MAAM,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,OAAO,CAAC,IAC7HP,SAAS,CAACO,OAAO,KAAKP,SAAS,CAACQ,MAAM,CAACJ,IAAI,CAACG,OAAO,EAAE;IACrD,MAAM,IAAIV,OAAO,CAACY,cAAc,CAAC,iGAAiG,EAAEJ,OAAO,EAAE,8BAA8B,CAAC;EAChL;EACA,MAAME,OAAO,GAAGP,SAAS,CAACO,OAAO;EACjC,IAAI,CAACA,OAAO,IAAI,CAACH,IAAI,EAAE;IACnB,OAAO,IAAI;EACf;EACA,OAAO,CAACA,IAAI,IAAI,CAACG,OAAO,GAClB,IAAI,GACJ,IAAIV,OAAO,CAACiB,UAAU,CAAC,SAAS,EAAExB,MAAM,CAACyB,MAAM,CAACzB,MAAM,CAACyB,MAAM,CAAC,CAAC,CAAC,EAAEV,OAAO,CAAC,EAAE;IAAEE,OAAO,EAAEI,MAAM,CAACJ,OAAO,CAAC;IAAES,OAAO,EAAEL,MAAM,CAACJ,OAAO;EAAE,CAAC,CAAC,CAAC;AAC9I,CAAC;AACDf,OAAO,CAACE,uBAAuB,GAAGA,uBAAuB;AACzDF,OAAO,CAACC,uBAAuB,GAAG;EAC9BwB,IAAI,EAAE,qBAAqB;EAC3BC,OAAO,EAAE1B,OAAO,CAACG,mBAAmB;EACpCwB,WAAW,EAAE3B,OAAO,CAACE,uBAAuB;EAC5C0B,aAAa,EAAE5B,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}