{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.dereferenceProcessInfo = exports.dereferenceProcess = void 0;\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\nconst isDereferenceableDataTableComponent = component => {\n  var _a, _b, _c;\n  return component && component.type === 'datatable' && ((_a = component.fetch) === null || _a === void 0 ? void 0 : _a.enableFetch) === true && ((_b = component.fetch) === null || _b === void 0 ? void 0 : _b.dataSrc) === 'resource' && typeof ((_c = component.fetch) === null || _c === void 0 ? void 0 : _c.resource) === 'string';\n};\n/**\n * This function is used to dereference reference IDs contained in the form.\n * It is currently only compatible with Data Table components.\n * @todo Add support for other components (if applicable) and for submission data dereferencing (e.g. save-as-reference, currently a property action).\n */\nconst dereferenceProcess = context => __awaiter(void 0, void 0, void 0, function* () {\n  var _a;\n  const {\n    component,\n    config,\n    scope,\n    path\n  } = context;\n  if (!scope.dereference) {\n    scope.dereference = {};\n  }\n  if (!isDereferenceableDataTableComponent(component)) {\n    return;\n  }\n  if (!(config === null || config === void 0 ? void 0 : config.database)) {\n    throw new error_1.ProcessorError('Cannot dereference resource value without a database config object', context, 'dereference');\n  }\n  try {\n    const components = yield (_a = config.database) === null || _a === void 0 ? void 0 : _a.dereferenceDataTableComponent(component);\n    const vmCompatibleComponents = (0, utils_1.fastCloneDeep)(components);\n    scope.dereference[path] = vmCompatibleComponents;\n    // Modify the components in place; we have to do this now as opposed to a \"post-processor\" step because\n    // eachComponentDataAsync will immediately turn around and introspect these components in the case of Data Table\n    component.components = vmCompatibleComponents;\n  } catch (err) {\n    throw new error_1.ProcessorError(err.message || err, context, 'dereference');\n  }\n});\nexports.dereferenceProcess = dereferenceProcess;\nexports.dereferenceProcessInfo = {\n  name: 'dereference',\n  shouldProcess: () => true,\n  process: exports.dereferenceProcess\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "dereferenceProcessInfo", "dereferenceProcess", "error_1", "require", "utils_1", "isDereferenceableDataTableComponent", "component", "_a", "_b", "_c", "type", "fetch", "enableFetch", "dataSrc", "resource", "context", "config", "scope", "path", "dereference", "database", "ProcessorError", "components", "dereferenceDataTableComponent", "vmCompatibleComponents", "fastCloneDeep", "err", "message", "name", "shouldProcess", "process"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/dereference/index.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.dereferenceProcessInfo = exports.dereferenceProcess = void 0;\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\nconst isDereferenceableDataTableComponent = (component) => {\n    var _a, _b, _c;\n    return (component &&\n        component.type === 'datatable' &&\n        ((_a = component.fetch) === null || _a === void 0 ? void 0 : _a.enableFetch) === true &&\n        ((_b = component.fetch) === null || _b === void 0 ? void 0 : _b.dataSrc) === 'resource' &&\n        typeof ((_c = component.fetch) === null || _c === void 0 ? void 0 : _c.resource) === 'string');\n};\n/**\n * This function is used to dereference reference IDs contained in the form.\n * It is currently only compatible with Data Table components.\n * @todo Add support for other components (if applicable) and for submission data dereferencing (e.g. save-as-reference, currently a property action).\n */\nconst dereferenceProcess = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    const { component, config, scope, path } = context;\n    if (!scope.dereference) {\n        scope.dereference = {};\n    }\n    if (!isDereferenceableDataTableComponent(component)) {\n        return;\n    }\n    if (!(config === null || config === void 0 ? void 0 : config.database)) {\n        throw new error_1.ProcessorError('Cannot dereference resource value without a database config object', context, 'dereference');\n    }\n    try {\n        const components = yield ((_a = config.database) === null || _a === void 0 ? void 0 : _a.dereferenceDataTableComponent(component));\n        const vmCompatibleComponents = (0, utils_1.fastCloneDeep)(components);\n        scope.dereference[path] = vmCompatibleComponents;\n        // Modify the components in place; we have to do this now as opposed to a \"post-processor\" step because\n        // eachComponentDataAsync will immediately turn around and introspect these components in the case of Data Table\n        component.components = vmCompatibleComponents;\n    }\n    catch (err) {\n        throw new error_1.ProcessorError(err.message || err, context, 'dereference');\n    }\n});\nexports.dereferenceProcess = dereferenceProcess;\nexports.dereferenceProcessInfo = {\n    name: 'dereference',\n    shouldProcess: () => true,\n    process: exports.dereferenceProcess,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,sBAAsB,GAAGD,OAAO,CAACE,kBAAkB,GAAG,KAAK,CAAC;AACpE,MAAMC,OAAO,GAAGC,OAAO,CAAC,aAAa,CAAC;AACtC,MAAMC,OAAO,GAAGD,OAAO,CAAC,aAAa,CAAC;AACtC,MAAME,mCAAmC,GAAIC,SAAS,IAAK;EACvD,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,OAAQH,SAAS,IACbA,SAAS,CAACI,IAAI,KAAK,WAAW,IAC9B,CAAC,CAACH,EAAE,GAAGD,SAAS,CAACK,KAAK,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,WAAW,MAAM,IAAI,IACrF,CAAC,CAACJ,EAAE,GAAGF,SAAS,CAACK,KAAK,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,OAAO,MAAM,UAAU,IACvF,QAAQ,CAACJ,EAAE,GAAGH,SAAS,CAACK,KAAK,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,QAAQ,CAAC,KAAK,QAAQ;AACrG,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMb,kBAAkB,GAAIc,OAAO,IAAKrC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACnF,IAAI6B,EAAE;EACN,MAAM;IAAED,SAAS;IAAEU,MAAM;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGH,OAAO;EAClD,IAAI,CAACE,KAAK,CAACE,WAAW,EAAE;IACpBF,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC;EAC1B;EACA,IAAI,CAACd,mCAAmC,CAACC,SAAS,CAAC,EAAE;IACjD;EACJ;EACA,IAAI,EAAEU,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACI,QAAQ,CAAC,EAAE;IACpE,MAAM,IAAIlB,OAAO,CAACmB,cAAc,CAAC,oEAAoE,EAAEN,OAAO,EAAE,aAAa,CAAC;EAClI;EACA,IAAI;IACA,MAAMO,UAAU,GAAG,MAAO,CAACf,EAAE,GAAGS,MAAM,CAACI,QAAQ,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,6BAA6B,CAACjB,SAAS,CAAE;IAClI,MAAMkB,sBAAsB,GAAG,CAAC,CAAC,EAAEpB,OAAO,CAACqB,aAAa,EAAEH,UAAU,CAAC;IACrEL,KAAK,CAACE,WAAW,CAACD,IAAI,CAAC,GAAGM,sBAAsB;IAChD;IACA;IACAlB,SAAS,CAACgB,UAAU,GAAGE,sBAAsB;EACjD,CAAC,CACD,OAAOE,GAAG,EAAE;IACR,MAAM,IAAIxB,OAAO,CAACmB,cAAc,CAACK,GAAG,CAACC,OAAO,IAAID,GAAG,EAAEX,OAAO,EAAE,aAAa,CAAC;EAChF;AACJ,CAAC,CAAC;AACFhB,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/CF,OAAO,CAACC,sBAAsB,GAAG;EAC7B4B,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAEA,CAAA,KAAM,IAAI;EACzBC,OAAO,EAAE/B,OAAO,CAACE;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}