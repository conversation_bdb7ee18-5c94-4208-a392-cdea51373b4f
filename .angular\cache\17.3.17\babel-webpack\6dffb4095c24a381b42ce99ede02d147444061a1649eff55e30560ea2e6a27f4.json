{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMaximumDayInfo = exports.validateMaximumDaySync = exports.validateMaximumDay = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst date_1 = require(\"../../../utils/date\");\nconst isValidatableDayComponent = component => {\n  return component && component.type === 'day' && component.hasOwnProperty('maxDate');\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!isValidatableDayComponent(component)) {\n    return false;\n  }\n  if ((0, date_1.isPartialDay)(component, value)) {\n    return false;\n  }\n  if (!(0, date_1.getDateSetting)(component.maxDate)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMaximumDay = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMaximumDaySync)(context);\n});\nexports.validateMaximumDay = validateMaximumDay;\nconst validateMaximumDaySync = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  if (typeof value !== 'string') {\n    throw new error_1.ProcessorError(`Cannot validate day value ${value} because it is not a string`, context, 'validate:validateMaximumDay');\n  }\n  // TODO: this validation probably goes for dates and days\n  const format = (0, date_1.getDateValidationFormat)(component);\n  const date = (0, date_1.dayjs)(value, format);\n  const maxDate = (0, date_1.getDateSetting)(component.maxDate);\n  if (maxDate === null) {\n    return null;\n  } else {\n    maxDate.setHours(0, 0, 0, 0);\n  }\n  const error = new error_1.FieldError('maxDate', Object.assign(Object.assign({}, context), {\n    maxDate: (0, date_1.dayjs)(maxDate).format((0, date_1.getDayFormat)(component)),\n    setting: String(maxDate)\n  }));\n  return date.isBefore((0, date_1.dayjs)(maxDate)) || date.isSame((0, date_1.dayjs)(maxDate)) ? null : error;\n};\nexports.validateMaximumDaySync = validateMaximumDaySync;\nexports.validateMaximumDayInfo = {\n  name: 'validateMaximumDay',\n  process: exports.validateMaximumDay,\n  processSync: exports.validateMaximumDaySync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateMaximumDayInfo", "validateMaximumDaySync", "validateMaximumDay", "shouldValidate", "error_1", "require", "date_1", "isValidatableDayComponent", "component", "type", "hasOwnProperty", "context", "isPartialDay", "getDateSetting", "maxDate", "ProcessorError", "format", "getDateValidationFormat", "date", "dayjs", "setHours", "error", "FieldError", "assign", "getDayFormat", "setting", "String", "isBefore", "isSame", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMaximumDay.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMaximumDayInfo = exports.validateMaximumDaySync = exports.validateMaximumDay = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst date_1 = require(\"../../../utils/date\");\nconst isValidatableDayComponent = (component) => {\n    return component && component.type === 'day' && component.hasOwnProperty('maxDate');\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!isValidatableDayComponent(component)) {\n        return false;\n    }\n    if ((0, date_1.isPartialDay)(component, value)) {\n        return false;\n    }\n    if (!(0, date_1.getDateSetting)(component.maxDate)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMaximumDay = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMaximumDaySync)(context);\n});\nexports.validateMaximumDay = validateMaximumDay;\nconst validateMaximumDaySync = (context) => {\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    if (typeof value !== 'string') {\n        throw new error_1.ProcessorError(`Cannot validate day value ${value} because it is not a string`, context, 'validate:validateMaximumDay');\n    }\n    // TODO: this validation probably goes for dates and days\n    const format = (0, date_1.getDateValidationFormat)(component);\n    const date = (0, date_1.dayjs)(value, format);\n    const maxDate = (0, date_1.getDateSetting)(component.maxDate);\n    if (maxDate === null) {\n        return null;\n    }\n    else {\n        maxDate.setHours(0, 0, 0, 0);\n    }\n    const error = new error_1.FieldError('maxDate', Object.assign(Object.assign({}, context), { maxDate: (0, date_1.dayjs)(maxDate).format((0, date_1.getDayFormat)(component)), setting: String(maxDate) }));\n    return date.isBefore((0, date_1.dayjs)(maxDate)) || date.isSame((0, date_1.dayjs)(maxDate)) ? null : error;\n};\nexports.validateMaximumDaySync = validateMaximumDaySync;\nexports.validateMaximumDayInfo = {\n    name: 'validateMaximumDay',\n    process: exports.validateMaximumDay,\n    processSync: exports.validateMaximumDaySync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,sBAAsB,GAAGD,OAAO,CAACE,sBAAsB,GAAGF,OAAO,CAACG,kBAAkB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AAC9H,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,MAAM,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAC7C,MAAME,yBAAyB,GAAIC,SAAS,IAAK;EAC7C,OAAOA,SAAS,IAAIA,SAAS,CAACC,IAAI,KAAK,KAAK,IAAID,SAAS,CAACE,cAAc,CAAC,SAAS,CAAC;AACvF,CAAC;AACD,MAAMP,cAAc,GAAIQ,OAAO,IAAK;EAChC,MAAM;IAAEH,SAAS;IAAExB;EAAM,CAAC,GAAG2B,OAAO;EACpC,IAAI,CAACJ,yBAAyB,CAACC,SAAS,CAAC,EAAE;IACvC,OAAO,KAAK;EAChB;EACA,IAAI,CAAC,CAAC,EAAEF,MAAM,CAACM,YAAY,EAAEJ,SAAS,EAAExB,KAAK,CAAC,EAAE;IAC5C,OAAO,KAAK;EAChB;EACA,IAAI,CAAC,CAAC,CAAC,EAAEsB,MAAM,CAACO,cAAc,EAAEL,SAAS,CAACM,OAAO,CAAC,EAAE;IAChD,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDf,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,kBAAkB,GAAIS,OAAO,IAAKjC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACnF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,sBAAsB,EAAEU,OAAO,CAAC;AACvD,CAAC,CAAC;AACFZ,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/C,MAAMD,sBAAsB,GAAIU,OAAO,IAAK;EACxC,MAAM;IAAEH,SAAS;IAAExB;EAAM,CAAC,GAAG2B,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAEZ,OAAO,CAACI,cAAc,EAAEQ,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAI,OAAO3B,KAAK,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIoB,OAAO,CAACW,cAAc,CAAC,6BAA6B/B,KAAK,6BAA6B,EAAE2B,OAAO,EAAE,6BAA6B,CAAC;EAC7I;EACA;EACA,MAAMK,MAAM,GAAG,CAAC,CAAC,EAAEV,MAAM,CAACW,uBAAuB,EAAET,SAAS,CAAC;EAC7D,MAAMU,IAAI,GAAG,CAAC,CAAC,EAAEZ,MAAM,CAACa,KAAK,EAAEnC,KAAK,EAAEgC,MAAM,CAAC;EAC7C,MAAMF,OAAO,GAAG,CAAC,CAAC,EAAER,MAAM,CAACO,cAAc,EAAEL,SAAS,CAACM,OAAO,CAAC;EAC7D,IAAIA,OAAO,KAAK,IAAI,EAAE;IAClB,OAAO,IAAI;EACf,CAAC,MACI;IACDA,OAAO,CAACM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChC;EACA,MAAMC,KAAK,GAAG,IAAIjB,OAAO,CAACkB,UAAU,CAAC,SAAS,EAAEzB,MAAM,CAAC0B,MAAM,CAAC1B,MAAM,CAAC0B,MAAM,CAAC,CAAC,CAAC,EAAEZ,OAAO,CAAC,EAAE;IAAEG,OAAO,EAAE,CAAC,CAAC,EAAER,MAAM,CAACa,KAAK,EAAEL,OAAO,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEV,MAAM,CAACkB,YAAY,EAAEhB,SAAS,CAAC,CAAC;IAAEiB,OAAO,EAAEC,MAAM,CAACZ,OAAO;EAAE,CAAC,CAAC,CAAC;EACzM,OAAOI,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,EAAErB,MAAM,CAACa,KAAK,EAAEL,OAAO,CAAC,CAAC,IAAII,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEtB,MAAM,CAACa,KAAK,EAAEL,OAAO,CAAC,CAAC,GAAG,IAAI,GAAGO,KAAK;AAC9G,CAAC;AACDtB,OAAO,CAACE,sBAAsB,GAAGA,sBAAsB;AACvDF,OAAO,CAACC,sBAAsB,GAAG;EAC7B6B,IAAI,EAAE,oBAAoB;EAC1BC,OAAO,EAAE/B,OAAO,CAACG,kBAAkB;EACnC6B,WAAW,EAAEhC,OAAO,CAACE,sBAAsB;EAC3C+B,aAAa,EAAEjC,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}