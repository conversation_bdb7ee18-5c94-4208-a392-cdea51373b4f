{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.EN_ERRORS = void 0;\nexports.EN_ERRORS = {\n  unsavedRowsError: 'Please save all rows before proceeding.',\n  invalidRowsError: 'Please correct invalid rows before proceeding.',\n  invalidRowError: 'Invalid row. Please correct it or delete.',\n  invalidOption: '{{field}} is an invalid value.',\n  invalidDay: '{{field}} is not a valid day.',\n  required: '{{field}} is required',\n  unique: '{{field}} must be unique',\n  array: '{{field}} must be an array',\n  array_nonempty: '{{field}} must be a non-empty array',\n  nonarray: '{{field}} must not be an array',\n  select: '{{field}} contains an invalid selection',\n  pattern: '{{field}} does not match the pattern {{pattern}}',\n  minLength: '{{field}} must have at least {{length}} characters.',\n  maxLength: '{{field}} must have no more than {{length}} characters.',\n  minWords: '{{field}} must have at least {{length}} words.',\n  maxWords: '{{field}} must have no more than {{length}} words.',\n  min: '{{field}} cannot be less than {{min}}.',\n  max: '{{field}} cannot be greater than {{max}}.',\n  maxDate: '{{field}} should not contain date after {{- maxDate}}',\n  minDate: '{{field}} should not contain date before {{- minDate}}',\n  maxYear: '{{field}} should not contain year greater than {{maxYear}}',\n  minSelectedCount: 'You must select at least {{minCount}} items',\n  maxSelectedCount: 'You may only select up to {{maxCount}} items',\n  minYear: '{{field}} should not contain year less than {{minYear}}',\n  invalid_email: '{{field}} must be a valid email.',\n  invalid_url: '{{field}} must be a valid url.',\n  invalid_regex: '{{field}} does not match the pattern {{regex}}.',\n  invalid_date: '{{field}} is not a valid date.',\n  invalid_day: '{{field}} is not a valid day.',\n  invalidValueProperty: 'Invalid Value Property',\n  mask: '{{field}} does not match the mask.',\n  valueIsNotAvailable: '{{ field }} is an invalid value.',\n  time: '{{field}} is not a valid time.',\n  invalidDate: '{{field}} is not a valid date',\n  number: '{{field}} is not a valid number.',\n  requiredDayField: '{{ field }} is required',\n  requiredMonthField: '{{ field }} is required',\n  requiredYearField: '{{ field }} is required'\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "EN_ERRORS", "unsavedRowsError", "invalidRowsError", "invalidRowError", "invalidOption", "invalidDay", "required", "unique", "array", "array_nonempty", "nonarray", "select", "pattern", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "minWords", "max<PERSON><PERSON>s", "min", "max", "maxDate", "minDate", "maxYear", "minSelectedCount", "maxSelectedCount", "minYear", "invalid_email", "invalid_url", "invalid_regex", "invalid_date", "invalid_day", "invalidValueProperty", "mask", "valueIsNotAvailable", "time", "invalidDate", "number", "requiredDayField", "requiredMonthField", "requiredYearField"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/i18n/en.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EN_ERRORS = void 0;\nexports.EN_ERRORS = {\n    unsavedRowsError: 'Please save all rows before proceeding.',\n    invalidRowsError: 'Please correct invalid rows before proceeding.',\n    invalidRowError: 'Invalid row. Please correct it or delete.',\n    invalidOption: '{{field}} is an invalid value.',\n    invalidDay: '{{field}} is not a valid day.',\n    required: '{{field}} is required',\n    unique: '{{field}} must be unique',\n    array: '{{field}} must be an array',\n    array_nonempty: '{{field}} must be a non-empty array',\n    nonarray: '{{field}} must not be an array',\n    select: '{{field}} contains an invalid selection',\n    pattern: '{{field}} does not match the pattern {{pattern}}',\n    minLength: '{{field}} must have at least {{length}} characters.',\n    maxLength: '{{field}} must have no more than {{length}} characters.',\n    minWords: '{{field}} must have at least {{length}} words.',\n    maxWords: '{{field}} must have no more than {{length}} words.',\n    min: '{{field}} cannot be less than {{min}}.',\n    max: '{{field}} cannot be greater than {{max}}.',\n    maxDate: '{{field}} should not contain date after {{- maxDate}}',\n    minDate: '{{field}} should not contain date before {{- minDate}}',\n    maxYear: '{{field}} should not contain year greater than {{maxYear}}',\n    minSelectedCount: 'You must select at least {{minCount}} items',\n    maxSelectedCount: 'You may only select up to {{maxCount}} items',\n    minYear: '{{field}} should not contain year less than {{minYear}}',\n    invalid_email: '{{field}} must be a valid email.',\n    invalid_url: '{{field}} must be a valid url.',\n    invalid_regex: '{{field}} does not match the pattern {{regex}}.',\n    invalid_date: '{{field}} is not a valid date.',\n    invalid_day: '{{field}} is not a valid day.',\n    invalidValueProperty: 'Invalid Value Property',\n    mask: '{{field}} does not match the mask.',\n    valueIsNotAvailable: '{{ field }} is an invalid value.',\n    time: '{{field}} is not a valid time.',\n    invalidDate: '{{field}} is not a valid date',\n    number: '{{field}} is not a valid number.',\n    requiredDayField: '{{ field }} is required',\n    requiredMonthField: '{{ field }} is required',\n    requiredYearField: '{{ field }} is required',\n};\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,SAAS,GAAG,KAAK,CAAC;AAC1BF,OAAO,CAACE,SAAS,GAAG;EAChBC,gBAAgB,EAAE,yCAAyC;EAC3DC,gBAAgB,EAAE,gDAAgD;EAClEC,eAAe,EAAE,2CAA2C;EAC5DC,aAAa,EAAE,gCAAgC;EAC/CC,UAAU,EAAE,+BAA+B;EAC3CC,QAAQ,EAAE,uBAAuB;EACjCC,MAAM,EAAE,0BAA0B;EAClCC,KAAK,EAAE,4BAA4B;EACnCC,cAAc,EAAE,qCAAqC;EACrDC,QAAQ,EAAE,gCAAgC;EAC1CC,MAAM,EAAE,yCAAyC;EACjDC,OAAO,EAAE,kDAAkD;EAC3DC,SAAS,EAAE,qDAAqD;EAChEC,SAAS,EAAE,yDAAyD;EACpEC,QAAQ,EAAE,gDAAgD;EAC1DC,QAAQ,EAAE,oDAAoD;EAC9DC,GAAG,EAAE,wCAAwC;EAC7CC,GAAG,EAAE,2CAA2C;EAChDC,OAAO,EAAE,uDAAuD;EAChEC,OAAO,EAAE,wDAAwD;EACjEC,OAAO,EAAE,4DAA4D;EACrEC,gBAAgB,EAAE,6CAA6C;EAC/DC,gBAAgB,EAAE,8CAA8C;EAChEC,OAAO,EAAE,yDAAyD;EAClEC,aAAa,EAAE,kCAAkC;EACjDC,WAAW,EAAE,gCAAgC;EAC7CC,aAAa,EAAE,iDAAiD;EAChEC,YAAY,EAAE,gCAAgC;EAC9CC,WAAW,EAAE,+BAA+B;EAC5CC,oBAAoB,EAAE,wBAAwB;EAC9CC,IAAI,EAAE,oCAAoC;EAC1CC,mBAAmB,EAAE,kCAAkC;EACvDC,IAAI,EAAE,gCAAgC;EACtCC,WAAW,EAAE,+BAA+B;EAC5CC,MAAM,EAAE,kCAAkC;EAC1CC,gBAAgB,EAAE,yBAAyB;EAC3CC,kBAAkB,EAAE,yBAAyB;EAC7CC,iBAAiB,EAAE;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}