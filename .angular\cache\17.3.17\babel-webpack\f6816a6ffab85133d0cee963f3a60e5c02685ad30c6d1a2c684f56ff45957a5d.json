{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.normalizeProcessInfo = exports.normalizeProcessSync = exports.normalizeProcess = void 0;\nconst lodash_1 = require(\"lodash\");\nconst dayjs_1 = __importDefault(require(\"dayjs\"));\nconst customParseFormat_1 = __importDefault(require(\"dayjs/plugin/customParseFormat\"));\ndayjs_1.default.extend(customParseFormat_1.default);\nconst isAddressComponent = component => component.type === 'address';\nconst isDayComponent = component => component.type === 'day';\nconst isEmailComponent = component => component.type === 'email';\nconst isRadioComponent = component => component.type === 'radio';\nconst isSelectComponent = component => component.type === 'select';\nconst isSelectBoxesComponent = component => component.type === 'selectboxes';\nconst isTagsComponent = component => component.type === 'tags';\nconst isTextFieldComponent = component => component.type === 'textfield';\nconst isTimeComponent = component => component.type === 'time';\nconst isNumberComponent = component => component.type === 'number';\nconst normalizeAddressComponentValue = (component, value) => {\n  if (!component.multiple && Boolean(component.enableManualMode) && value && !value.mode) {\n    return {\n      mode: 'autocomplete',\n      address: value\n    };\n  }\n  return value;\n};\nconst getLocaleDateFormatInfo = (locale = 'en') => {\n  const formatInfo = {};\n  const day = 21;\n  const exampleDate = new Date(2017, 11, day);\n  const localDateString = exampleDate.toLocaleDateString(locale);\n  formatInfo.dayFirst = localDateString.slice(0, 2) === day.toString();\n  return formatInfo;\n};\nconst getLocaleDayFirst = (component, form) => {\n  var _a;\n  if (component.useLocaleSettings) {\n    return getLocaleDateFormatInfo((_a = form.options) === null || _a === void 0 ? void 0 : _a.language).dayFirst;\n  }\n  return component.dayFirst;\n};\nconst normalizeDayComponentValue = (component, form, value) => {\n  // TODO: this is a quick and dirty port of the Day component's normalizeValue method, may need some updates\n  const valueMask = /^\\d{2}\\/\\d{2}\\/\\d{4}$/;\n  const isDayFirst = getLocaleDayFirst(component, form);\n  const showDay = !(0, lodash_1.get)(component, 'fields.day.hide', false);\n  const showMonth = !(0, lodash_1.get)(component, 'fields.month.hide', false);\n  const showYear = !(0, lodash_1.get)(component, 'fields.year.hide', false);\n  if (!value || valueMask.test(value)) {\n    return value;\n  }\n  const dateParts = [];\n  const valueParts = value.split('/');\n  const [DAY, MONTH, YEAR] = component.dayFirst ? [0, 1, 2] : [1, 0, 2];\n  const defaultValue = component.defaultValue ? component.defaultValue.split('/') : '';\n  let defaultDay = '';\n  let defaultMonth = '';\n  let defaultYear = '';\n  const getDayWithHiddenFields = parts => {\n    let DAY, MONTH, YEAR;\n    [DAY, MONTH, YEAR] = component.dayFirst ? [0, 1, 2] : [1, 0, 2];\n    if (!showDay) {\n      MONTH = MONTH === 0 ? 0 : MONTH - 1;\n      YEAR = YEAR - 1;\n      DAY = null;\n    }\n    if (!showMonth) {\n      if (!(0, lodash_1.isNull)(DAY)) {\n        DAY = DAY === 0 ? 0 : DAY - 1;\n      }\n      YEAR = YEAR - 1;\n      MONTH = null;\n    }\n    if (!showYear) {\n      YEAR = null;\n    }\n    return {\n      month: (0, lodash_1.isNull)(MONTH) ? '' : parts[MONTH],\n      day: (0, lodash_1.isNull)(DAY) ? '' : parts[DAY],\n      year: (0, lodash_1.isNull)(YEAR) ? '' : parts[YEAR]\n    };\n  };\n  const getNextPart = (shouldTake, defaultValue) => {\n    // Only push the part if it's not an empty string\n    const part = shouldTake ? valueParts.shift() : defaultValue;\n    if (part !== '') {\n      dateParts.push(part);\n    }\n  };\n  if (defaultValue) {\n    const hasHiddenFields = defaultValue.length !== 3;\n    defaultDay = hasHiddenFields ? getDayWithHiddenFields(defaultValue).day : defaultValue[DAY];\n    defaultMonth = hasHiddenFields ? getDayWithHiddenFields(defaultValue).month : defaultValue[MONTH];\n    defaultYear = hasHiddenFields ? getDayWithHiddenFields(defaultValue).year : defaultValue[YEAR];\n  }\n  if (isDayFirst) {\n    getNextPart(showDay, defaultDay);\n  }\n  getNextPart(showMonth, defaultMonth);\n  if (!isDayFirst) {\n    getNextPart(showDay, defaultDay);\n  }\n  getNextPart(showYear, defaultYear);\n  return dateParts.join('/');\n};\nconst normalizeRadioComponentValue = (value, dataType) => {\n  switch (dataType) {\n    case 'number':\n      return +value;\n    case 'string':\n      return typeof value === 'object' ? JSON.stringify(value) : String(value);\n    case 'boolean':\n      return !(!value || value.toString() === 'false');\n  }\n  const isEquivalent = (0, lodash_1.toString)(value) === Number(value).toString();\n  if (!isNaN(parseFloat(value)) && isFinite(value) && isEquivalent) {\n    return +value;\n  }\n  if (value === 'true') {\n    return true;\n  }\n  if (value === 'false') {\n    return false;\n  }\n  return value;\n};\nconst normalizeSingleSelectComponentValue = (component, value) => {\n  if ((0, lodash_1.isNil)(value)) {\n    return;\n  }\n  const valueIsObject = (0, lodash_1.isObject)(value);\n  //check if value equals to default emptyValue\n  if (valueIsObject && Object.keys(value).length === 0) {\n    return value;\n  }\n  const dataType = component.dataType || 'auto';\n  const normalize = {\n    value,\n    number() {\n      const numberValue = Number(this.value);\n      const isEquivalent = value.toString() === numberValue.toString();\n      if (!Number.isNaN(numberValue) && Number.isFinite(numberValue) && value !== '' && isEquivalent) {\n        this.value = numberValue;\n      }\n      return this;\n    },\n    boolean() {\n      if ((0, lodash_1.isString)(this.value) && (this.value.toLowerCase() === 'true' || this.value.toLowerCase() === 'false')) {\n        this.value = this.value.toLowerCase() === 'true';\n      }\n      return this;\n    },\n    string() {\n      this.value = String(this.value);\n      return this;\n    },\n    object() {\n      return this;\n    },\n    auto() {\n      if ((0, lodash_1.isObject)(this.value)) {\n        this.value = this.object().value;\n      } else {\n        this.value = this.string().number().boolean().value;\n      }\n      return this;\n    }\n  };\n  try {\n    return normalize[dataType]().value;\n  } catch (err) {\n    console.warn('Failed to normalize value', err);\n    return value;\n  }\n};\nconst normalizeSelectComponentValue = (component, value) => {\n  if (component.multiple && Array.isArray(value)) {\n    return value.map(singleValue => normalizeSingleSelectComponentValue(component, singleValue));\n  }\n  return normalizeSingleSelectComponentValue(component, value);\n};\nconst normalizeSelectBoxesComponentValue = value => {\n  if (!value) {\n    value = {};\n  }\n  if (typeof value !== 'object') {\n    if (typeof value === 'string') {\n      return {\n        [value]: true\n      };\n    } else {\n      return {};\n    }\n  }\n  if (Array.isArray(value)) {\n    return value.reduce((acc, curr) => {\n      return Object.assign(Object.assign({}, acc), {\n        [curr]: true\n      });\n    }, {});\n  }\n  return value;\n};\nconst normalizeTagsComponentValue = (component, value) => {\n  const delimiter = component.delimeter || ',';\n  if ((!component.hasOwnProperty('storeas') || component.storeas === 'string') && Array.isArray(value)) {\n    return value.join(delimiter);\n  } else if (component.storeas === 'array' && typeof value === 'string') {\n    return value.split(delimiter).filter(result => result);\n  }\n  return value;\n};\nconst normalizeMaskValue = (component, defaultValues, value, path) => {\n  if (component.inputMasks && component.inputMasks.length > 0) {\n    if (!value || typeof value !== 'object') {\n      return {\n        value: value,\n        maskName: component.inputMasks[0].label\n      };\n    }\n    if (!value.value) {\n      const defaultValue = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.find(defaultValue => defaultValue.path === path);\n      value.value = Array.isArray(defaultValue) && defaultValue.length > 0 ? defaultValue[0] : defaultValue;\n    }\n  }\n  return value;\n};\nconst normalizeTextFieldComponentValue = (component, defaultValues, value, path) => {\n  // If the component has truncate multiple spaces enabled, then normalize the value to remove extra spaces.\n  if (component.truncateMultipleSpaces && typeof value === 'string') {\n    value = value.trim().replace(/\\s{2,}/g, ' ');\n  }\n  if (component.allowMultipleMasks && component.inputMasks && component.inputMasks.length > 0) {\n    if (Array.isArray(value)) {\n      return value.map(val => normalizeMaskValue(component, defaultValues, val, path));\n    } else {\n      return normalizeMaskValue(component, defaultValues, value, path);\n    }\n  }\n  return value;\n};\n// Allow submissions of time components in their visual \"format\" property by coercing them to the \"dataFormat\" property\n// i.e. \"HH:mm\" -> \"HH:mm:ss\"\nconst normalizeTimeComponentValue = (component, value) => {\n  const defaultDataFormat = 'HH:mm:ss';\n  const defaultFormat = 'HH:mm';\n  if ((0, dayjs_1.default)(value, component.format || defaultFormat, true).isValid()) {\n    return (0, dayjs_1.default)(value, component.format || defaultFormat, true).format(component.dataFormat || defaultDataFormat);\n  }\n  return value;\n};\nconst normalizeSingleNumberComponentValue = (component, value) => {\n  if (!isNaN(parseFloat(value)) && isFinite(value)) {\n    return +value;\n  }\n  return value;\n};\nconst normalizeNumberComponentValue = (component, value) => {\n  if (component.multiple && Array.isArray(value)) {\n    return value.map(singleValue => normalizeSingleNumberComponentValue(component, singleValue));\n  }\n  return normalizeSingleNumberComponentValue(component, value);\n};\nconst normalizeProcess = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.normalizeProcessSync)(context);\n});\nexports.normalizeProcess = normalizeProcess;\nconst normalizeProcessSync = context => {\n  const {\n    component,\n    form,\n    scope,\n    path,\n    data,\n    value\n  } = context;\n  if (!scope.normalize) {\n    scope.normalize = {};\n  }\n  const {\n    defaultValues\n  } = scope;\n  scope.normalize[path] = {\n    type: component.type,\n    normalized: false\n  };\n  // First check for component-type-specific transformations\n  if (isAddressComponent(component)) {\n    (0, lodash_1.set)(data, path, normalizeAddressComponentValue(component, value));\n    scope.normalize[path].normalized = true;\n  } else if (isDayComponent(component)) {\n    (0, lodash_1.set)(data, path, normalizeDayComponentValue(component, form, value));\n    scope.normalize[path].normalized = true;\n  } else if (isEmailComponent(component)) {\n    if (value && typeof value === 'string') {\n      (0, lodash_1.set)(data, path, value.toLowerCase());\n      scope.normalize[path].normalized = true;\n    }\n  } else if (isRadioComponent(component)) {\n    (0, lodash_1.set)(data, path, normalizeRadioComponentValue(value, component.dataType));\n    scope.normalize[path].normalized = true;\n  } else if (isSelectComponent(component)) {\n    (0, lodash_1.set)(data, path, normalizeSelectComponentValue(component, value));\n    scope.normalize[path].normalized = true;\n  } else if (isSelectBoxesComponent(component)) {\n    (0, lodash_1.set)(data, path, normalizeSelectBoxesComponentValue(value));\n    scope.normalize[path].normalized = true;\n  } else if (isTagsComponent(component)) {\n    (0, lodash_1.set)(data, path, normalizeTagsComponentValue(component, value));\n    scope.normalize[path].normalized = true;\n  } else if (isTextFieldComponent(component)) {\n    (0, lodash_1.set)(data, path, normalizeTextFieldComponentValue(component, defaultValues, value, path));\n    scope.normalize[path].normalized = true;\n  } else if (isTimeComponent(component)) {\n    (0, lodash_1.set)(data, path, normalizeTimeComponentValue(component, value));\n    scope.normalize[path].normalized = true;\n  } else if (isNumberComponent(component)) {\n    (0, lodash_1.set)(data, path, normalizeNumberComponentValue(component, value));\n    scope.normalize[path].normalized = true;\n  }\n  // Next perform component-type-agnostic transformations (i.e. super())\n  if (component.multiple && !Array.isArray(value)) {\n    (0, lodash_1.set)(data, path, value ? [value] : []);\n    scope.normalize[path].normalized = true;\n  }\n};\nexports.normalizeProcessSync = normalizeProcessSync;\nexports.normalizeProcessInfo = {\n  name: 'normalize',\n  shouldProcess: () => true,\n  process: exports.normalizeProcess,\n  processSync: exports.normalizeProcessSync\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "normalizeProcessInfo", "normalizeProcessSync", "normalizeProcess", "lodash_1", "require", "dayjs_1", "customParseFormat_1", "default", "extend", "isAddressComponent", "component", "type", "isDayComponent", "isEmailComponent", "isRadioComponent", "isSelectComponent", "isSelectBoxesComponent", "isTagsComponent", "isTextFieldComponent", "isTimeComponent", "isNumberComponent", "normalizeAddressComponentValue", "multiple", "Boolean", "enableManualMode", "mode", "address", "getLocaleDateFormatInfo", "locale", "formatInfo", "day", "exampleDate", "Date", "localDateString", "toLocaleDateString", "<PERSON><PERSON><PERSON><PERSON>", "slice", "toString", "getLocaleDayFirst", "form", "_a", "useLocaleSettings", "options", "language", "normalizeDayComponentValue", "valueMask", "isDayFirst", "showDay", "get", "showMonth", "showYear", "test", "dateParts", "valueParts", "split", "DAY", "MONTH", "YEAR", "defaultValue", "defaultDay", "defaultMonth", "defaultYear", "getDayWithHiddenFields", "parts", "isNull", "month", "year", "getNextPart", "shouldTake", "part", "shift", "push", "<PERSON><PERSON><PERSON>denF<PERSON>s", "length", "join", "normalizeRadioComponentValue", "dataType", "JSON", "stringify", "String", "isEquivalent", "Number", "isNaN", "parseFloat", "isFinite", "normalizeSingleSelectComponentValue", "isNil", "valueIsObject", "isObject", "keys", "normalize", "number", "numberValue", "boolean", "isString", "toLowerCase", "string", "object", "auto", "err", "console", "warn", "normalizeSelectComponentValue", "Array", "isArray", "map", "singleValue", "normalizeSelectBoxesComponentValue", "reduce", "acc", "curr", "assign", "normalizeTagsComponentValue", "delimiter", "delimeter", "hasOwnProperty", "storeas", "filter", "normalizeMaskValue", "defaultValues", "path", "inputMasks", "<PERSON><PERSON><PERSON>", "label", "find", "normalizeTextFieldComponentValue", "truncateMultipleSpaces", "trim", "replace", "allowMultipleMasks", "val", "normalizeTimeComponentValue", "defaultDataFormat", "defaultFormat", "format", "<PERSON><PERSON><PERSON><PERSON>", "dataFormat", "normalizeSingleNumberComponentValue", "normalizeNumberComponentValue", "context", "scope", "data", "normalized", "set", "name", "shouldProcess", "process", "processSync"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/normalize/index.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.normalizeProcessInfo = exports.normalizeProcessSync = exports.normalizeProcess = void 0;\nconst lodash_1 = require(\"lodash\");\nconst dayjs_1 = __importDefault(require(\"dayjs\"));\nconst customParseFormat_1 = __importDefault(require(\"dayjs/plugin/customParseFormat\"));\ndayjs_1.default.extend(customParseFormat_1.default);\nconst isAddressComponent = (component) => component.type === 'address';\nconst isDayComponent = (component) => component.type === 'day';\nconst isEmailComponent = (component) => component.type === 'email';\nconst isRadioComponent = (component) => component.type === 'radio';\nconst isSelectComponent = (component) => component.type === 'select';\nconst isSelectBoxesComponent = (component) => component.type === 'selectboxes';\nconst isTagsComponent = (component) => component.type === 'tags';\nconst isTextFieldComponent = (component) => component.type === 'textfield';\nconst isTimeComponent = (component) => component.type === 'time';\nconst isNumberComponent = (component) => component.type === 'number';\nconst normalizeAddressComponentValue = (component, value) => {\n    if (!component.multiple && Boolean(component.enableManualMode) && value && !value.mode) {\n        return {\n            mode: 'autocomplete',\n            address: value,\n        };\n    }\n    return value;\n};\nconst getLocaleDateFormatInfo = (locale = 'en') => {\n    const formatInfo = {};\n    const day = 21;\n    const exampleDate = new Date(2017, 11, day);\n    const localDateString = exampleDate.toLocaleDateString(locale);\n    formatInfo.dayFirst = localDateString.slice(0, 2) === day.toString();\n    return formatInfo;\n};\nconst getLocaleDayFirst = (component, form) => {\n    var _a;\n    if (component.useLocaleSettings) {\n        return getLocaleDateFormatInfo((_a = form.options) === null || _a === void 0 ? void 0 : _a.language).dayFirst;\n    }\n    return component.dayFirst;\n};\nconst normalizeDayComponentValue = (component, form, value) => {\n    // TODO: this is a quick and dirty port of the Day component's normalizeValue method, may need some updates\n    const valueMask = /^\\d{2}\\/\\d{2}\\/\\d{4}$/;\n    const isDayFirst = getLocaleDayFirst(component, form);\n    const showDay = !(0, lodash_1.get)(component, 'fields.day.hide', false);\n    const showMonth = !(0, lodash_1.get)(component, 'fields.month.hide', false);\n    const showYear = !(0, lodash_1.get)(component, 'fields.year.hide', false);\n    if (!value || valueMask.test(value)) {\n        return value;\n    }\n    const dateParts = [];\n    const valueParts = value.split('/');\n    const [DAY, MONTH, YEAR] = component.dayFirst ? [0, 1, 2] : [1, 0, 2];\n    const defaultValue = component.defaultValue ? component.defaultValue.split('/') : '';\n    let defaultDay = '';\n    let defaultMonth = '';\n    let defaultYear = '';\n    const getDayWithHiddenFields = (parts) => {\n        let DAY, MONTH, YEAR;\n        [DAY, MONTH, YEAR] = component.dayFirst ? [0, 1, 2] : [1, 0, 2];\n        if (!showDay) {\n            MONTH = MONTH === 0 ? 0 : MONTH - 1;\n            YEAR = YEAR - 1;\n            DAY = null;\n        }\n        if (!showMonth) {\n            if (!(0, lodash_1.isNull)(DAY)) {\n                DAY = DAY === 0 ? 0 : DAY - 1;\n            }\n            YEAR = YEAR - 1;\n            MONTH = null;\n        }\n        if (!showYear) {\n            YEAR = null;\n        }\n        return {\n            month: (0, lodash_1.isNull)(MONTH) ? '' : parts[MONTH],\n            day: (0, lodash_1.isNull)(DAY) ? '' : parts[DAY],\n            year: (0, lodash_1.isNull)(YEAR) ? '' : parts[YEAR],\n        };\n    };\n    const getNextPart = (shouldTake, defaultValue) => {\n        // Only push the part if it's not an empty string\n        const part = shouldTake ? valueParts.shift() : defaultValue;\n        if (part !== '') {\n            dateParts.push(part);\n        }\n    };\n    if (defaultValue) {\n        const hasHiddenFields = defaultValue.length !== 3;\n        defaultDay = hasHiddenFields ? getDayWithHiddenFields(defaultValue).day : defaultValue[DAY];\n        defaultMonth = hasHiddenFields\n            ? getDayWithHiddenFields(defaultValue).month\n            : defaultValue[MONTH];\n        defaultYear = hasHiddenFields ? getDayWithHiddenFields(defaultValue).year : defaultValue[YEAR];\n    }\n    if (isDayFirst) {\n        getNextPart(showDay, defaultDay);\n    }\n    getNextPart(showMonth, defaultMonth);\n    if (!isDayFirst) {\n        getNextPart(showDay, defaultDay);\n    }\n    getNextPart(showYear, defaultYear);\n    return dateParts.join('/');\n};\nconst normalizeRadioComponentValue = (value, dataType) => {\n    switch (dataType) {\n        case 'number':\n            return +value;\n        case 'string':\n            return typeof value === 'object' ? JSON.stringify(value) : String(value);\n        case 'boolean':\n            return !(!value || value.toString() === 'false');\n    }\n    const isEquivalent = (0, lodash_1.toString)(value) === Number(value).toString();\n    if (!isNaN(parseFloat(value)) && isFinite(value) && isEquivalent) {\n        return +value;\n    }\n    if (value === 'true') {\n        return true;\n    }\n    if (value === 'false') {\n        return false;\n    }\n    return value;\n};\nconst normalizeSingleSelectComponentValue = (component, value) => {\n    if ((0, lodash_1.isNil)(value)) {\n        return;\n    }\n    const valueIsObject = (0, lodash_1.isObject)(value);\n    //check if value equals to default emptyValue\n    if (valueIsObject && Object.keys(value).length === 0) {\n        return value;\n    }\n    const dataType = component.dataType || 'auto';\n    const normalize = {\n        value,\n        number() {\n            const numberValue = Number(this.value);\n            const isEquivalent = value.toString() === numberValue.toString();\n            if (!Number.isNaN(numberValue) &&\n                Number.isFinite(numberValue) &&\n                value !== '' &&\n                isEquivalent) {\n                this.value = numberValue;\n            }\n            return this;\n        },\n        boolean() {\n            if ((0, lodash_1.isString)(this.value) &&\n                (this.value.toLowerCase() === 'true' || this.value.toLowerCase() === 'false')) {\n                this.value = this.value.toLowerCase() === 'true';\n            }\n            return this;\n        },\n        string() {\n            this.value = String(this.value);\n            return this;\n        },\n        object() {\n            return this;\n        },\n        auto() {\n            if ((0, lodash_1.isObject)(this.value)) {\n                this.value = this.object().value;\n            }\n            else {\n                this.value = this.string().number().boolean().value;\n            }\n            return this;\n        },\n    };\n    try {\n        return normalize[dataType]().value;\n    }\n    catch (err) {\n        console.warn('Failed to normalize value', err);\n        return value;\n    }\n};\nconst normalizeSelectComponentValue = (component, value) => {\n    if (component.multiple && Array.isArray(value)) {\n        return value.map((singleValue) => normalizeSingleSelectComponentValue(component, singleValue));\n    }\n    return normalizeSingleSelectComponentValue(component, value);\n};\nconst normalizeSelectBoxesComponentValue = (value) => {\n    if (!value) {\n        value = {};\n    }\n    if (typeof value !== 'object') {\n        if (typeof value === 'string') {\n            return {\n                [value]: true,\n            };\n        }\n        else {\n            return {};\n        }\n    }\n    if (Array.isArray(value)) {\n        return value.reduce((acc, curr) => {\n            return Object.assign(Object.assign({}, acc), { [curr]: true });\n        }, {});\n    }\n    return value;\n};\nconst normalizeTagsComponentValue = (component, value) => {\n    const delimiter = component.delimeter || ',';\n    if ((!component.hasOwnProperty('storeas') || component.storeas === 'string') &&\n        Array.isArray(value)) {\n        return value.join(delimiter);\n    }\n    else if (component.storeas === 'array' && typeof value === 'string') {\n        return value.split(delimiter).filter((result) => result);\n    }\n    return value;\n};\nconst normalizeMaskValue = (component, defaultValues, value, path) => {\n    if (component.inputMasks && component.inputMasks.length > 0) {\n        if (!value || typeof value !== 'object') {\n            return {\n                value: value,\n                maskName: component.inputMasks[0].label,\n            };\n        }\n        if (!value.value) {\n            const defaultValue = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.find((defaultValue) => defaultValue.path === path);\n            value.value =\n                Array.isArray(defaultValue) && defaultValue.length > 0 ? defaultValue[0] : defaultValue;\n        }\n    }\n    return value;\n};\nconst normalizeTextFieldComponentValue = (component, defaultValues, value, path) => {\n    // If the component has truncate multiple spaces enabled, then normalize the value to remove extra spaces.\n    if (component.truncateMultipleSpaces && typeof value === 'string') {\n        value = value.trim().replace(/\\s{2,}/g, ' ');\n    }\n    if (component.allowMultipleMasks && component.inputMasks && component.inputMasks.length > 0) {\n        if (Array.isArray(value)) {\n            return value.map((val) => normalizeMaskValue(component, defaultValues, val, path));\n        }\n        else {\n            return normalizeMaskValue(component, defaultValues, value, path);\n        }\n    }\n    return value;\n};\n// Allow submissions of time components in their visual \"format\" property by coercing them to the \"dataFormat\" property\n// i.e. \"HH:mm\" -> \"HH:mm:ss\"\nconst normalizeTimeComponentValue = (component, value) => {\n    const defaultDataFormat = 'HH:mm:ss';\n    const defaultFormat = 'HH:mm';\n    if ((0, dayjs_1.default)(value, component.format || defaultFormat, true).isValid()) {\n        return (0, dayjs_1.default)(value, component.format || defaultFormat, true).format(component.dataFormat || defaultDataFormat);\n    }\n    return value;\n};\nconst normalizeSingleNumberComponentValue = (component, value) => {\n    if (!isNaN(parseFloat(value)) && isFinite(value)) {\n        return +value;\n    }\n    return value;\n};\nconst normalizeNumberComponentValue = (component, value) => {\n    if (component.multiple && Array.isArray(value)) {\n        return value.map((singleValue) => normalizeSingleNumberComponentValue(component, singleValue));\n    }\n    return normalizeSingleNumberComponentValue(component, value);\n};\nconst normalizeProcess = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.normalizeProcessSync)(context);\n});\nexports.normalizeProcess = normalizeProcess;\nconst normalizeProcessSync = (context) => {\n    const { component, form, scope, path, data, value } = context;\n    if (!scope.normalize) {\n        scope.normalize = {};\n    }\n    const { defaultValues } = scope;\n    scope.normalize[path] = {\n        type: component.type,\n        normalized: false,\n    };\n    // First check for component-type-specific transformations\n    if (isAddressComponent(component)) {\n        (0, lodash_1.set)(data, path, normalizeAddressComponentValue(component, value));\n        scope.normalize[path].normalized = true;\n    }\n    else if (isDayComponent(component)) {\n        (0, lodash_1.set)(data, path, normalizeDayComponentValue(component, form, value));\n        scope.normalize[path].normalized = true;\n    }\n    else if (isEmailComponent(component)) {\n        if (value && typeof value === 'string') {\n            (0, lodash_1.set)(data, path, value.toLowerCase());\n            scope.normalize[path].normalized = true;\n        }\n    }\n    else if (isRadioComponent(component)) {\n        (0, lodash_1.set)(data, path, normalizeRadioComponentValue(value, component.dataType));\n        scope.normalize[path].normalized = true;\n    }\n    else if (isSelectComponent(component)) {\n        (0, lodash_1.set)(data, path, normalizeSelectComponentValue(component, value));\n        scope.normalize[path].normalized = true;\n    }\n    else if (isSelectBoxesComponent(component)) {\n        (0, lodash_1.set)(data, path, normalizeSelectBoxesComponentValue(value));\n        scope.normalize[path].normalized = true;\n    }\n    else if (isTagsComponent(component)) {\n        (0, lodash_1.set)(data, path, normalizeTagsComponentValue(component, value));\n        scope.normalize[path].normalized = true;\n    }\n    else if (isTextFieldComponent(component)) {\n        (0, lodash_1.set)(data, path, normalizeTextFieldComponentValue(component, defaultValues, value, path));\n        scope.normalize[path].normalized = true;\n    }\n    else if (isTimeComponent(component)) {\n        (0, lodash_1.set)(data, path, normalizeTimeComponentValue(component, value));\n        scope.normalize[path].normalized = true;\n    }\n    else if (isNumberComponent(component)) {\n        (0, lodash_1.set)(data, path, normalizeNumberComponentValue(component, value));\n        scope.normalize[path].normalized = true;\n    }\n    // Next perform component-type-agnostic transformations (i.e. super())\n    if (component.multiple && !Array.isArray(value)) {\n        (0, lodash_1.set)(data, path, value ? [value] : []);\n        scope.normalize[path].normalized = true;\n    }\n};\nexports.normalizeProcessSync = normalizeProcessSync;\nexports.normalizeProcessInfo = {\n    name: 'normalize',\n    shouldProcess: () => true,\n    process: exports.normalizeProcess,\n    processSync: exports.normalizeProcessSync,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,IAAIO,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAElB,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DkB,OAAO,CAACC,oBAAoB,GAAGD,OAAO,CAACE,oBAAoB,GAAGF,OAAO,CAACG,gBAAgB,GAAG,KAAK,CAAC;AAC/F,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,OAAO,GAAGX,eAAe,CAACU,OAAO,CAAC,OAAO,CAAC,CAAC;AACjD,MAAME,mBAAmB,GAAGZ,eAAe,CAACU,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACtFC,OAAO,CAACE,OAAO,CAACC,MAAM,CAACF,mBAAmB,CAACC,OAAO,CAAC;AACnD,MAAME,kBAAkB,GAAIC,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,SAAS;AACtE,MAAMC,cAAc,GAAIF,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,KAAK;AAC9D,MAAME,gBAAgB,GAAIH,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,OAAO;AAClE,MAAMG,gBAAgB,GAAIJ,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,OAAO;AAClE,MAAMI,iBAAiB,GAAIL,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,QAAQ;AACpE,MAAMK,sBAAsB,GAAIN,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,aAAa;AAC9E,MAAMM,eAAe,GAAIP,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,MAAM;AAChE,MAAMO,oBAAoB,GAAIR,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,WAAW;AAC1E,MAAMQ,eAAe,GAAIT,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,MAAM;AAChE,MAAMS,iBAAiB,GAAIV,SAAS,IAAKA,SAAS,CAACC,IAAI,KAAK,QAAQ;AACpE,MAAMU,8BAA8B,GAAGA,CAACX,SAAS,EAAE7B,KAAK,KAAK;EACzD,IAAI,CAAC6B,SAAS,CAACY,QAAQ,IAAIC,OAAO,CAACb,SAAS,CAACc,gBAAgB,CAAC,IAAI3C,KAAK,IAAI,CAACA,KAAK,CAAC4C,IAAI,EAAE;IACpF,OAAO;MACHA,IAAI,EAAE,cAAc;MACpBC,OAAO,EAAE7C;IACb,CAAC;EACL;EACA,OAAOA,KAAK;AAChB,CAAC;AACD,MAAM8C,uBAAuB,GAAGA,CAACC,MAAM,GAAG,IAAI,KAAK;EAC/C,MAAMC,UAAU,GAAG,CAAC,CAAC;EACrB,MAAMC,GAAG,GAAG,EAAE;EACd,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAEF,GAAG,CAAC;EAC3C,MAAMG,eAAe,GAAGF,WAAW,CAACG,kBAAkB,CAACN,MAAM,CAAC;EAC9DC,UAAU,CAACM,QAAQ,GAAGF,eAAe,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKN,GAAG,CAACO,QAAQ,CAAC,CAAC;EACpE,OAAOR,UAAU;AACrB,CAAC;AACD,MAAMS,iBAAiB,GAAGA,CAAC5B,SAAS,EAAE6B,IAAI,KAAK;EAC3C,IAAIC,EAAE;EACN,IAAI9B,SAAS,CAAC+B,iBAAiB,EAAE;IAC7B,OAAOd,uBAAuB,CAAC,CAACa,EAAE,GAAGD,IAAI,CAACG,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,QAAQ,CAAC,CAACR,QAAQ;EACjH;EACA,OAAOzB,SAAS,CAACyB,QAAQ;AAC7B,CAAC;AACD,MAAMS,0BAA0B,GAAGA,CAAClC,SAAS,EAAE6B,IAAI,EAAE1D,KAAK,KAAK;EAC3D;EACA,MAAMgE,SAAS,GAAG,uBAAuB;EACzC,MAAMC,UAAU,GAAGR,iBAAiB,CAAC5B,SAAS,EAAE6B,IAAI,CAAC;EACrD,MAAMQ,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE5C,QAAQ,CAAC6C,GAAG,EAAEtC,SAAS,EAAE,iBAAiB,EAAE,KAAK,CAAC;EACvE,MAAMuC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE9C,QAAQ,CAAC6C,GAAG,EAAEtC,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC;EAC3E,MAAMwC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE/C,QAAQ,CAAC6C,GAAG,EAAEtC,SAAS,EAAE,kBAAkB,EAAE,KAAK,CAAC;EACzE,IAAI,CAAC7B,KAAK,IAAIgE,SAAS,CAACM,IAAI,CAACtE,KAAK,CAAC,EAAE;IACjC,OAAOA,KAAK;EAChB;EACA,MAAMuE,SAAS,GAAG,EAAE;EACpB,MAAMC,UAAU,GAAGxE,KAAK,CAACyE,KAAK,CAAC,GAAG,CAAC;EACnC,MAAM,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAG/C,SAAS,CAACyB,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACrE,MAAMuB,YAAY,GAAGhD,SAAS,CAACgD,YAAY,GAAGhD,SAAS,CAACgD,YAAY,CAACJ,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;EACpF,IAAIK,UAAU,GAAG,EAAE;EACnB,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIC,WAAW,GAAG,EAAE;EACpB,MAAMC,sBAAsB,GAAIC,KAAK,IAAK;IACtC,IAAIR,GAAG,EAAEC,KAAK,EAAEC,IAAI;IACpB,CAACF,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAG/C,SAAS,CAACyB,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACY,OAAO,EAAE;MACVS,KAAK,GAAGA,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;MACnCC,IAAI,GAAGA,IAAI,GAAG,CAAC;MACfF,GAAG,GAAG,IAAI;IACd;IACA,IAAI,CAACN,SAAS,EAAE;MACZ,IAAI,CAAC,CAAC,CAAC,EAAE9C,QAAQ,CAAC6D,MAAM,EAAET,GAAG,CAAC,EAAE;QAC5BA,GAAG,GAAGA,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC;MACjC;MACAE,IAAI,GAAGA,IAAI,GAAG,CAAC;MACfD,KAAK,GAAG,IAAI;IAChB;IACA,IAAI,CAACN,QAAQ,EAAE;MACXO,IAAI,GAAG,IAAI;IACf;IACA,OAAO;MACHQ,KAAK,EAAE,CAAC,CAAC,EAAE9D,QAAQ,CAAC6D,MAAM,EAAER,KAAK,CAAC,GAAG,EAAE,GAAGO,KAAK,CAACP,KAAK,CAAC;MACtD1B,GAAG,EAAE,CAAC,CAAC,EAAE3B,QAAQ,CAAC6D,MAAM,EAAET,GAAG,CAAC,GAAG,EAAE,GAAGQ,KAAK,CAACR,GAAG,CAAC;MAChDW,IAAI,EAAE,CAAC,CAAC,EAAE/D,QAAQ,CAAC6D,MAAM,EAAEP,IAAI,CAAC,GAAG,EAAE,GAAGM,KAAK,CAACN,IAAI;IACtD,CAAC;EACL,CAAC;EACD,MAAMU,WAAW,GAAGA,CAACC,UAAU,EAAEV,YAAY,KAAK;IAC9C;IACA,MAAMW,IAAI,GAAGD,UAAU,GAAGf,UAAU,CAACiB,KAAK,CAAC,CAAC,GAAGZ,YAAY;IAC3D,IAAIW,IAAI,KAAK,EAAE,EAAE;MACbjB,SAAS,CAACmB,IAAI,CAACF,IAAI,CAAC;IACxB;EACJ,CAAC;EACD,IAAIX,YAAY,EAAE;IACd,MAAMc,eAAe,GAAGd,YAAY,CAACe,MAAM,KAAK,CAAC;IACjDd,UAAU,GAAGa,eAAe,GAAGV,sBAAsB,CAACJ,YAAY,CAAC,CAAC5B,GAAG,GAAG4B,YAAY,CAACH,GAAG,CAAC;IAC3FK,YAAY,GAAGY,eAAe,GACxBV,sBAAsB,CAACJ,YAAY,CAAC,CAACO,KAAK,GAC1CP,YAAY,CAACF,KAAK,CAAC;IACzBK,WAAW,GAAGW,eAAe,GAAGV,sBAAsB,CAACJ,YAAY,CAAC,CAACQ,IAAI,GAAGR,YAAY,CAACD,IAAI,CAAC;EAClG;EACA,IAAIX,UAAU,EAAE;IACZqB,WAAW,CAACpB,OAAO,EAAEY,UAAU,CAAC;EACpC;EACAQ,WAAW,CAAClB,SAAS,EAAEW,YAAY,CAAC;EACpC,IAAI,CAACd,UAAU,EAAE;IACbqB,WAAW,CAACpB,OAAO,EAAEY,UAAU,CAAC;EACpC;EACAQ,WAAW,CAACjB,QAAQ,EAAEW,WAAW,CAAC;EAClC,OAAOT,SAAS,CAACsB,IAAI,CAAC,GAAG,CAAC;AAC9B,CAAC;AACD,MAAMC,4BAA4B,GAAGA,CAAC9F,KAAK,EAAE+F,QAAQ,KAAK;EACtD,QAAQA,QAAQ;IACZ,KAAK,QAAQ;MACT,OAAO,CAAC/F,KAAK;IACjB,KAAK,QAAQ;MACT,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAGgG,IAAI,CAACC,SAAS,CAACjG,KAAK,CAAC,GAAGkG,MAAM,CAAClG,KAAK,CAAC;IAC5E,KAAK,SAAS;MACV,OAAO,EAAE,CAACA,KAAK,IAAIA,KAAK,CAACwD,QAAQ,CAAC,CAAC,KAAK,OAAO,CAAC;EACxD;EACA,MAAM2C,YAAY,GAAG,CAAC,CAAC,EAAE7E,QAAQ,CAACkC,QAAQ,EAAExD,KAAK,CAAC,KAAKoG,MAAM,CAACpG,KAAK,CAAC,CAACwD,QAAQ,CAAC,CAAC;EAC/E,IAAI,CAAC6C,KAAK,CAACC,UAAU,CAACtG,KAAK,CAAC,CAAC,IAAIuG,QAAQ,CAACvG,KAAK,CAAC,IAAImG,YAAY,EAAE;IAC9D,OAAO,CAACnG,KAAK;EACjB;EACA,IAAIA,KAAK,KAAK,MAAM,EAAE;IAClB,OAAO,IAAI;EACf;EACA,IAAIA,KAAK,KAAK,OAAO,EAAE;IACnB,OAAO,KAAK;EAChB;EACA,OAAOA,KAAK;AAChB,CAAC;AACD,MAAMwG,mCAAmC,GAAGA,CAAC3E,SAAS,EAAE7B,KAAK,KAAK;EAC9D,IAAI,CAAC,CAAC,EAAEsB,QAAQ,CAACmF,KAAK,EAAEzG,KAAK,CAAC,EAAE;IAC5B;EACJ;EACA,MAAM0G,aAAa,GAAG,CAAC,CAAC,EAAEpF,QAAQ,CAACqF,QAAQ,EAAE3G,KAAK,CAAC;EACnD;EACA,IAAI0G,aAAa,IAAI1F,MAAM,CAAC4F,IAAI,CAAC5G,KAAK,CAAC,CAAC4F,MAAM,KAAK,CAAC,EAAE;IAClD,OAAO5F,KAAK;EAChB;EACA,MAAM+F,QAAQ,GAAGlE,SAAS,CAACkE,QAAQ,IAAI,MAAM;EAC7C,MAAMc,SAAS,GAAG;IACd7G,KAAK;IACL8G,MAAMA,CAAA,EAAG;MACL,MAAMC,WAAW,GAAGX,MAAM,CAAC,IAAI,CAACpG,KAAK,CAAC;MACtC,MAAMmG,YAAY,GAAGnG,KAAK,CAACwD,QAAQ,CAAC,CAAC,KAAKuD,WAAW,CAACvD,QAAQ,CAAC,CAAC;MAChE,IAAI,CAAC4C,MAAM,CAACC,KAAK,CAACU,WAAW,CAAC,IAC1BX,MAAM,CAACG,QAAQ,CAACQ,WAAW,CAAC,IAC5B/G,KAAK,KAAK,EAAE,IACZmG,YAAY,EAAE;QACd,IAAI,CAACnG,KAAK,GAAG+G,WAAW;MAC5B;MACA,OAAO,IAAI;IACf,CAAC;IACDC,OAAOA,CAAA,EAAG;MACN,IAAI,CAAC,CAAC,EAAE1F,QAAQ,CAAC2F,QAAQ,EAAE,IAAI,CAACjH,KAAK,CAAC,KACjC,IAAI,CAACA,KAAK,CAACkH,WAAW,CAAC,CAAC,KAAK,MAAM,IAAI,IAAI,CAAClH,KAAK,CAACkH,WAAW,CAAC,CAAC,KAAK,OAAO,CAAC,EAAE;QAC/E,IAAI,CAAClH,KAAK,GAAG,IAAI,CAACA,KAAK,CAACkH,WAAW,CAAC,CAAC,KAAK,MAAM;MACpD;MACA,OAAO,IAAI;IACf,CAAC;IACDC,MAAMA,CAAA,EAAG;MACL,IAAI,CAACnH,KAAK,GAAGkG,MAAM,CAAC,IAAI,CAAClG,KAAK,CAAC;MAC/B,OAAO,IAAI;IACf,CAAC;IACDoH,MAAMA,CAAA,EAAG;MACL,OAAO,IAAI;IACf,CAAC;IACDC,IAAIA,CAAA,EAAG;MACH,IAAI,CAAC,CAAC,EAAE/F,QAAQ,CAACqF,QAAQ,EAAE,IAAI,CAAC3G,KAAK,CAAC,EAAE;QACpC,IAAI,CAACA,KAAK,GAAG,IAAI,CAACoH,MAAM,CAAC,CAAC,CAACpH,KAAK;MACpC,CAAC,MACI;QACD,IAAI,CAACA,KAAK,GAAG,IAAI,CAACmH,MAAM,CAAC,CAAC,CAACL,MAAM,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,CAAChH,KAAK;MACvD;MACA,OAAO,IAAI;IACf;EACJ,CAAC;EACD,IAAI;IACA,OAAO6G,SAAS,CAACd,QAAQ,CAAC,CAAC,CAAC,CAAC/F,KAAK;EACtC,CAAC,CACD,OAAOsH,GAAG,EAAE;IACRC,OAAO,CAACC,IAAI,CAAC,2BAA2B,EAAEF,GAAG,CAAC;IAC9C,OAAOtH,KAAK;EAChB;AACJ,CAAC;AACD,MAAMyH,6BAA6B,GAAGA,CAAC5F,SAAS,EAAE7B,KAAK,KAAK;EACxD,IAAI6B,SAAS,CAACY,QAAQ,IAAIiF,KAAK,CAACC,OAAO,CAAC3H,KAAK,CAAC,EAAE;IAC5C,OAAOA,KAAK,CAAC4H,GAAG,CAAEC,WAAW,IAAKrB,mCAAmC,CAAC3E,SAAS,EAAEgG,WAAW,CAAC,CAAC;EAClG;EACA,OAAOrB,mCAAmC,CAAC3E,SAAS,EAAE7B,KAAK,CAAC;AAChE,CAAC;AACD,MAAM8H,kCAAkC,GAAI9H,KAAK,IAAK;EAClD,IAAI,CAACA,KAAK,EAAE;IACRA,KAAK,GAAG,CAAC,CAAC;EACd;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAO;QACH,CAACA,KAAK,GAAG;MACb,CAAC;IACL,CAAC,MACI;MACD,OAAO,CAAC,CAAC;IACb;EACJ;EACA,IAAI0H,KAAK,CAACC,OAAO,CAAC3H,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAAC+H,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAC/B,OAAOjH,MAAM,CAACkH,MAAM,CAAClH,MAAM,CAACkH,MAAM,CAAC,CAAC,CAAC,EAAEF,GAAG,CAAC,EAAE;QAAE,CAACC,IAAI,GAAG;MAAK,CAAC,CAAC;IAClE,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;EACA,OAAOjI,KAAK;AAChB,CAAC;AACD,MAAMmI,2BAA2B,GAAGA,CAACtG,SAAS,EAAE7B,KAAK,KAAK;EACtD,MAAMoI,SAAS,GAAGvG,SAAS,CAACwG,SAAS,IAAI,GAAG;EAC5C,IAAI,CAAC,CAACxG,SAAS,CAACyG,cAAc,CAAC,SAAS,CAAC,IAAIzG,SAAS,CAAC0G,OAAO,KAAK,QAAQ,KACvEb,KAAK,CAACC,OAAO,CAAC3H,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAAC6F,IAAI,CAACuC,SAAS,CAAC;EAChC,CAAC,MACI,IAAIvG,SAAS,CAAC0G,OAAO,KAAK,OAAO,IAAI,OAAOvI,KAAK,KAAK,QAAQ,EAAE;IACjE,OAAOA,KAAK,CAACyE,KAAK,CAAC2D,SAAS,CAAC,CAACI,MAAM,CAAE/H,MAAM,IAAKA,MAAM,CAAC;EAC5D;EACA,OAAOT,KAAK;AAChB,CAAC;AACD,MAAMyI,kBAAkB,GAAGA,CAAC5G,SAAS,EAAE6G,aAAa,EAAE1I,KAAK,EAAE2I,IAAI,KAAK;EAClE,IAAI9G,SAAS,CAAC+G,UAAU,IAAI/G,SAAS,CAAC+G,UAAU,CAAChD,MAAM,GAAG,CAAC,EAAE;IACzD,IAAI,CAAC5F,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACrC,OAAO;QACHA,KAAK,EAAEA,KAAK;QACZ6I,QAAQ,EAAEhH,SAAS,CAAC+G,UAAU,CAAC,CAAC,CAAC,CAACE;MACtC,CAAC;IACL;IACA,IAAI,CAAC9I,KAAK,CAACA,KAAK,EAAE;MACd,MAAM6E,YAAY,GAAG6D,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,IAAI,CAAElE,YAAY,IAAKA,YAAY,CAAC8D,IAAI,KAAKA,IAAI,CAAC;MACnJ3I,KAAK,CAACA,KAAK,GACP0H,KAAK,CAACC,OAAO,CAAC9C,YAAY,CAAC,IAAIA,YAAY,CAACe,MAAM,GAAG,CAAC,GAAGf,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY;IAC/F;EACJ;EACA,OAAO7E,KAAK;AAChB,CAAC;AACD,MAAMgJ,gCAAgC,GAAGA,CAACnH,SAAS,EAAE6G,aAAa,EAAE1I,KAAK,EAAE2I,IAAI,KAAK;EAChF;EACA,IAAI9G,SAAS,CAACoH,sBAAsB,IAAI,OAAOjJ,KAAK,KAAK,QAAQ,EAAE;IAC/DA,KAAK,GAAGA,KAAK,CAACkJ,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;EAChD;EACA,IAAItH,SAAS,CAACuH,kBAAkB,IAAIvH,SAAS,CAAC+G,UAAU,IAAI/G,SAAS,CAAC+G,UAAU,CAAChD,MAAM,GAAG,CAAC,EAAE;IACzF,IAAI8B,KAAK,CAACC,OAAO,CAAC3H,KAAK,CAAC,EAAE;MACtB,OAAOA,KAAK,CAAC4H,GAAG,CAAEyB,GAAG,IAAKZ,kBAAkB,CAAC5G,SAAS,EAAE6G,aAAa,EAAEW,GAAG,EAAEV,IAAI,CAAC,CAAC;IACtF,CAAC,MACI;MACD,OAAOF,kBAAkB,CAAC5G,SAAS,EAAE6G,aAAa,EAAE1I,KAAK,EAAE2I,IAAI,CAAC;IACpE;EACJ;EACA,OAAO3I,KAAK;AAChB,CAAC;AACD;AACA;AACA,MAAMsJ,2BAA2B,GAAGA,CAACzH,SAAS,EAAE7B,KAAK,KAAK;EACtD,MAAMuJ,iBAAiB,GAAG,UAAU;EACpC,MAAMC,aAAa,GAAG,OAAO;EAC7B,IAAI,CAAC,CAAC,EAAEhI,OAAO,CAACE,OAAO,EAAE1B,KAAK,EAAE6B,SAAS,CAAC4H,MAAM,IAAID,aAAa,EAAE,IAAI,CAAC,CAACE,OAAO,CAAC,CAAC,EAAE;IAChF,OAAO,CAAC,CAAC,EAAElI,OAAO,CAACE,OAAO,EAAE1B,KAAK,EAAE6B,SAAS,CAAC4H,MAAM,IAAID,aAAa,EAAE,IAAI,CAAC,CAACC,MAAM,CAAC5H,SAAS,CAAC8H,UAAU,IAAIJ,iBAAiB,CAAC;EACjI;EACA,OAAOvJ,KAAK;AAChB,CAAC;AACD,MAAM4J,mCAAmC,GAAGA,CAAC/H,SAAS,EAAE7B,KAAK,KAAK;EAC9D,IAAI,CAACqG,KAAK,CAACC,UAAU,CAACtG,KAAK,CAAC,CAAC,IAAIuG,QAAQ,CAACvG,KAAK,CAAC,EAAE;IAC9C,OAAO,CAACA,KAAK;EACjB;EACA,OAAOA,KAAK;AAChB,CAAC;AACD,MAAM6J,6BAA6B,GAAGA,CAAChI,SAAS,EAAE7B,KAAK,KAAK;EACxD,IAAI6B,SAAS,CAACY,QAAQ,IAAIiF,KAAK,CAACC,OAAO,CAAC3H,KAAK,CAAC,EAAE;IAC5C,OAAOA,KAAK,CAAC4H,GAAG,CAAEC,WAAW,IAAK+B,mCAAmC,CAAC/H,SAAS,EAAEgG,WAAW,CAAC,CAAC;EAClG;EACA,OAAO+B,mCAAmC,CAAC/H,SAAS,EAAE7B,KAAK,CAAC;AAChE,CAAC;AACD,MAAMqB,gBAAgB,GAAIyI,OAAO,IAAKpK,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACjF,OAAO,CAAC,CAAC,EAAEwB,OAAO,CAACE,oBAAoB,EAAE0I,OAAO,CAAC;AACrD,CAAC,CAAC;AACF5I,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMD,oBAAoB,GAAI0I,OAAO,IAAK;EACtC,MAAM;IAAEjI,SAAS;IAAE6B,IAAI;IAAEqG,KAAK;IAAEpB,IAAI;IAAEqB,IAAI;IAAEhK;EAAM,CAAC,GAAG8J,OAAO;EAC7D,IAAI,CAACC,KAAK,CAAClD,SAAS,EAAE;IAClBkD,KAAK,CAAClD,SAAS,GAAG,CAAC,CAAC;EACxB;EACA,MAAM;IAAE6B;EAAc,CAAC,GAAGqB,KAAK;EAC/BA,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,GAAG;IACpB7G,IAAI,EAAED,SAAS,CAACC,IAAI;IACpBmI,UAAU,EAAE;EAChB,CAAC;EACD;EACA,IAAIrI,kBAAkB,CAACC,SAAS,CAAC,EAAE;IAC/B,CAAC,CAAC,EAAEP,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAEnG,8BAA8B,CAACX,SAAS,EAAE7B,KAAK,CAAC,CAAC;IAC/E+J,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C,CAAC,MACI,IAAIlI,cAAc,CAACF,SAAS,CAAC,EAAE;IAChC,CAAC,CAAC,EAAEP,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAE5E,0BAA0B,CAAClC,SAAS,EAAE6B,IAAI,EAAE1D,KAAK,CAAC,CAAC;IACjF+J,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C,CAAC,MACI,IAAIjI,gBAAgB,CAACH,SAAS,CAAC,EAAE;IAClC,IAAI7B,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACpC,CAAC,CAAC,EAAEsB,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAE3I,KAAK,CAACkH,WAAW,CAAC,CAAC,CAAC;MAClD6C,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;IAC3C;EACJ,CAAC,MACI,IAAIhI,gBAAgB,CAACJ,SAAS,CAAC,EAAE;IAClC,CAAC,CAAC,EAAEP,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAE7C,4BAA4B,CAAC9F,KAAK,EAAE6B,SAAS,CAACkE,QAAQ,CAAC,CAAC;IACtFgE,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C,CAAC,MACI,IAAI/H,iBAAiB,CAACL,SAAS,CAAC,EAAE;IACnC,CAAC,CAAC,EAAEP,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAElB,6BAA6B,CAAC5F,SAAS,EAAE7B,KAAK,CAAC,CAAC;IAC9E+J,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C,CAAC,MACI,IAAI9H,sBAAsB,CAACN,SAAS,CAAC,EAAE;IACxC,CAAC,CAAC,EAAEP,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAEb,kCAAkC,CAAC9H,KAAK,CAAC,CAAC;IACxE+J,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C,CAAC,MACI,IAAI7H,eAAe,CAACP,SAAS,CAAC,EAAE;IACjC,CAAC,CAAC,EAAEP,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAER,2BAA2B,CAACtG,SAAS,EAAE7B,KAAK,CAAC,CAAC;IAC5E+J,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C,CAAC,MACI,IAAI5H,oBAAoB,CAACR,SAAS,CAAC,EAAE;IACtC,CAAC,CAAC,EAAEP,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAEK,gCAAgC,CAACnH,SAAS,EAAE6G,aAAa,EAAE1I,KAAK,EAAE2I,IAAI,CAAC,CAAC;IACtGoB,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C,CAAC,MACI,IAAI3H,eAAe,CAACT,SAAS,CAAC,EAAE;IACjC,CAAC,CAAC,EAAEP,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAEW,2BAA2B,CAACzH,SAAS,EAAE7B,KAAK,CAAC,CAAC;IAC5E+J,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C,CAAC,MACI,IAAI1H,iBAAiB,CAACV,SAAS,CAAC,EAAE;IACnC,CAAC,CAAC,EAAEP,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAEkB,6BAA6B,CAAChI,SAAS,EAAE7B,KAAK,CAAC,CAAC;IAC9E+J,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C;EACA;EACA,IAAIpI,SAAS,CAACY,QAAQ,IAAI,CAACiF,KAAK,CAACC,OAAO,CAAC3H,KAAK,CAAC,EAAE;IAC7C,CAAC,CAAC,EAAEsB,QAAQ,CAAC4I,GAAG,EAAEF,IAAI,EAAErB,IAAI,EAAE3I,KAAK,GAAG,CAACA,KAAK,CAAC,GAAG,EAAE,CAAC;IACnD+J,KAAK,CAAClD,SAAS,CAAC8B,IAAI,CAAC,CAACsB,UAAU,GAAG,IAAI;EAC3C;AACJ,CAAC;AACD/I,OAAO,CAACE,oBAAoB,GAAGA,oBAAoB;AACnDF,OAAO,CAACC,oBAAoB,GAAG;EAC3BgJ,IAAI,EAAE,WAAW;EACjBC,aAAa,EAAEA,CAAA,KAAM,IAAI;EACzBC,OAAO,EAAEnJ,OAAO,CAACG,gBAAgB;EACjCiJ,WAAW,EAAEpJ,OAAO,CAACE;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}