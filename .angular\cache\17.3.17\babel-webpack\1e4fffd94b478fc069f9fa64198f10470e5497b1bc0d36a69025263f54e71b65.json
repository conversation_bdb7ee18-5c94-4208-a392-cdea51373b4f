{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateRequiredDayInfo = exports.validateRequiredDaySync = exports.validateRequiredDay = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableDayComponent = component => {\n  var _a, _b, _c, _d, _e, _f;\n  return component && component.type === 'day' && (((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.day) === null || _b === void 0 ? void 0 : _b.required) || ((_d = (_c = component.fields) === null || _c === void 0 ? void 0 : _c.month) === null || _d === void 0 ? void 0 : _d.required) || ((_f = (_e = component.fields) === null || _e === void 0 ? void 0 : _e.year) === null || _f === void 0 ? void 0 : _f.required));\n};\nconst shouldValidate = context => {\n  const {\n    component\n  } = context;\n  return isValidatableDayComponent(component);\n};\nexports.shouldValidate = shouldValidate;\nconst validateRequiredDay = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateRequiredDaySync)(context);\n});\nexports.validateRequiredDay = validateRequiredDay;\nconst validateRequiredDaySync = context => {\n  var _a, _b, _c, _d, _e, _f;\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  if (!isValidatableDayComponent(component)) {\n    return null;\n  }\n  if (!value) {\n    return new error_1.FieldError('requiredDayEmpty', context, 'day');\n  }\n  if (typeof value !== 'string') {\n    throw new error_1.ProcessorError(`Cannot validate required day field of ${value} because it is not a string`, context, 'validate:validateRequiredDay');\n  }\n  let [DAY, MONTH, YEAR] = component.dayFirst ? [0, 1, 2] : [1, 0, 2];\n  const values = value.split('/').map(x => parseInt(x, 10));\n  let day = values[DAY];\n  let month = values[MONTH];\n  let year = values[YEAR];\n  if (values.length !== 3) {\n    if (component.fields.day.hide) {\n      MONTH = MONTH === 0 ? 0 : MONTH - 1;\n      YEAR = YEAR - 1;\n      day = 0;\n      month = values[MONTH];\n      year = values[YEAR];\n    }\n    if (component.fields.month.hide) {\n      DAY = DAY === 0 ? 0 : DAY - 1;\n      YEAR = YEAR - 1;\n      day = component.fields.day.hide && day === 0 ? 0 : values[DAY];\n      month = 0;\n      year = values[YEAR];\n    }\n    if (component.fields.year.hide) {\n      day = component.fields.day.hide && day === 0 ? 0 : values[DAY];\n      month = component.fields.month.hide && month === 0 ? 0 : values[MONTH];\n      year = 0;\n    }\n  }\n  if (!day && ((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.day) === null || _b === void 0 ? void 0 : _b.required)) {\n    return new error_1.FieldError('requiredDayField', context, 'day');\n  }\n  if (!month && ((_d = (_c = component.fields) === null || _c === void 0 ? void 0 : _c.month) === null || _d === void 0 ? void 0 : _d.required)) {\n    return new error_1.FieldError('requiredMonthField', context, 'day');\n  }\n  if (!year && ((_f = (_e = component.fields) === null || _e === void 0 ? void 0 : _e.year) === null || _f === void 0 ? void 0 : _f.required)) {\n    return new error_1.FieldError('requiredYearField', context, 'day');\n  }\n  return null;\n};\nexports.validateRequiredDaySync = validateRequiredDaySync;\nexports.validateRequiredDayInfo = {\n  name: 'validateRequiredDay',\n  process: exports.validateRequiredDay,\n  processSync: exports.validateRequiredDaySync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateRequiredDayInfo", "validateRequiredDaySync", "validateRequiredDay", "shouldValidate", "error_1", "require", "isValidatableDayComponent", "component", "_a", "_b", "_c", "_d", "_e", "_f", "type", "fields", "day", "required", "month", "year", "context", "FieldError", "ProcessorError", "DAY", "MONTH", "YEAR", "<PERSON><PERSON><PERSON><PERSON>", "values", "split", "map", "x", "parseInt", "length", "hide", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateRequiredDay.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateRequiredDayInfo = exports.validateRequiredDaySync = exports.validateRequiredDay = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableDayComponent = (component) => {\n    var _a, _b, _c, _d, _e, _f;\n    return (component &&\n        component.type === 'day' &&\n        (((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.day) === null || _b === void 0 ? void 0 : _b.required) ||\n            ((_d = (_c = component.fields) === null || _c === void 0 ? void 0 : _c.month) === null || _d === void 0 ? void 0 : _d.required) ||\n            ((_f = (_e = component.fields) === null || _e === void 0 ? void 0 : _e.year) === null || _f === void 0 ? void 0 : _f.required)));\n};\nconst shouldValidate = (context) => {\n    const { component } = context;\n    return isValidatableDayComponent(component);\n};\nexports.shouldValidate = shouldValidate;\nconst validateRequiredDay = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateRequiredDaySync)(context);\n});\nexports.validateRequiredDay = validateRequiredDay;\nconst validateRequiredDaySync = (context) => {\n    var _a, _b, _c, _d, _e, _f;\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    if (!isValidatableDayComponent(component)) {\n        return null;\n    }\n    if (!value) {\n        return new error_1.FieldError('requiredDayEmpty', context, 'day');\n    }\n    if (typeof value !== 'string') {\n        throw new error_1.ProcessorError(`Cannot validate required day field of ${value} because it is not a string`, context, 'validate:validateRequiredDay');\n    }\n    let [DAY, MONTH, YEAR] = component.dayFirst ? [0, 1, 2] : [1, 0, 2];\n    const values = value.split('/').map((x) => parseInt(x, 10));\n    let day = values[DAY];\n    let month = values[MONTH];\n    let year = values[YEAR];\n    if (values.length !== 3) {\n        if (component.fields.day.hide) {\n            MONTH = MONTH === 0 ? 0 : MONTH - 1;\n            YEAR = YEAR - 1;\n            day = 0;\n            month = values[MONTH];\n            year = values[YEAR];\n        }\n        if (component.fields.month.hide) {\n            DAY = DAY === 0 ? 0 : DAY - 1;\n            YEAR = YEAR - 1;\n            day = component.fields.day.hide && day === 0 ? 0 : values[DAY];\n            month = 0;\n            year = values[YEAR];\n        }\n        if (component.fields.year.hide) {\n            day = component.fields.day.hide && day === 0 ? 0 : values[DAY];\n            month = component.fields.month.hide && month === 0 ? 0 : values[MONTH];\n            year = 0;\n        }\n    }\n    if (!day && ((_b = (_a = component.fields) === null || _a === void 0 ? void 0 : _a.day) === null || _b === void 0 ? void 0 : _b.required)) {\n        return new error_1.FieldError('requiredDayField', context, 'day');\n    }\n    if (!month && ((_d = (_c = component.fields) === null || _c === void 0 ? void 0 : _c.month) === null || _d === void 0 ? void 0 : _d.required)) {\n        return new error_1.FieldError('requiredMonthField', context, 'day');\n    }\n    if (!year && ((_f = (_e = component.fields) === null || _e === void 0 ? void 0 : _e.year) === null || _f === void 0 ? void 0 : _f.required)) {\n        return new error_1.FieldError('requiredYearField', context, 'day');\n    }\n    return null;\n};\nexports.validateRequiredDaySync = validateRequiredDaySync;\nexports.validateRequiredDayInfo = {\n    name: 'validateRequiredDay',\n    process: exports.validateRequiredDay,\n    processSync: exports.validateRequiredDaySync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,uBAAuB,GAAGD,OAAO,CAACE,uBAAuB,GAAGF,OAAO,CAACG,mBAAmB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACjI,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,yBAAyB,GAAIC,SAAS,IAAK;EAC7C,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAC1B,OAAQN,SAAS,IACbA,SAAS,CAACO,IAAI,KAAK,KAAK,KACvB,CAAC,CAACL,EAAE,GAAG,CAACD,EAAE,GAAGD,SAAS,CAACQ,MAAM,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,GAAG,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,QAAQ,MACxH,CAACN,EAAE,GAAG,CAACD,EAAE,GAAGH,SAAS,CAACQ,MAAM,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,KAAK,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,QAAQ,CAAC,KAC9H,CAACJ,EAAE,GAAG,CAACD,EAAE,GAAGL,SAAS,CAACQ,MAAM,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,CAAC,CAAC;AAC3I,CAAC;AACD,MAAMd,cAAc,GAAIiB,OAAO,IAAK;EAChC,MAAM;IAAEb;EAAU,CAAC,GAAGa,OAAO;EAC7B,OAAOd,yBAAyB,CAACC,SAAS,CAAC;AAC/C,CAAC;AACDR,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,mBAAmB,GAAIkB,OAAO,IAAK1C,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACpF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,uBAAuB,EAAEmB,OAAO,CAAC;AACxD,CAAC,CAAC;AACFrB,OAAO,CAACG,mBAAmB,GAAGA,mBAAmB;AACjD,MAAMD,uBAAuB,GAAImB,OAAO,IAAK;EACzC,IAAIZ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAC1B,MAAM;IAAEN,SAAS;IAAEvB;EAAM,CAAC,GAAGoC,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAErB,OAAO,CAACI,cAAc,EAAEiB,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAI,CAACd,yBAAyB,CAACC,SAAS,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAI,CAACvB,KAAK,EAAE;IACR,OAAO,IAAIoB,OAAO,CAACiB,UAAU,CAAC,kBAAkB,EAAED,OAAO,EAAE,KAAK,CAAC;EACrE;EACA,IAAI,OAAOpC,KAAK,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIoB,OAAO,CAACkB,cAAc,CAAC,yCAAyCtC,KAAK,6BAA6B,EAAEoC,OAAO,EAAE,8BAA8B,CAAC;EAC1J;EACA,IAAI,CAACG,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAGlB,SAAS,CAACmB,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnE,MAAMC,MAAM,GAAG3C,KAAK,CAAC4C,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC;EAC3D,IAAId,GAAG,GAAGW,MAAM,CAACJ,GAAG,CAAC;EACrB,IAAIL,KAAK,GAAGS,MAAM,CAACH,KAAK,CAAC;EACzB,IAAIL,IAAI,GAAGQ,MAAM,CAACF,IAAI,CAAC;EACvB,IAAIE,MAAM,CAACK,MAAM,KAAK,CAAC,EAAE;IACrB,IAAIzB,SAAS,CAACQ,MAAM,CAACC,GAAG,CAACiB,IAAI,EAAE;MAC3BT,KAAK,GAAGA,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;MACnCC,IAAI,GAAGA,IAAI,GAAG,CAAC;MACfT,GAAG,GAAG,CAAC;MACPE,KAAK,GAAGS,MAAM,CAACH,KAAK,CAAC;MACrBL,IAAI,GAAGQ,MAAM,CAACF,IAAI,CAAC;IACvB;IACA,IAAIlB,SAAS,CAACQ,MAAM,CAACG,KAAK,CAACe,IAAI,EAAE;MAC7BV,GAAG,GAAGA,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC;MAC7BE,IAAI,GAAGA,IAAI,GAAG,CAAC;MACfT,GAAG,GAAGT,SAAS,CAACQ,MAAM,CAACC,GAAG,CAACiB,IAAI,IAAIjB,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGW,MAAM,CAACJ,GAAG,CAAC;MAC9DL,KAAK,GAAG,CAAC;MACTC,IAAI,GAAGQ,MAAM,CAACF,IAAI,CAAC;IACvB;IACA,IAAIlB,SAAS,CAACQ,MAAM,CAACI,IAAI,CAACc,IAAI,EAAE;MAC5BjB,GAAG,GAAGT,SAAS,CAACQ,MAAM,CAACC,GAAG,CAACiB,IAAI,IAAIjB,GAAG,KAAK,CAAC,GAAG,CAAC,GAAGW,MAAM,CAACJ,GAAG,CAAC;MAC9DL,KAAK,GAAGX,SAAS,CAACQ,MAAM,CAACG,KAAK,CAACe,IAAI,IAAIf,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGS,MAAM,CAACH,KAAK,CAAC;MACtEL,IAAI,GAAG,CAAC;IACZ;EACJ;EACA,IAAI,CAACH,GAAG,KAAK,CAACP,EAAE,GAAG,CAACD,EAAE,GAAGD,SAAS,CAACQ,MAAM,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,GAAG,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,QAAQ,CAAC,EAAE;IACvI,OAAO,IAAIb,OAAO,CAACiB,UAAU,CAAC,kBAAkB,EAAED,OAAO,EAAE,KAAK,CAAC;EACrE;EACA,IAAI,CAACF,KAAK,KAAK,CAACP,EAAE,GAAG,CAACD,EAAE,GAAGH,SAAS,CAACQ,MAAM,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,KAAK,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,QAAQ,CAAC,EAAE;IAC3I,OAAO,IAAIb,OAAO,CAACiB,UAAU,CAAC,oBAAoB,EAAED,OAAO,EAAE,KAAK,CAAC;EACvE;EACA,IAAI,CAACD,IAAI,KAAK,CAACN,EAAE,GAAG,CAACD,EAAE,GAAGL,SAAS,CAACQ,MAAM,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,CAAC,EAAE;IACzI,OAAO,IAAIb,OAAO,CAACiB,UAAU,CAAC,mBAAmB,EAAED,OAAO,EAAE,KAAK,CAAC;EACtE;EACA,OAAO,IAAI;AACf,CAAC;AACDrB,OAAO,CAACE,uBAAuB,GAAGA,uBAAuB;AACzDF,OAAO,CAACC,uBAAuB,GAAG;EAC9BkC,IAAI,EAAE,qBAAqB;EAC3BC,OAAO,EAAEpC,OAAO,CAACG,mBAAmB;EACpCkC,WAAW,EAAErC,OAAO,CAACE,uBAAuB;EAC5CoC,aAAa,EAAEtC,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}