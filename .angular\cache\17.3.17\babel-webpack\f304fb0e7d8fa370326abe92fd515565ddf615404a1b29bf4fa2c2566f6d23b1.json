{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMinimumValueInfo = exports.validateMinimumValueSync = exports.validateMinimumValue = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableNumberComponent = component => {\n  var _a;\n  return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('min'));\n};\nconst getValidationSetting = component => {\n  var _a;\n  let min = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.min;\n  if (typeof min === 'string') {\n    min = parseFloat(min);\n  }\n  return min;\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!isValidatableNumberComponent(component)) {\n    return false;\n  }\n  if (Number.isNaN(parseFloat(value))) {\n    return false;\n  }\n  if (Number.isNaN(getValidationSetting(component))) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMinimumValue = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMinimumValueSync)(context);\n});\nexports.validateMinimumValue = validateMinimumValue;\nconst validateMinimumValueSync = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  const min = getValidationSetting(component);\n  if (min === undefined) {\n    return null;\n  }\n  const parsedValue = typeof value === 'string' ? parseFloat(value) : Number(value);\n  if (Number.isNaN(parsedValue)) {\n    throw new error_1.ProcessorError(`Cannot validate value ${parsedValue} because it is invalid`, context, 'validate:validateMinimumValue');\n  }\n  return parsedValue >= min ? null : new error_1.FieldError('min', Object.assign(Object.assign({}, context), {\n    min: String(min),\n    setting: String(min)\n  }));\n};\nexports.validateMinimumValueSync = validateMinimumValueSync;\nexports.validateMinimumValueInfo = {\n  name: 'validateMinimumValue',\n  process: exports.validateMinimumValue,\n  processSync: exports.validateMinimumValueSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateMinimumValueInfo", "validateMinimumValueSync", "validateMinimumValue", "shouldValidate", "error_1", "require", "isValidatableNumberComponent", "component", "_a", "validate", "hasOwnProperty", "getValidationSetting", "min", "parseFloat", "context", "Number", "isNaN", "undefined", "parsedValue", "ProcessorError", "FieldError", "assign", "String", "setting", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMinimumValue.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMinimumValueInfo = exports.validateMinimumValueSync = exports.validateMinimumValue = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableNumberComponent = (component) => {\n    var _a;\n    return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('min'));\n};\nconst getValidationSetting = (component) => {\n    var _a;\n    let min = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.min;\n    if (typeof min === 'string') {\n        min = parseFloat(min);\n    }\n    return min;\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!isValidatableNumberComponent(component)) {\n        return false;\n    }\n    if (Number.isNaN(parseFloat(value))) {\n        return false;\n    }\n    if (Number.isNaN(getValidationSetting(component))) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMinimumValue = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMinimumValueSync)(context);\n});\nexports.validateMinimumValue = validateMinimumValue;\nconst validateMinimumValueSync = (context) => {\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    const min = getValidationSetting(component);\n    if (min === undefined) {\n        return null;\n    }\n    const parsedValue = typeof value === 'string' ? parseFloat(value) : Number(value);\n    if (Number.isNaN(parsedValue)) {\n        throw new error_1.ProcessorError(`Cannot validate value ${parsedValue} because it is invalid`, context, 'validate:validateMinimumValue');\n    }\n    return parsedValue >= min\n        ? null\n        : new error_1.FieldError('min', Object.assign(Object.assign({}, context), { min: String(min), setting: String(min) }));\n};\nexports.validateMinimumValueSync = validateMinimumValueSync;\nexports.validateMinimumValueInfo = {\n    name: 'validateMinimumValue',\n    process: exports.validateMinimumValue,\n    processSync: exports.validateMinimumValueSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,wBAAwB,GAAGD,OAAO,CAACE,wBAAwB,GAAGF,OAAO,CAACG,oBAAoB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACpI,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,4BAA4B,GAAIC,SAAS,IAAK;EAChD,IAAIC,EAAE;EACN,OAAOD,SAAS,KAAK,CAACC,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,cAAc,CAAC,KAAK,CAAC,CAAC;AACjH,CAAC;AACD,MAAMC,oBAAoB,GAAIJ,SAAS,IAAK;EACxC,IAAIC,EAAE;EACN,IAAII,GAAG,GAAG,CAACJ,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,GAAG;EAC/E,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACzBA,GAAG,GAAGC,UAAU,CAACD,GAAG,CAAC;EACzB;EACA,OAAOA,GAAG;AACd,CAAC;AACD,MAAMT,cAAc,GAAIW,OAAO,IAAK;EAChC,MAAM;IAAEP,SAAS;IAAEvB;EAAM,CAAC,GAAG8B,OAAO;EACpC,IAAI,CAACR,4BAA4B,CAACC,SAAS,CAAC,EAAE;IAC1C,OAAO,KAAK;EAChB;EACA,IAAIQ,MAAM,CAACC,KAAK,CAACH,UAAU,CAAC7B,KAAK,CAAC,CAAC,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,IAAI+B,MAAM,CAACC,KAAK,CAACL,oBAAoB,CAACJ,SAAS,CAAC,CAAC,EAAE;IAC/C,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDR,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,oBAAoB,GAAIY,OAAO,IAAKpC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACrF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,wBAAwB,EAAEa,OAAO,CAAC;AACzD,CAAC,CAAC;AACFf,OAAO,CAACG,oBAAoB,GAAGA,oBAAoB;AACnD,MAAMD,wBAAwB,GAAIa,OAAO,IAAK;EAC1C,MAAM;IAAEP,SAAS;IAAEvB;EAAM,CAAC,GAAG8B,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAEf,OAAO,CAACI,cAAc,EAAEW,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,MAAMF,GAAG,GAAGD,oBAAoB,CAACJ,SAAS,CAAC;EAC3C,IAAIK,GAAG,KAAKK,SAAS,EAAE;IACnB,OAAO,IAAI;EACf;EACA,MAAMC,WAAW,GAAG,OAAOlC,KAAK,KAAK,QAAQ,GAAG6B,UAAU,CAAC7B,KAAK,CAAC,GAAG+B,MAAM,CAAC/B,KAAK,CAAC;EACjF,IAAI+B,MAAM,CAACC,KAAK,CAACE,WAAW,CAAC,EAAE;IAC3B,MAAM,IAAId,OAAO,CAACe,cAAc,CAAC,yBAAyBD,WAAW,wBAAwB,EAAEJ,OAAO,EAAE,+BAA+B,CAAC;EAC5I;EACA,OAAOI,WAAW,IAAIN,GAAG,GACnB,IAAI,GACJ,IAAIR,OAAO,CAACgB,UAAU,CAAC,KAAK,EAAEvB,MAAM,CAACwB,MAAM,CAACxB,MAAM,CAACwB,MAAM,CAAC,CAAC,CAAC,EAAEP,OAAO,CAAC,EAAE;IAAEF,GAAG,EAAEU,MAAM,CAACV,GAAG,CAAC;IAAEW,OAAO,EAAED,MAAM,CAACV,GAAG;EAAE,CAAC,CAAC,CAAC;AAC9H,CAAC;AACDb,OAAO,CAACE,wBAAwB,GAAGA,wBAAwB;AAC3DF,OAAO,CAACC,wBAAwB,GAAG;EAC/BwB,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,EAAE1B,OAAO,CAACG,oBAAoB;EACrCwB,WAAW,EAAE3B,OAAO,CAACE,wBAAwB;EAC7C0B,aAAa,EAAE5B,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}