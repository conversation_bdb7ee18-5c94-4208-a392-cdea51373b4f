{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.interpolate = exports.evaluate = exports.attachResourceToDom = exports.unescapeHTML = exports.boolValue = exports.escapeRegExCharacters = void 0;\nconst lodash_1 = require(\"lodash\");\nconst Evaluator_1 = require(\"./Evaluator\");\nconst formUtil_1 = require(\"./formUtil\");\n/**\n * Escapes RegEx characters in provided String value.\n *\n * @param {String} value\n *   String for escaping RegEx characters.\n * @returns {string}\n *   String with escaped RegEx characters.\n */\nfunction escapeRegExCharacters(value) {\n  return value.replace(/[-[\\]/{}()*+?.\\\\^$|]/g, '\\\\$&');\n}\nexports.escapeRegExCharacters = escapeRegExCharacters;\n/**\n * Determines the boolean value of a setting.\n *\n * @param value\n * @return {boolean}\n */\nfunction boolValue(value) {\n  if ((0, lodash_1.isBoolean)(value)) {\n    return value;\n  } else if ((0, lodash_1.isString)(value)) {\n    return value.toLowerCase() === 'true';\n  } else {\n    return !!value;\n  }\n}\nexports.boolValue = boolValue;\n/**\n * Unescape HTML characters like &lt, &gt, &amp and etc.\n * @param str\n * @returns {string}\n */\nfunction unescapeHTML(str) {\n  if (typeof window === 'undefined' || !('DOMParser' in window)) {\n    return str;\n  }\n  const doc = new window.DOMParser().parseFromString(str, 'text/html');\n  return doc.documentElement.textContent;\n}\nexports.unescapeHTML = unescapeHTML;\nfunction attachResourceToDom(options) {\n  const {\n    name,\n    formio,\n    onload,\n    rootElement\n  } = options;\n  let {\n    src\n  } = options;\n  src = Array.isArray(src) ? src : [src];\n  src.forEach(lib => {\n    let attrs = {};\n    let elementType = '';\n    if (typeof lib === 'string') {\n      lib = {\n        type: 'script',\n        src: lib\n      };\n    }\n    switch (lib.type) {\n      case 'script':\n        elementType = 'script';\n        attrs = {\n          src: lib.src,\n          type: 'text/javascript',\n          defer: true,\n          async: true,\n          referrerpolicy: 'origin'\n        };\n        break;\n      case 'styles':\n        elementType = 'link';\n        attrs = {\n          href: lib.src,\n          rel: 'stylesheet'\n        };\n        break;\n    }\n    // Add the script to the top of the page.\n    const element = document.createElement(elementType);\n    if (element.setAttribute) {\n      for (const attr in attrs) {\n        element.setAttribute(attr, attrs[attr]);\n      }\n    }\n    if (onload) {\n      element.addEventListener('load', () => {\n        formio.libraries[name].loaded = true;\n        onload(formio.libraries[name].ready);\n      });\n    }\n    if (rootElement) {\n      rootElement.insertAdjacentElement('afterend', element);\n      return;\n    }\n    const {\n      head\n    } = document;\n    if (head) {\n      head.appendChild(element);\n    }\n  });\n}\nexports.attachResourceToDom = attachResourceToDom;\n/**\n * A convenience function that wraps Evaluator.evaluate and normalizes context values\n * @param evaluation - The code string to evaluate\n * @param context - The processor context\n * @param ret - The return value\n * @param interpolate - Whether or not to interpolate the code string before evaluating\n * @param evalContextFn - A callback to mutate the context value after it has been normalized\n * @param options - Options to pass to the Evaluator\n * @returns {*} - Returns the result of the evaluation\n */\nfunction evaluate(evaluation, context, ret = 'result', interpolate = false, evalContextFn, options = {}) {\n  const {\n    instance,\n    form\n  } = context;\n  const normalizedContext = (0, formUtil_1.normalizeContext)(context);\n  if (evalContextFn) {\n    evalContextFn(normalizedContext);\n  }\n  if (form === null || form === void 0 ? void 0 : form.module) {\n    options = Object.assign(Object.assign({}, options), {\n      formModule: form.module\n    });\n  }\n  if (instance && instance.evaluate) {\n    return instance.evaluate(evaluation, normalizedContext, ret, interpolate, options);\n  }\n  return Evaluator_1.Evaluator.evaluate(evaluation, normalizedContext, ret, interpolate, context, options);\n}\nexports.evaluate = evaluate;\nfunction interpolate(evaluation, context, evalContextFn) {\n  const {\n    instance\n  } = context;\n  const normalizedContext = (0, formUtil_1.normalizeContext)(context);\n  if (evalContextFn) {\n    evalContextFn(normalizedContext);\n  }\n  if (instance && instance.interpolate) {\n    return instance.interpolate(evaluation, normalizedContext, {\n      noeval: true\n    });\n  }\n  return Evaluator_1.Evaluator.interpolate(evaluation, normalizedContext, {\n    noeval: true\n  });\n}\nexports.interpolate = interpolate;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "interpolate", "evaluate", "attachResourceToDom", "unescapeHTML", "boolValue", "escapeRegExCharacters", "lodash_1", "require", "Evaluator_1", "formUtil_1", "replace", "isBoolean", "isString", "toLowerCase", "str", "window", "doc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "documentElement", "textContent", "options", "name", "formio", "onload", "rootElement", "src", "Array", "isArray", "for<PERSON>ach", "lib", "attrs", "elementType", "type", "defer", "async", "referrerpolicy", "href", "rel", "element", "document", "createElement", "setAttribute", "attr", "addEventListener", "libraries", "loaded", "ready", "insertAdjacentElement", "head", "append<PERSON><PERSON><PERSON>", "evaluation", "context", "ret", "evalContextFn", "instance", "form", "normalizedContext", "normalizeContext", "module", "assign", "formModule", "Evaluator", "noeval"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/utils/utils.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.interpolate = exports.evaluate = exports.attachResourceToDom = exports.unescapeHTML = exports.boolValue = exports.escapeRegExCharacters = void 0;\nconst lodash_1 = require(\"lodash\");\nconst Evaluator_1 = require(\"./Evaluator\");\nconst formUtil_1 = require(\"./formUtil\");\n/**\n * Escapes RegEx characters in provided String value.\n *\n * @param {String} value\n *   String for escaping RegEx characters.\n * @returns {string}\n *   String with escaped RegEx characters.\n */\nfunction escapeRegExCharacters(value) {\n    return value.replace(/[-[\\]/{}()*+?.\\\\^$|]/g, '\\\\$&');\n}\nexports.escapeRegExCharacters = escapeRegExCharacters;\n/**\n * Determines the boolean value of a setting.\n *\n * @param value\n * @return {boolean}\n */\nfunction boolValue(value) {\n    if ((0, lodash_1.isBoolean)(value)) {\n        return value;\n    }\n    else if ((0, lodash_1.isString)(value)) {\n        return value.toLowerCase() === 'true';\n    }\n    else {\n        return !!value;\n    }\n}\nexports.boolValue = boolValue;\n/**\n * Unescape HTML characters like &lt, &gt, &amp and etc.\n * @param str\n * @returns {string}\n */\nfunction unescapeHTML(str) {\n    if (typeof window === 'undefined' || !('DOMParser' in window)) {\n        return str;\n    }\n    const doc = new window.DOMParser().parseFromString(str, 'text/html');\n    return doc.documentElement.textContent;\n}\nexports.unescapeHTML = unescapeHTML;\nfunction attachResourceToDom(options) {\n    const { name, formio, onload, rootElement } = options;\n    let { src } = options;\n    src = Array.isArray(src) ? src : [src];\n    src.forEach((lib) => {\n        let attrs = {};\n        let elementType = '';\n        if (typeof lib === 'string') {\n            lib = {\n                type: 'script',\n                src: lib,\n            };\n        }\n        switch (lib.type) {\n            case 'script':\n                elementType = 'script';\n                attrs = {\n                    src: lib.src,\n                    type: 'text/javascript',\n                    defer: true,\n                    async: true,\n                    referrerpolicy: 'origin',\n                };\n                break;\n            case 'styles':\n                elementType = 'link';\n                attrs = {\n                    href: lib.src,\n                    rel: 'stylesheet',\n                };\n                break;\n        }\n        // Add the script to the top of the page.\n        const element = document.createElement(elementType);\n        if (element.setAttribute) {\n            for (const attr in attrs) {\n                element.setAttribute(attr, attrs[attr]);\n            }\n        }\n        if (onload) {\n            element.addEventListener('load', () => {\n                formio.libraries[name].loaded = true;\n                onload(formio.libraries[name].ready);\n            });\n        }\n        if (rootElement) {\n            rootElement.insertAdjacentElement('afterend', element);\n            return;\n        }\n        const { head } = document;\n        if (head) {\n            head.appendChild(element);\n        }\n    });\n}\nexports.attachResourceToDom = attachResourceToDom;\n/**\n * A convenience function that wraps Evaluator.evaluate and normalizes context values\n * @param evaluation - The code string to evaluate\n * @param context - The processor context\n * @param ret - The return value\n * @param interpolate - Whether or not to interpolate the code string before evaluating\n * @param evalContextFn - A callback to mutate the context value after it has been normalized\n * @param options - Options to pass to the Evaluator\n * @returns {*} - Returns the result of the evaluation\n */\nfunction evaluate(evaluation, context, ret = 'result', interpolate = false, evalContextFn, options = {}) {\n    const { instance, form } = context;\n    const normalizedContext = (0, formUtil_1.normalizeContext)(context);\n    if (evalContextFn) {\n        evalContextFn(normalizedContext);\n    }\n    if (form === null || form === void 0 ? void 0 : form.module) {\n        options = Object.assign(Object.assign({}, options), { formModule: form.module });\n    }\n    if (instance && instance.evaluate) {\n        return instance.evaluate(evaluation, normalizedContext, ret, interpolate, options);\n    }\n    return Evaluator_1.Evaluator.evaluate(evaluation, normalizedContext, ret, interpolate, context, options);\n}\nexports.evaluate = evaluate;\nfunction interpolate(evaluation, context, evalContextFn) {\n    const { instance } = context;\n    const normalizedContext = (0, formUtil_1.normalizeContext)(context);\n    if (evalContextFn) {\n        evalContextFn(normalizedContext);\n    }\n    if (instance && instance.interpolate) {\n        return instance.interpolate(evaluation, normalizedContext, {\n            noeval: true,\n        });\n    }\n    return Evaluator_1.Evaluator.interpolate(evaluation, normalizedContext, {\n        noeval: true,\n    });\n}\nexports.interpolate = interpolate;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACG,QAAQ,GAAGH,OAAO,CAACI,mBAAmB,GAAGJ,OAAO,CAACK,YAAY,GAAGL,OAAO,CAACM,SAAS,GAAGN,OAAO,CAACO,qBAAqB,GAAG,KAAK,CAAC;AACxJ,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,WAAW,GAAGD,OAAO,CAAC,aAAa,CAAC;AAC1C,MAAME,UAAU,GAAGF,OAAO,CAAC,YAAY,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,qBAAqBA,CAACN,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACW,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC;AACzD;AACAZ,OAAO,CAACO,qBAAqB,GAAGA,qBAAqB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,SAASA,CAACL,KAAK,EAAE;EACtB,IAAI,CAAC,CAAC,EAAEO,QAAQ,CAACK,SAAS,EAAEZ,KAAK,CAAC,EAAE;IAChC,OAAOA,KAAK;EAChB,CAAC,MACI,IAAI,CAAC,CAAC,EAAEO,QAAQ,CAACM,QAAQ,EAAEb,KAAK,CAAC,EAAE;IACpC,OAAOA,KAAK,CAACc,WAAW,CAAC,CAAC,KAAK,MAAM;EACzC,CAAC,MACI;IACD,OAAO,CAAC,CAACd,KAAK;EAClB;AACJ;AACAD,OAAO,CAACM,SAAS,GAAGA,SAAS;AAC7B;AACA;AACA;AACA;AACA;AACA,SAASD,YAAYA,CAACW,GAAG,EAAE;EACvB,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,EAAE,WAAW,IAAIA,MAAM,CAAC,EAAE;IAC3D,OAAOD,GAAG;EACd;EACA,MAAME,GAAG,GAAG,IAAID,MAAM,CAACE,SAAS,CAAC,CAAC,CAACC,eAAe,CAACJ,GAAG,EAAE,WAAW,CAAC;EACpE,OAAOE,GAAG,CAACG,eAAe,CAACC,WAAW;AAC1C;AACAtB,OAAO,CAACK,YAAY,GAAGA,YAAY;AACnC,SAASD,mBAAmBA,CAACmB,OAAO,EAAE;EAClC,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAY,CAAC,GAAGJ,OAAO;EACrD,IAAI;IAAEK;EAAI,CAAC,GAAGL,OAAO;EACrBK,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;EACtCA,GAAG,CAACG,OAAO,CAAEC,GAAG,IAAK;IACjB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;MACzBA,GAAG,GAAG;QACFG,IAAI,EAAE,QAAQ;QACdP,GAAG,EAAEI;MACT,CAAC;IACL;IACA,QAAQA,GAAG,CAACG,IAAI;MACZ,KAAK,QAAQ;QACTD,WAAW,GAAG,QAAQ;QACtBD,KAAK,GAAG;UACJL,GAAG,EAAEI,GAAG,CAACJ,GAAG;UACZO,IAAI,EAAE,iBAAiB;UACvBC,KAAK,EAAE,IAAI;UACXC,KAAK,EAAE,IAAI;UACXC,cAAc,EAAE;QACpB,CAAC;QACD;MACJ,KAAK,QAAQ;QACTJ,WAAW,GAAG,MAAM;QACpBD,KAAK,GAAG;UACJM,IAAI,EAAEP,GAAG,CAACJ,GAAG;UACbY,GAAG,EAAE;QACT,CAAC;QACD;IACR;IACA;IACA,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAACT,WAAW,CAAC;IACnD,IAAIO,OAAO,CAACG,YAAY,EAAE;MACtB,KAAK,MAAMC,IAAI,IAAIZ,KAAK,EAAE;QACtBQ,OAAO,CAACG,YAAY,CAACC,IAAI,EAAEZ,KAAK,CAACY,IAAI,CAAC,CAAC;MAC3C;IACJ;IACA,IAAInB,MAAM,EAAE;MACRe,OAAO,CAACK,gBAAgB,CAAC,MAAM,EAAE,MAAM;QACnCrB,MAAM,CAACsB,SAAS,CAACvB,IAAI,CAAC,CAACwB,MAAM,GAAG,IAAI;QACpCtB,MAAM,CAACD,MAAM,CAACsB,SAAS,CAACvB,IAAI,CAAC,CAACyB,KAAK,CAAC;MACxC,CAAC,CAAC;IACN;IACA,IAAItB,WAAW,EAAE;MACbA,WAAW,CAACuB,qBAAqB,CAAC,UAAU,EAAET,OAAO,CAAC;MACtD;IACJ;IACA,MAAM;MAAEU;IAAK,CAAC,GAAGT,QAAQ;IACzB,IAAIS,IAAI,EAAE;MACNA,IAAI,CAACC,WAAW,CAACX,OAAO,CAAC;IAC7B;EACJ,CAAC,CAAC;AACN;AACAzC,OAAO,CAACI,mBAAmB,GAAGA,mBAAmB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,QAAQA,CAACkD,UAAU,EAAEC,OAAO,EAAEC,GAAG,GAAG,QAAQ,EAAErD,WAAW,GAAG,KAAK,EAAEsD,aAAa,EAAEjC,OAAO,GAAG,CAAC,CAAC,EAAE;EACrG,MAAM;IAAEkC,QAAQ;IAAEC;EAAK,CAAC,GAAGJ,OAAO;EAClC,MAAMK,iBAAiB,GAAG,CAAC,CAAC,EAAEhD,UAAU,CAACiD,gBAAgB,EAAEN,OAAO,CAAC;EACnE,IAAIE,aAAa,EAAE;IACfA,aAAa,CAACG,iBAAiB,CAAC;EACpC;EACA,IAAID,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACG,MAAM,EAAE;IACzDtC,OAAO,GAAGzB,MAAM,CAACgE,MAAM,CAAChE,MAAM,CAACgE,MAAM,CAAC,CAAC,CAAC,EAAEvC,OAAO,CAAC,EAAE;MAAEwC,UAAU,EAAEL,IAAI,CAACG;IAAO,CAAC,CAAC;EACpF;EACA,IAAIJ,QAAQ,IAAIA,QAAQ,CAACtD,QAAQ,EAAE;IAC/B,OAAOsD,QAAQ,CAACtD,QAAQ,CAACkD,UAAU,EAAEM,iBAAiB,EAAEJ,GAAG,EAAErD,WAAW,EAAEqB,OAAO,CAAC;EACtF;EACA,OAAOb,WAAW,CAACsD,SAAS,CAAC7D,QAAQ,CAACkD,UAAU,EAAEM,iBAAiB,EAAEJ,GAAG,EAAErD,WAAW,EAAEoD,OAAO,EAAE/B,OAAO,CAAC;AAC5G;AACAvB,OAAO,CAACG,QAAQ,GAAGA,QAAQ;AAC3B,SAASD,WAAWA,CAACmD,UAAU,EAAEC,OAAO,EAAEE,aAAa,EAAE;EACrD,MAAM;IAAEC;EAAS,CAAC,GAAGH,OAAO;EAC5B,MAAMK,iBAAiB,GAAG,CAAC,CAAC,EAAEhD,UAAU,CAACiD,gBAAgB,EAAEN,OAAO,CAAC;EACnE,IAAIE,aAAa,EAAE;IACfA,aAAa,CAACG,iBAAiB,CAAC;EACpC;EACA,IAAIF,QAAQ,IAAIA,QAAQ,CAACvD,WAAW,EAAE;IAClC,OAAOuD,QAAQ,CAACvD,WAAW,CAACmD,UAAU,EAAEM,iBAAiB,EAAE;MACvDM,MAAM,EAAE;IACZ,CAAC,CAAC;EACN;EACA,OAAOvD,WAAW,CAACsD,SAAS,CAAC9D,WAAW,CAACmD,UAAU,EAAEM,iBAAiB,EAAE;IACpEM,MAAM,EAAE;EACZ,CAAC,CAAC;AACN;AACAjE,OAAO,CAACE,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}