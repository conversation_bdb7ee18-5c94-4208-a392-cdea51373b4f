{"ast": null, "code": "Object.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = function (ctx) {\n  var __t,\n    __p = '',\n    __j = Array.prototype.join;\n  function print() {\n    __p += __j.call(arguments, '');\n  }\n  __p += '<div class=\"node-edit\">\\n  <div ref=\"nodeEdit\">' + ((__t = ctx.children) == null ? '' : __t) + '</div>\\n  ';\n  if (!ctx.readOnly) {\n    ;\n    __p += '\\n    <div class=\"node-actions\">\\n      <button ref=\"saveNode\" class=\"btn btn-primary saveNode\">' + ((__t = ctx.t('Save')) == null ? '' : __t) + '</button>\\n      <button ref=\"cancelNode\" class=\"btn btn-danger cancelNode\">' + ((__t = ctx.t('Cancel')) == null ? '' : __t) + '</button>\\n    </div>\\n  ';\n  }\n  ;\n  __p += '\\n</div>\\n';\n  return __p;\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ctx", "__t", "__p", "__j", "Array", "prototype", "join", "print", "call", "arguments", "children", "readOnly", "t"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/bootstrap/lib/cjs/templates/bootstrap5/tree/partials/edit.ejs.js"], "sourcesContent": ["Object.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default=function(ctx) {\nvar __t, __p = '', __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n__p += '<div class=\"node-edit\">\\n  <div ref=\"nodeEdit\">' +\n((__t = ( ctx.children )) == null ? '' : __t) +\n'</div>\\n  ';\n if (!ctx.readOnly) { ;\n__p += '\\n    <div class=\"node-actions\">\\n      <button ref=\"saveNode\" class=\"btn btn-primary saveNode\">' +\n((__t = ( ctx.t('Save') )) == null ? '' : __t) +\n'</button>\\n      <button ref=\"cancelNode\" class=\"btn btn-danger cancelNode\">' +\n((__t = ( ctx.t('Cancel') )) == null ? '' : __t) +\n'</button>\\n    </div>\\n  ';\n } ;\n__p += '\\n</div>\\n';\nreturn __p\n}"], "mappings": "AAAAA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAC,UAASC,GAAG,EAAE;EAC9B,IAAIC,GAAG;IAAEC,GAAG,GAAG,EAAE;IAAEC,GAAG,GAAGC,KAAK,CAACC,SAAS,CAACC,IAAI;EAC7C,SAASC,KAAKA,CAAA,EAAG;IAAEL,GAAG,IAAIC,GAAG,CAACK,IAAI,CAACC,SAAS,EAAE,EAAE,CAAC;EAAC;EAClDP,GAAG,IAAI,iDAAiD,IACvD,CAACD,GAAG,GAAKD,GAAG,CAACU,QAAU,KAAK,IAAI,GAAG,EAAE,GAAGT,GAAG,CAAC,GAC7C,YAAY;EACX,IAAI,CAACD,GAAG,CAACW,QAAQ,EAAE;IAAE;IACtBT,GAAG,IAAI,kGAAkG,IACxG,CAACD,GAAG,GAAKD,GAAG,CAACY,CAAC,CAAC,MAAM,CAAG,KAAK,IAAI,GAAG,EAAE,GAAGX,GAAG,CAAC,GAC9C,8EAA8E,IAC7E,CAACA,GAAG,GAAKD,GAAG,CAACY,CAAC,CAAC,QAAQ,CAAG,KAAK,IAAI,GAAG,EAAE,GAAGX,GAAG,CAAC,GAChD,2BAA2B;EAC1B;EAAE;EACHC,GAAG,IAAI,YAAY;EACnB,OAAOA,GAAG;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}