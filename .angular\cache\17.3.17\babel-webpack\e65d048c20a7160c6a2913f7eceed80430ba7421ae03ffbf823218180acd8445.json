{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.databaseRules = void 0;\nconst validateUnique_1 = require(\"./validateUnique\");\nconst validateResourceSelectValue_1 = require(\"./validateResourceSelectValue\");\n// These are the validations that require a database connection.\nexports.databaseRules = [validateUnique_1.validateUniqueInfo, validateResourceSelectValue_1.validateResourceSelectValueInfo];", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "databaseRules", "validateUnique_1", "require", "validateResourceSelectValue_1", "validateUniqueInfo", "validateResourceSelectValueInfo"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/databaseRules.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.databaseRules = void 0;\nconst validateUnique_1 = require(\"./validateUnique\");\nconst validateResourceSelectValue_1 = require(\"./validateResourceSelectValue\");\n// These are the validations that require a database connection.\nexports.databaseRules = [\n    validateUnique_1.validateUniqueInfo,\n    validateResourceSelectValue_1.validateResourceSelectValueInfo,\n];\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,aAAa,GAAG,KAAK,CAAC;AAC9B,MAAMC,gBAAgB,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACpD,MAAMC,6BAA6B,GAAGD,OAAO,CAAC,+BAA+B,CAAC;AAC9E;AACAJ,OAAO,CAACE,aAAa,GAAG,CACpBC,gBAAgB,CAACG,kBAAkB,EACnCD,6BAA6B,CAACE,+BAA+B,CAChE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}