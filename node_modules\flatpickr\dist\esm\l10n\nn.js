var fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export var NorwegianNynorsk = {
    weekdays: {
        shorthand: ["Sø.", "<PERSON><PERSON>.", "<PERSON>.", "<PERSON>.", "<PERSON>.", "<PERSON>.", "<PERSON>."],
        longhand: [
            "Søndag",
            "Måndag",
            "Tysdag",
            "Onsdag",
            "Torsdag",
            "Fredag",
            "Laurdag",
        ],
    },
    months: {
        shorthand: [
            "Jan",
            "Feb",
            "Mars",
            "Apr",
            "<PERSON>",
            "Juni",
            "Juli",
            "Aug",
            "Sep",
            "Okt",
            "Nov",
            "Des",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "April",
            "<PERSON>",
            "<PERSON>i",
            "Juli",
            "August",
            "September",
            "Oktober",
            "November",
            "Desember",
        ],
    },
    firstDayOfWeek: 1,
    rangeSeparator: " til ",
    weekAbbreviation: "Veke",
    scrollTitle: "Scroll for å endre",
    toggleTitle: "Klikk for å veksle",
    time_24hr: true,
    ordinal: function () {
        return ".";
    },
};
fp.l10ns.nn = NorwegianNynorsk;
export default fp.l10ns;
