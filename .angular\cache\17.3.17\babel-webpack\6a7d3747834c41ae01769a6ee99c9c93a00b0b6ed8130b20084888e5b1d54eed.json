{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateResourceSelectValueInfo = exports.validateResourceSelectValue = exports.shouldValidate = exports.generateUrl = void 0;\nconst error_1 = require(\"../../../error\");\nconst util_1 = require(\"../util\");\nconst isValidatableSelectComponent = component => {\n  var _a;\n  return component && component.type === 'select' && (0, util_1.toBoolean)(component.dataSrc === 'resource') && (0, util_1.toBoolean)((_a = component.validate) === null || _a === void 0 ? void 0 : _a.select);\n};\nconst generateUrl = (baseUrl, component, value) => {\n  const url = baseUrl;\n  const query = url.searchParams;\n  if (component.searchField) {\n    let searchValue = value;\n    if (component.valueProperty) {\n      searchValue = value[component.valueProperty];\n    } else {\n      searchValue = value;\n    }\n    query.set(component.searchField, typeof searchValue === 'string' ? searchValue : JSON.stringify(searchValue));\n  }\n  if (component.selectFields) {\n    query.set('select', component.selectFields);\n  }\n  if (component.sort) {\n    query.set('sort', component.sort);\n  }\n  if (component.filter) {\n    const filterQueryStrings = new URLSearchParams(component.filter);\n    filterQueryStrings.forEach((value, key) => query.set(key, value));\n  }\n  return url;\n};\nexports.generateUrl = generateUrl;\nconst shouldValidate = context => {\n  var _a;\n  const {\n    component,\n    value,\n    config\n  } = context;\n  // Only run this validation if server-side\n  if (!(config === null || config === void 0 ? void 0 : config.server)) {\n    return false;\n  }\n  if (!isValidatableSelectComponent(component)) {\n    return false;\n  }\n  if (!value || (0, util_1.isEmptyObject)(value) || Array.isArray(value) && value.length === 0) {\n    return false;\n  }\n  // If given an invalid configuration, do not validate the remote value\n  if (component.dataSrc !== 'resource' || !((_a = component.data) === null || _a === void 0 ? void 0 : _a.resource)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateResourceSelectValue = context => __awaiter(void 0, void 0, void 0, function* () {\n  var _a;\n  const {\n    value,\n    config\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  if (!config || !config.database) {\n    throw new error_1.ProcessorError(\"Can't validate for resource value without a database config object\", context, 'validate:validateResourceSelectValue');\n  }\n  try {\n    const resourceSelectValueResult = yield (_a = config.database) === null || _a === void 0 ? void 0 : _a.validateResourceSelectValue(context, value);\n    return resourceSelectValueResult === true ? null : new error_1.FieldError('select', context);\n  } catch (err) {\n    throw new error_1.ProcessorError(err.message || err, context, 'validate:validateResourceSelectValue');\n  }\n});\nexports.validateResourceSelectValue = validateResourceSelectValue;\nexports.validateResourceSelectValueInfo = {\n  name: 'validateResourceSelectValue',\n  process: exports.validateResourceSelectValue,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateResourceSelectValueInfo", "validateResourceSelectValue", "shouldValidate", "generateUrl", "error_1", "require", "util_1", "isValidatableSelectComponent", "component", "_a", "type", "toBoolean", "dataSrc", "validate", "select", "baseUrl", "url", "query", "searchParams", "searchField", "searchValue", "valueProperty", "set", "JSON", "stringify", "selectFields", "sort", "filter", "filterQueryStrings", "URLSearchParams", "for<PERSON>ach", "key", "context", "config", "server", "isEmptyObject", "Array", "isArray", "length", "data", "resource", "database", "ProcessorError", "resourceSelectValueResult", "FieldError", "err", "message", "name", "process", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateResourceSelectValue.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateResourceSelectValueInfo = exports.validateResourceSelectValue = exports.shouldValidate = exports.generateUrl = void 0;\nconst error_1 = require(\"../../../error\");\nconst util_1 = require(\"../util\");\nconst isValidatableSelectComponent = (component) => {\n    var _a;\n    return (component &&\n        component.type === 'select' &&\n        (0, util_1.toBoolean)(component.dataSrc === 'resource') &&\n        (0, util_1.toBoolean)((_a = component.validate) === null || _a === void 0 ? void 0 : _a.select));\n};\nconst generateUrl = (baseUrl, component, value) => {\n    const url = baseUrl;\n    const query = url.searchParams;\n    if (component.searchField) {\n        let searchValue = value;\n        if (component.valueProperty) {\n            searchValue = value[component.valueProperty];\n        }\n        else {\n            searchValue = value;\n        }\n        query.set(component.searchField, typeof searchValue === 'string' ? searchValue : JSON.stringify(searchValue));\n    }\n    if (component.selectFields) {\n        query.set('select', component.selectFields);\n    }\n    if (component.sort) {\n        query.set('sort', component.sort);\n    }\n    if (component.filter) {\n        const filterQueryStrings = new URLSearchParams(component.filter);\n        filterQueryStrings.forEach((value, key) => query.set(key, value));\n    }\n    return url;\n};\nexports.generateUrl = generateUrl;\nconst shouldValidate = (context) => {\n    var _a;\n    const { component, value, config } = context;\n    // Only run this validation if server-side\n    if (!(config === null || config === void 0 ? void 0 : config.server)) {\n        return false;\n    }\n    if (!isValidatableSelectComponent(component)) {\n        return false;\n    }\n    if (!value ||\n        (0, util_1.isEmptyObject)(value) ||\n        (Array.isArray(value) && value.length === 0)) {\n        return false;\n    }\n    // If given an invalid configuration, do not validate the remote value\n    if (component.dataSrc !== 'resource' || !((_a = component.data) === null || _a === void 0 ? void 0 : _a.resource)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateResourceSelectValue = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    const { value, config } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    if (!config || !config.database) {\n        throw new error_1.ProcessorError(\"Can't validate for resource value without a database config object\", context, 'validate:validateResourceSelectValue');\n    }\n    try {\n        const resourceSelectValueResult = yield ((_a = config.database) === null || _a === void 0 ? void 0 : _a.validateResourceSelectValue(context, value));\n        return resourceSelectValueResult === true ? null : new error_1.FieldError('select', context);\n    }\n    catch (err) {\n        throw new error_1.ProcessorError(err.message || err, context, 'validate:validateResourceSelectValue');\n    }\n});\nexports.validateResourceSelectValue = validateResourceSelectValue;\nexports.validateResourceSelectValueInfo = {\n    name: 'validateResourceSelectValue',\n    process: exports.validateResourceSelectValue,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,+BAA+B,GAAGD,OAAO,CAACE,2BAA2B,GAAGF,OAAO,CAACG,cAAc,GAAGH,OAAO,CAACI,WAAW,GAAG,KAAK,CAAC;AACrI,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,MAAM,GAAGD,OAAO,CAAC,SAAS,CAAC;AACjC,MAAME,4BAA4B,GAAIC,SAAS,IAAK;EAChD,IAAIC,EAAE;EACN,OAAQD,SAAS,IACbA,SAAS,CAACE,IAAI,KAAK,QAAQ,IAC3B,CAAC,CAAC,EAAEJ,MAAM,CAACK,SAAS,EAAEH,SAAS,CAACI,OAAO,KAAK,UAAU,CAAC,IACvD,CAAC,CAAC,EAAEN,MAAM,CAACK,SAAS,EAAE,CAACF,EAAE,GAAGD,SAAS,CAACK,QAAQ,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,MAAM,CAAC;AACvG,CAAC;AACD,MAAMX,WAAW,GAAGA,CAACY,OAAO,EAAEP,SAAS,EAAExB,KAAK,KAAK;EAC/C,MAAMgC,GAAG,GAAGD,OAAO;EACnB,MAAME,KAAK,GAAGD,GAAG,CAACE,YAAY;EAC9B,IAAIV,SAAS,CAACW,WAAW,EAAE;IACvB,IAAIC,WAAW,GAAGpC,KAAK;IACvB,IAAIwB,SAAS,CAACa,aAAa,EAAE;MACzBD,WAAW,GAAGpC,KAAK,CAACwB,SAAS,CAACa,aAAa,CAAC;IAChD,CAAC,MACI;MACDD,WAAW,GAAGpC,KAAK;IACvB;IACAiC,KAAK,CAACK,GAAG,CAACd,SAAS,CAACW,WAAW,EAAE,OAAOC,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGG,IAAI,CAACC,SAAS,CAACJ,WAAW,CAAC,CAAC;EACjH;EACA,IAAIZ,SAAS,CAACiB,YAAY,EAAE;IACxBR,KAAK,CAACK,GAAG,CAAC,QAAQ,EAAEd,SAAS,CAACiB,YAAY,CAAC;EAC/C;EACA,IAAIjB,SAAS,CAACkB,IAAI,EAAE;IAChBT,KAAK,CAACK,GAAG,CAAC,MAAM,EAAEd,SAAS,CAACkB,IAAI,CAAC;EACrC;EACA,IAAIlB,SAAS,CAACmB,MAAM,EAAE;IAClB,MAAMC,kBAAkB,GAAG,IAAIC,eAAe,CAACrB,SAAS,CAACmB,MAAM,CAAC;IAChEC,kBAAkB,CAACE,OAAO,CAAC,CAAC9C,KAAK,EAAE+C,GAAG,KAAKd,KAAK,CAACK,GAAG,CAACS,GAAG,EAAE/C,KAAK,CAAC,CAAC;EACrE;EACA,OAAOgC,GAAG;AACd,CAAC;AACDjB,OAAO,CAACI,WAAW,GAAGA,WAAW;AACjC,MAAMD,cAAc,GAAI8B,OAAO,IAAK;EAChC,IAAIvB,EAAE;EACN,MAAM;IAAED,SAAS;IAAExB,KAAK;IAAEiD;EAAO,CAAC,GAAGD,OAAO;EAC5C;EACA,IAAI,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,CAAC,EAAE;IAClE,OAAO,KAAK;EAChB;EACA,IAAI,CAAC3B,4BAA4B,CAACC,SAAS,CAAC,EAAE;IAC1C,OAAO,KAAK;EAChB;EACA,IAAI,CAACxB,KAAK,IACN,CAAC,CAAC,EAAEsB,MAAM,CAAC6B,aAAa,EAAEnD,KAAK,CAAC,IAC/BoD,KAAK,CAACC,OAAO,CAACrD,KAAK,CAAC,IAAIA,KAAK,CAACsD,MAAM,KAAK,CAAE,EAAE;IAC9C,OAAO,KAAK;EAChB;EACA;EACA,IAAI9B,SAAS,CAACI,OAAO,KAAK,UAAU,IAAI,EAAE,CAACH,EAAE,GAAGD,SAAS,CAAC+B,IAAI,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,QAAQ,CAAC,EAAE;IAC/G,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDzC,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvC,MAAMD,2BAA2B,GAAI+B,OAAO,IAAKtD,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC5F,IAAI+B,EAAE;EACN,MAAM;IAAEzB,KAAK;IAAEiD;EAAO,CAAC,GAAGD,OAAO;EACjC,IAAI,CAAC,CAAC,CAAC,EAAEjC,OAAO,CAACG,cAAc,EAAE8B,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAI,CAACC,MAAM,IAAI,CAACA,MAAM,CAACQ,QAAQ,EAAE;IAC7B,MAAM,IAAIrC,OAAO,CAACsC,cAAc,CAAC,oEAAoE,EAAEV,OAAO,EAAE,sCAAsC,CAAC;EAC3J;EACA,IAAI;IACA,MAAMW,yBAAyB,GAAG,MAAO,CAAClC,EAAE,GAAGwB,MAAM,CAACQ,QAAQ,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACR,2BAA2B,CAAC+B,OAAO,EAAEhD,KAAK,CAAE;IACpJ,OAAO2D,yBAAyB,KAAK,IAAI,GAAG,IAAI,GAAG,IAAIvC,OAAO,CAACwC,UAAU,CAAC,QAAQ,EAAEZ,OAAO,CAAC;EAChG,CAAC,CACD,OAAOa,GAAG,EAAE;IACR,MAAM,IAAIzC,OAAO,CAACsC,cAAc,CAACG,GAAG,CAACC,OAAO,IAAID,GAAG,EAAEb,OAAO,EAAE,sCAAsC,CAAC;EACzG;AACJ,CAAC,CAAC;AACFjC,OAAO,CAACE,2BAA2B,GAAGA,2BAA2B;AACjEF,OAAO,CAACC,+BAA+B,GAAG;EACtC+C,IAAI,EAAE,6BAA6B;EACnCC,OAAO,EAAEjD,OAAO,CAACE,2BAA2B;EAC5CgD,aAAa,EAAElD,OAAO,CAACG;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}