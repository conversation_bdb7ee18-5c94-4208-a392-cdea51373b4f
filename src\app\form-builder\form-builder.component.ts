import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-form-builder',
  templateUrl: './form-builder.component.html',
  styleUrls: ['./form-builder.component.css']
})
export class FormBuilderComponent implements OnInit {
  
  public form: any = {
    components: []
  };

  public options: any = {
    builder: {
      basic: {
        title: 'Basic Components',
        default: true,
        weight: 0,
        components: {
          textfield: true,
          textarea: true,
          number: true,
          password: true,
          checkbox: true,
          selectboxes: true,
          select: true,
          radio: true,
          button: true
        }
      },
      advanced: {
        title: 'Advanced',
        weight: 10,
        components: {
          email: true,
          url: true,
          phoneNumber: true,
          tags: true,
          address: true,
          datetime: true,
          day: true,
          time: true,
          currency: true,
          survey: true,
          signature: true
        }
      },
      layout: {
        title: 'Layout',
        weight: 20,
        components: {
          htmlelement: true,
          content: true,
          columns: true,
          fieldset: true,
          panel: true,
          table: true,
          tabs: true,
          well: true
        }
      }
    }
  };

  constructor() { }

  ngOnInit(): void {
    // 初始化一个示例表单
    this.form = {
      title: 'File an Invoice',
      components: [
        {
          type: 'textfield',
          key: 'creditor',
          label: 'Creditor*',
          placeholder: 'Add your invoice details below.',
          input: true,
          validate: {
            required: true
          }
        },
        {
          type: 'number',
          key: 'invoiceNumber',
          label: 'Invoice Number',
          placeholder: 'An invoice number in the format: C-123',
          input: true
        },
        {
          type: 'button',
          key: 'submit',
          label: 'Submit',
          action: 'submit',
          theme: 'primary'
        }
      ]
    };
  }

  onChange(event: any) {
    console.log('Form changed:', event);
    this.form = event.form;
  }

  onSubmit(submission: any) {
    console.log('Form submitted:', submission);
  }
}