{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.html = void 0;\nconst html = require('./html.ejs.js').default;\nexports.html = html;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "html", "require", "default"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/experimental/components/templates/bootstrap/datatable/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.html = void 0;\nconst html = require('./html.ejs.js').default;\nexports.html = html;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,IAAI,GAAG,KAAK,CAAC;AACrB,MAAMA,IAAI,GAAGC,OAAO,CAAC,eAAe,CAAC,CAACC,OAAO;AAC7CJ,OAAO,CAACE,IAAI,GAAGA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}