{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.clearHiddenProcessInfo = exports.clearHiddenProcess = exports.clearHiddenProcessSync = void 0;\nconst lodash_1 = require(\"lodash\");\n/**\n * This processor function checks components for the `hidden` property and unsets corresponding data\n */\nconst clearHiddenProcessSync = context => {\n  var _a, _b;\n  const {\n    component,\n    data,\n    value,\n    scope,\n    path\n  } = context;\n  // No need to unset the value if it's undefined\n  if (value === undefined) {\n    return;\n  }\n  if (!scope.clearHidden) {\n    scope.clearHidden = {};\n  }\n  // Check if there's a conditional set for the component and if it's marked as conditionally hidden\n  const isConditionallyHidden = (_a = scope.conditionals) === null || _a === void 0 ? void 0 : _a.find(cond => {\n    return path === cond.path && cond.conditionallyHidden;\n  });\n  const shouldClearValueWhenHidden = !component.hasOwnProperty('clearOnHide') || component.clearOnHide;\n  if (shouldClearValueWhenHidden && (isConditionallyHidden || ((_b = component.scope) === null || _b === void 0 ? void 0 : _b.conditionallyHidden))) {\n    (0, lodash_1.unset)(data, path);\n    scope.clearHidden[path] = true;\n  }\n};\nexports.clearHiddenProcessSync = clearHiddenProcessSync;\nconst clearHiddenProcess = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.clearHiddenProcessSync)(context);\n});\nexports.clearHiddenProcess = clearHiddenProcess;\nexports.clearHiddenProcessInfo = {\n  name: 'clearHidden',\n  shouldProcess: () => true,\n  process: exports.clearHiddenProcess,\n  processSync: exports.clearHiddenProcessSync\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "clearHiddenProcessInfo", "clearHiddenProcess", "clearHiddenProcessSync", "lodash_1", "require", "context", "_a", "_b", "component", "data", "scope", "path", "undefined", "clearHidden", "isConditionallyHidden", "conditionals", "find", "cond", "conditionally<PERSON><PERSON><PERSON>", "shouldClearValueWhenHidden", "hasOwnProperty", "clearOnHide", "unset", "name", "shouldProcess", "process", "processSync"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/clearHidden/index.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.clearHiddenProcessInfo = exports.clearHiddenProcess = exports.clearHiddenProcessSync = void 0;\nconst lodash_1 = require(\"lodash\");\n/**\n * This processor function checks components for the `hidden` property and unsets corresponding data\n */\nconst clearHiddenProcessSync = (context) => {\n    var _a, _b;\n    const { component, data, value, scope, path } = context;\n    // No need to unset the value if it's undefined\n    if (value === undefined) {\n        return;\n    }\n    if (!scope.clearHidden) {\n        scope.clearHidden = {};\n    }\n    // Check if there's a conditional set for the component and if it's marked as conditionally hidden\n    const isConditionallyHidden = (_a = scope.conditionals) === null || _a === void 0 ? void 0 : _a.find((cond) => {\n        return path === cond.path && cond.conditionallyHidden;\n    });\n    const shouldClearValueWhenHidden = !component.hasOwnProperty('clearOnHide') || component.clearOnHide;\n    if (shouldClearValueWhenHidden &&\n        (isConditionallyHidden || ((_b = component.scope) === null || _b === void 0 ? void 0 : _b.conditionallyHidden))) {\n        (0, lodash_1.unset)(data, path);\n        scope.clearHidden[path] = true;\n    }\n};\nexports.clearHiddenProcessSync = clearHiddenProcessSync;\nconst clearHiddenProcess = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.clearHiddenProcessSync)(context);\n});\nexports.clearHiddenProcess = clearHiddenProcess;\nexports.clearHiddenProcessInfo = {\n    name: 'clearHidden',\n    shouldProcess: () => true,\n    process: exports.clearHiddenProcess,\n    processSync: exports.clearHiddenProcessSync,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,sBAAsB,GAAGD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,sBAAsB,GAAG,KAAK,CAAC;AACrG,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC;AACA;AACA;AACA,MAAMF,sBAAsB,GAAIG,OAAO,IAAK;EACxC,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;IAAEC,SAAS;IAAEC,IAAI;IAAEzB,KAAK;IAAE0B,KAAK;IAAEC;EAAK,CAAC,GAAGN,OAAO;EACvD;EACA,IAAIrB,KAAK,KAAK4B,SAAS,EAAE;IACrB;EACJ;EACA,IAAI,CAACF,KAAK,CAACG,WAAW,EAAE;IACpBH,KAAK,CAACG,WAAW,GAAG,CAAC,CAAC;EAC1B;EACA;EACA,MAAMC,qBAAqB,GAAG,CAACR,EAAE,GAAGI,KAAK,CAACK,YAAY,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,IAAI,CAAEC,IAAI,IAAK;IAC3G,OAAON,IAAI,KAAKM,IAAI,CAACN,IAAI,IAAIM,IAAI,CAACC,mBAAmB;EACzD,CAAC,CAAC;EACF,MAAMC,0BAA0B,GAAG,CAACX,SAAS,CAACY,cAAc,CAAC,aAAa,CAAC,IAAIZ,SAAS,CAACa,WAAW;EACpG,IAAIF,0BAA0B,KACzBL,qBAAqB,KAAK,CAACP,EAAE,GAAGC,SAAS,CAACE,KAAK,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,mBAAmB,CAAC,CAAC,EAAE;IACjH,CAAC,CAAC,EAAEf,QAAQ,CAACmB,KAAK,EAAEb,IAAI,EAAEE,IAAI,CAAC;IAC/BD,KAAK,CAACG,WAAW,CAACF,IAAI,CAAC,GAAG,IAAI;EAClC;AACJ,CAAC;AACDZ,OAAO,CAACG,sBAAsB,GAAGA,sBAAsB;AACvD,MAAMD,kBAAkB,GAAII,OAAO,IAAK3B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACnF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACG,sBAAsB,EAAEG,OAAO,CAAC;AACvD,CAAC,CAAC;AACFN,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/CF,OAAO,CAACC,sBAAsB,GAAG;EAC7BuB,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAEA,CAAA,KAAM,IAAI;EACzBC,OAAO,EAAE1B,OAAO,CAACE,kBAAkB;EACnCyB,WAAW,EAAE3B,OAAO,CAACG;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}