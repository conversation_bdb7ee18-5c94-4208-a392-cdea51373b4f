{"ast": null, "code": "import { Patch<PERSON>rror, _deepClone, is<PERSON><PERSON><PERSON>, unescapePathComponent, hasUndefined } from './helpers.mjs';\nexport var JsonPatchError = PatchError;\nexport var deepClone = _deepClone;\n/* We use a Javascript hash to store each\n function. Each hash entry (property) uses\n the operation identifiers specified in rfc6902.\n In this way, we can map each patch operation\n to its dedicated function in efficient way.\n */\n/* The operations applicable to an object */\nvar objOps = {\n  add: function (obj, key, document) {\n    obj[key] = this.value;\n    return {\n      newDocument: document\n    };\n  },\n  remove: function (obj, key, document) {\n    var removed = obj[key];\n    delete obj[key];\n    return {\n      newDocument: document,\n      removed: removed\n    };\n  },\n  replace: function (obj, key, document) {\n    var removed = obj[key];\n    obj[key] = this.value;\n    return {\n      newDocument: document,\n      removed: removed\n    };\n  },\n  move: function (obj, key, document) {\n    /* in case move target overwrites an existing value,\n    return the removed value, this can be taxing performance-wise,\n    and is potentially unneeded */\n    var removed = getValueByPointer(document, this.path);\n    if (removed) {\n      removed = _deepClone(removed);\n    }\n    var originalValue = applyOperation(document, {\n      op: \"remove\",\n      path: this.from\n    }).removed;\n    applyOperation(document, {\n      op: \"add\",\n      path: this.path,\n      value: originalValue\n    });\n    return {\n      newDocument: document,\n      removed: removed\n    };\n  },\n  copy: function (obj, key, document) {\n    var valueToCopy = getValueByPointer(document, this.from);\n    // enforce copy by value so further operations don't affect source (see issue #177)\n    applyOperation(document, {\n      op: \"add\",\n      path: this.path,\n      value: _deepClone(valueToCopy)\n    });\n    return {\n      newDocument: document\n    };\n  },\n  test: function (obj, key, document) {\n    return {\n      newDocument: document,\n      test: _areEquals(obj[key], this.value)\n    };\n  },\n  _get: function (obj, key, document) {\n    this.value = obj[key];\n    return {\n      newDocument: document\n    };\n  }\n};\n/* The operations applicable to an array. Many are the same as for the object */\nvar arrOps = {\n  add: function (arr, i, document) {\n    if (isInteger(i)) {\n      arr.splice(i, 0, this.value);\n    } else {\n      // array props\n      arr[i] = this.value;\n    }\n    // this may be needed when using '-' in an array\n    return {\n      newDocument: document,\n      index: i\n    };\n  },\n  remove: function (arr, i, document) {\n    var removedList = arr.splice(i, 1);\n    return {\n      newDocument: document,\n      removed: removedList[0]\n    };\n  },\n  replace: function (arr, i, document) {\n    var removed = arr[i];\n    arr[i] = this.value;\n    return {\n      newDocument: document,\n      removed: removed\n    };\n  },\n  move: objOps.move,\n  copy: objOps.copy,\n  test: objOps.test,\n  _get: objOps._get\n};\n/**\n * Retrieves a value from a JSON document by a JSON pointer.\n * Returns the value.\n *\n * @param document The document to get the value from\n * @param pointer an escaped JSON pointer\n * @return The retrieved value\n */\nexport function getValueByPointer(document, pointer) {\n  if (pointer == '') {\n    return document;\n  }\n  var getOriginalDestination = {\n    op: \"_get\",\n    path: pointer\n  };\n  applyOperation(document, getOriginalDestination);\n  return getOriginalDestination.value;\n}\n/**\n * Apply a single JSON Patch Operation on a JSON document.\n * Returns the {newDocument, result} of the operation.\n * It modifies the `document` and `operation` objects - it gets the values by reference.\n * If you would like to avoid touching your values, clone them:\n * `jsonpatch.applyOperation(document, jsonpatch._deepClone(operation))`.\n *\n * @param document The document to patch\n * @param operation The operation to apply\n * @param validateOperation `false` is without validation, `true` to use default jsonpatch's validation, or you can pass a `validateOperation` callback to be used for validation.\n * @param mutateDocument Whether to mutate the original document or clone it before applying\n * @param banPrototypeModifications Whether to ban modifications to `__proto__`, defaults to `true`.\n * @return `{newDocument, result}` after the operation\n */\nexport function applyOperation(document, operation, validateOperation, mutateDocument, banPrototypeModifications, index) {\n  if (validateOperation === void 0) {\n    validateOperation = false;\n  }\n  if (mutateDocument === void 0) {\n    mutateDocument = true;\n  }\n  if (banPrototypeModifications === void 0) {\n    banPrototypeModifications = true;\n  }\n  if (index === void 0) {\n    index = 0;\n  }\n  if (validateOperation) {\n    if (typeof validateOperation == 'function') {\n      validateOperation(operation, 0, document, operation.path);\n    } else {\n      validator(operation, 0);\n    }\n  }\n  /* ROOT OPERATIONS */\n  if (operation.path === \"\") {\n    var returnValue = {\n      newDocument: document\n    };\n    if (operation.op === 'add') {\n      returnValue.newDocument = operation.value;\n      return returnValue;\n    } else if (operation.op === 'replace') {\n      returnValue.newDocument = operation.value;\n      returnValue.removed = document; //document we removed\n      return returnValue;\n    } else if (operation.op === 'move' || operation.op === 'copy') {\n      // it's a move or copy to root\n      returnValue.newDocument = getValueByPointer(document, operation.from); // get the value by json-pointer in `from` field\n      if (operation.op === 'move') {\n        // report removed item\n        returnValue.removed = document;\n      }\n      return returnValue;\n    } else if (operation.op === 'test') {\n      returnValue.test = _areEquals(document, operation.value);\n      if (returnValue.test === false) {\n        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n      }\n      returnValue.newDocument = document;\n      return returnValue;\n    } else if (operation.op === 'remove') {\n      // a remove on root\n      returnValue.removed = document;\n      returnValue.newDocument = null;\n      return returnValue;\n    } else if (operation.op === '_get') {\n      operation.value = document;\n      return returnValue;\n    } else {\n      /* bad operation */\n      if (validateOperation) {\n        throw new JsonPatchError('Operation `op` property is not one of operations defined in RFC-6902', 'OPERATION_OP_INVALID', index, operation, document);\n      } else {\n        return returnValue;\n      }\n    }\n  } /* END ROOT OPERATIONS */else {\n    if (!mutateDocument) {\n      document = _deepClone(document);\n    }\n    var path = operation.path || \"\";\n    var keys = path.split('/');\n    var obj = document;\n    var t = 1; //skip empty element - http://jsperf.com/to-shift-or-not-to-shift\n    var len = keys.length;\n    var existingPathFragment = undefined;\n    var key = void 0;\n    var validateFunction = void 0;\n    if (typeof validateOperation == 'function') {\n      validateFunction = validateOperation;\n    } else {\n      validateFunction = validator;\n    }\n    while (true) {\n      key = keys[t];\n      if (key && key.indexOf('~') != -1) {\n        key = unescapePathComponent(key);\n      }\n      if (banPrototypeModifications && (key == '__proto__' || key == 'prototype' && t > 0 && keys[t - 1] == 'constructor')) {\n        throw new TypeError('JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README');\n      }\n      if (validateOperation) {\n        if (existingPathFragment === undefined) {\n          if (obj[key] === undefined) {\n            existingPathFragment = keys.slice(0, t).join('/');\n          } else if (t == len - 1) {\n            existingPathFragment = operation.path;\n          }\n          if (existingPathFragment !== undefined) {\n            validateFunction(operation, 0, document, existingPathFragment);\n          }\n        }\n      }\n      t++;\n      if (Array.isArray(obj)) {\n        if (key === '-') {\n          key = obj.length;\n        } else {\n          if (validateOperation && !isInteger(key)) {\n            throw new JsonPatchError(\"Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index\", \"OPERATION_PATH_ILLEGAL_ARRAY_INDEX\", index, operation, document);\n          } // only parse key when it's an integer for `arr.prop` to work\n          else if (isInteger(key)) {\n            key = ~~key;\n          }\n        }\n        if (t >= len) {\n          if (validateOperation && operation.op === \"add\" && key > obj.length) {\n            throw new JsonPatchError(\"The specified index MUST NOT be greater than the number of elements in the array\", \"OPERATION_VALUE_OUT_OF_BOUNDS\", index, operation, document);\n          }\n          var returnValue = arrOps[operation.op].call(operation, obj, key, document); // Apply patch\n          if (returnValue.test === false) {\n            throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n          }\n          return returnValue;\n        }\n      } else {\n        if (t >= len) {\n          var returnValue = objOps[operation.op].call(operation, obj, key, document); // Apply patch\n          if (returnValue.test === false) {\n            throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n          }\n          return returnValue;\n        }\n      }\n      obj = obj[key];\n      // If we have more keys in the path, but the next value isn't a non-null object,\n      // throw an OPERATION_PATH_UNRESOLVABLE error instead of iterating again.\n      if (validateOperation && t < len && (!obj || typeof obj !== \"object\")) {\n        throw new JsonPatchError('Cannot perform operation at the desired path', 'OPERATION_PATH_UNRESOLVABLE', index, operation, document);\n      }\n    }\n  }\n}\n/**\n * Apply a full JSON Patch array on a JSON document.\n * Returns the {newDocument, result} of the patch.\n * It modifies the `document` object and `patch` - it gets the values by reference.\n * If you would like to avoid touching your values, clone them:\n * `jsonpatch.applyPatch(document, jsonpatch._deepClone(patch))`.\n *\n * @param document The document to patch\n * @param patch The patch to apply\n * @param validateOperation `false` is without validation, `true` to use default jsonpatch's validation, or you can pass a `validateOperation` callback to be used for validation.\n * @param mutateDocument Whether to mutate the original document or clone it before applying\n * @param banPrototypeModifications Whether to ban modifications to `__proto__`, defaults to `true`.\n * @return An array of `{newDocument, result}` after the patch\n */\nexport function applyPatch(document, patch, validateOperation, mutateDocument, banPrototypeModifications) {\n  if (mutateDocument === void 0) {\n    mutateDocument = true;\n  }\n  if (banPrototypeModifications === void 0) {\n    banPrototypeModifications = true;\n  }\n  if (validateOperation) {\n    if (!Array.isArray(patch)) {\n      throw new JsonPatchError('Patch sequence must be an array', 'SEQUENCE_NOT_AN_ARRAY');\n    }\n  }\n  if (!mutateDocument) {\n    document = _deepClone(document);\n  }\n  var results = new Array(patch.length);\n  for (var i = 0, length_1 = patch.length; i < length_1; i++) {\n    // we don't need to pass mutateDocument argument because if it was true, we already deep cloned the object, we'll just pass `true`\n    results[i] = applyOperation(document, patch[i], validateOperation, true, banPrototypeModifications, i);\n    document = results[i].newDocument; // in case root was replaced\n  }\n  results.newDocument = document;\n  return results;\n}\n/**\n * Apply a single JSON Patch Operation on a JSON document.\n * Returns the updated document.\n * Suitable as a reducer.\n *\n * @param document The document to patch\n * @param operation The operation to apply\n * @return The updated document\n */\nexport function applyReducer(document, operation, index) {\n  var operationResult = applyOperation(document, operation);\n  if (operationResult.test === false) {\n    // failed test\n    throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n  }\n  return operationResult.newDocument;\n}\n/**\n * Validates a single operation. Called from `jsonpatch.validate`. Throws `JsonPatchError` in case of an error.\n * @param {object} operation - operation object (patch)\n * @param {number} index - index of operation in the sequence\n * @param {object} [document] - object where the operation is supposed to be applied\n * @param {string} [existingPathFragment] - comes along with `document`\n */\nexport function validator(operation, index, document, existingPathFragment) {\n  if (typeof operation !== 'object' || operation === null || Array.isArray(operation)) {\n    throw new JsonPatchError('Operation is not an object', 'OPERATION_NOT_AN_OBJECT', index, operation, document);\n  } else if (!objOps[operation.op]) {\n    throw new JsonPatchError('Operation `op` property is not one of operations defined in RFC-6902', 'OPERATION_OP_INVALID', index, operation, document);\n  } else if (typeof operation.path !== 'string') {\n    throw new JsonPatchError('Operation `path` property is not a string', 'OPERATION_PATH_INVALID', index, operation, document);\n  } else if (operation.path.indexOf('/') !== 0 && operation.path.length > 0) {\n    // paths that aren't empty string should start with \"/\"\n    throw new JsonPatchError('Operation `path` property must start with \"/\"', 'OPERATION_PATH_INVALID', index, operation, document);\n  } else if ((operation.op === 'move' || operation.op === 'copy') && typeof operation.from !== 'string') {\n    throw new JsonPatchError('Operation `from` property is not present (applicable in `move` and `copy` operations)', 'OPERATION_FROM_REQUIRED', index, operation, document);\n  } else if ((operation.op === 'add' || operation.op === 'replace' || operation.op === 'test') && operation.value === undefined) {\n    throw new JsonPatchError('Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)', 'OPERATION_VALUE_REQUIRED', index, operation, document);\n  } else if ((operation.op === 'add' || operation.op === 'replace' || operation.op === 'test') && hasUndefined(operation.value)) {\n    throw new JsonPatchError('Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)', 'OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED', index, operation, document);\n  } else if (document) {\n    if (operation.op == \"add\") {\n      var pathLen = operation.path.split(\"/\").length;\n      var existingPathLen = existingPathFragment.split(\"/\").length;\n      if (pathLen !== existingPathLen + 1 && pathLen !== existingPathLen) {\n        throw new JsonPatchError('Cannot perform an `add` operation at the desired path', 'OPERATION_PATH_CANNOT_ADD', index, operation, document);\n      }\n    } else if (operation.op === 'replace' || operation.op === 'remove' || operation.op === '_get') {\n      if (operation.path !== existingPathFragment) {\n        throw new JsonPatchError('Cannot perform the operation at a path that does not exist', 'OPERATION_PATH_UNRESOLVABLE', index, operation, document);\n      }\n    } else if (operation.op === 'move' || operation.op === 'copy') {\n      var existingValue = {\n        op: \"_get\",\n        path: operation.from,\n        value: undefined\n      };\n      var error = validate([existingValue], document);\n      if (error && error.name === 'OPERATION_PATH_UNRESOLVABLE') {\n        throw new JsonPatchError('Cannot perform the operation from a path that does not exist', 'OPERATION_FROM_UNRESOLVABLE', index, operation, document);\n      }\n    }\n  }\n}\n/**\n * Validates a sequence of operations. If `document` parameter is provided, the sequence is additionally validated against the object document.\n * If error is encountered, returns a JsonPatchError object\n * @param sequence\n * @param document\n * @returns {JsonPatchError|undefined}\n */\nexport function validate(sequence, document, externalValidator) {\n  try {\n    if (!Array.isArray(sequence)) {\n      throw new JsonPatchError('Patch sequence must be an array', 'SEQUENCE_NOT_AN_ARRAY');\n    }\n    if (document) {\n      //clone document and sequence so that we can safely try applying operations\n      applyPatch(_deepClone(document), _deepClone(sequence), externalValidator || true);\n    } else {\n      externalValidator = externalValidator || validator;\n      for (var i = 0; i < sequence.length; i++) {\n        externalValidator(sequence[i], i, document, undefined);\n      }\n    }\n  } catch (e) {\n    if (e instanceof JsonPatchError) {\n      return e;\n    } else {\n      throw e;\n    }\n  }\n}\n// based on https://github.com/epoberezkin/fast-deep-equal\n// MIT License\n// Copyright (c) 2017 Evgeny Poberezkin\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nexport function _areEquals(a, b) {\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    var arrA = Array.isArray(a),\n      arrB = Array.isArray(b),\n      i,\n      length,\n      key;\n    if (arrA && arrB) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!_areEquals(a[i], b[i])) return false;\n      return true;\n    }\n    if (arrA != arrB) return false;\n    var keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n    for (i = length; i-- !== 0;) if (!b.hasOwnProperty(keys[i])) return false;\n    for (i = length; i-- !== 0;) {\n      key = keys[i];\n      if (!_areEquals(a[key], b[key])) return false;\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n;", "map": {"version": 3, "names": ["Patch<PERSON><PERSON>r", "_deepClone", "isInteger", "unescapePathComponent", "hasUndefined", "JsonPatchError", "deepClone", "objOps", "add", "obj", "key", "document", "value", "newDocument", "remove", "removed", "replace", "move", "getV<PERSON>ueByPointer", "path", "originalValue", "applyOperation", "op", "from", "copy", "valueToCopy", "test", "_areEquals", "_get", "arrOps", "arr", "i", "splice", "index", "removedList", "pointer", "getOriginalDestination", "operation", "validateOperation", "mutateDocument", "banPrototypeModifications", "validator", "returnValue", "keys", "split", "t", "len", "length", "existingPathFragment", "undefined", "validateFunction", "indexOf", "TypeError", "slice", "join", "Array", "isArray", "call", "applyPatch", "patch", "results", "length_1", "applyReducer", "operationResult", "pathLen", "existingPathLen", "existingValue", "error", "validate", "name", "sequence", "externalValidator", "e", "a", "b", "arrA", "arrB", "Object", "hasOwnProperty"], "sources": ["D:/workspace/formtest_aug/node_modules/fast-json-patch/module/core.mjs"], "sourcesContent": ["import { Patch<PERSON>rror, _deepClone, is<PERSON><PERSON><PERSON>, unescapePathComponent, hasUndefined } from './helpers.mjs';\nexport var JsonPatchError = PatchError;\nexport var deepClone = _deepClone;\n/* We use a Javascript hash to store each\n function. Each hash entry (property) uses\n the operation identifiers specified in rfc6902.\n In this way, we can map each patch operation\n to its dedicated function in efficient way.\n */\n/* The operations applicable to an object */\nvar objOps = {\n    add: function (obj, key, document) {\n        obj[key] = this.value;\n        return { newDocument: document };\n    },\n    remove: function (obj, key, document) {\n        var removed = obj[key];\n        delete obj[key];\n        return { newDocument: document, removed: removed };\n    },\n    replace: function (obj, key, document) {\n        var removed = obj[key];\n        obj[key] = this.value;\n        return { newDocument: document, removed: removed };\n    },\n    move: function (obj, key, document) {\n        /* in case move target overwrites an existing value,\n        return the removed value, this can be taxing performance-wise,\n        and is potentially unneeded */\n        var removed = getValueByPointer(document, this.path);\n        if (removed) {\n            removed = _deepClone(removed);\n        }\n        var originalValue = applyOperation(document, { op: \"remove\", path: this.from }).removed;\n        applyOperation(document, { op: \"add\", path: this.path, value: originalValue });\n        return { newDocument: document, removed: removed };\n    },\n    copy: function (obj, key, document) {\n        var valueToCopy = getValueByPointer(document, this.from);\n        // enforce copy by value so further operations don't affect source (see issue #177)\n        applyOperation(document, { op: \"add\", path: this.path, value: _deepClone(valueToCopy) });\n        return { newDocument: document };\n    },\n    test: function (obj, key, document) {\n        return { newDocument: document, test: _areEquals(obj[key], this.value) };\n    },\n    _get: function (obj, key, document) {\n        this.value = obj[key];\n        return { newDocument: document };\n    }\n};\n/* The operations applicable to an array. Many are the same as for the object */\nvar arrOps = {\n    add: function (arr, i, document) {\n        if (isInteger(i)) {\n            arr.splice(i, 0, this.value);\n        }\n        else { // array props\n            arr[i] = this.value;\n        }\n        // this may be needed when using '-' in an array\n        return { newDocument: document, index: i };\n    },\n    remove: function (arr, i, document) {\n        var removedList = arr.splice(i, 1);\n        return { newDocument: document, removed: removedList[0] };\n    },\n    replace: function (arr, i, document) {\n        var removed = arr[i];\n        arr[i] = this.value;\n        return { newDocument: document, removed: removed };\n    },\n    move: objOps.move,\n    copy: objOps.copy,\n    test: objOps.test,\n    _get: objOps._get\n};\n/**\n * Retrieves a value from a JSON document by a JSON pointer.\n * Returns the value.\n *\n * @param document The document to get the value from\n * @param pointer an escaped JSON pointer\n * @return The retrieved value\n */\nexport function getValueByPointer(document, pointer) {\n    if (pointer == '') {\n        return document;\n    }\n    var getOriginalDestination = { op: \"_get\", path: pointer };\n    applyOperation(document, getOriginalDestination);\n    return getOriginalDestination.value;\n}\n/**\n * Apply a single JSON Patch Operation on a JSON document.\n * Returns the {newDocument, result} of the operation.\n * It modifies the `document` and `operation` objects - it gets the values by reference.\n * If you would like to avoid touching your values, clone them:\n * `jsonpatch.applyOperation(document, jsonpatch._deepClone(operation))`.\n *\n * @param document The document to patch\n * @param operation The operation to apply\n * @param validateOperation `false` is without validation, `true` to use default jsonpatch's validation, or you can pass a `validateOperation` callback to be used for validation.\n * @param mutateDocument Whether to mutate the original document or clone it before applying\n * @param banPrototypeModifications Whether to ban modifications to `__proto__`, defaults to `true`.\n * @return `{newDocument, result}` after the operation\n */\nexport function applyOperation(document, operation, validateOperation, mutateDocument, banPrototypeModifications, index) {\n    if (validateOperation === void 0) { validateOperation = false; }\n    if (mutateDocument === void 0) { mutateDocument = true; }\n    if (banPrototypeModifications === void 0) { banPrototypeModifications = true; }\n    if (index === void 0) { index = 0; }\n    if (validateOperation) {\n        if (typeof validateOperation == 'function') {\n            validateOperation(operation, 0, document, operation.path);\n        }\n        else {\n            validator(operation, 0);\n        }\n    }\n    /* ROOT OPERATIONS */\n    if (operation.path === \"\") {\n        var returnValue = { newDocument: document };\n        if (operation.op === 'add') {\n            returnValue.newDocument = operation.value;\n            return returnValue;\n        }\n        else if (operation.op === 'replace') {\n            returnValue.newDocument = operation.value;\n            returnValue.removed = document; //document we removed\n            return returnValue;\n        }\n        else if (operation.op === 'move' || operation.op === 'copy') { // it's a move or copy to root\n            returnValue.newDocument = getValueByPointer(document, operation.from); // get the value by json-pointer in `from` field\n            if (operation.op === 'move') { // report removed item\n                returnValue.removed = document;\n            }\n            return returnValue;\n        }\n        else if (operation.op === 'test') {\n            returnValue.test = _areEquals(document, operation.value);\n            if (returnValue.test === false) {\n                throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n            }\n            returnValue.newDocument = document;\n            return returnValue;\n        }\n        else if (operation.op === 'remove') { // a remove on root\n            returnValue.removed = document;\n            returnValue.newDocument = null;\n            return returnValue;\n        }\n        else if (operation.op === '_get') {\n            operation.value = document;\n            return returnValue;\n        }\n        else { /* bad operation */\n            if (validateOperation) {\n                throw new JsonPatchError('Operation `op` property is not one of operations defined in RFC-6902', 'OPERATION_OP_INVALID', index, operation, document);\n            }\n            else {\n                return returnValue;\n            }\n        }\n    } /* END ROOT OPERATIONS */\n    else {\n        if (!mutateDocument) {\n            document = _deepClone(document);\n        }\n        var path = operation.path || \"\";\n        var keys = path.split('/');\n        var obj = document;\n        var t = 1; //skip empty element - http://jsperf.com/to-shift-or-not-to-shift\n        var len = keys.length;\n        var existingPathFragment = undefined;\n        var key = void 0;\n        var validateFunction = void 0;\n        if (typeof validateOperation == 'function') {\n            validateFunction = validateOperation;\n        }\n        else {\n            validateFunction = validator;\n        }\n        while (true) {\n            key = keys[t];\n            if (key && key.indexOf('~') != -1) {\n                key = unescapePathComponent(key);\n            }\n            if (banPrototypeModifications &&\n                (key == '__proto__' ||\n                    (key == 'prototype' && t > 0 && keys[t - 1] == 'constructor'))) {\n                throw new TypeError('JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README');\n            }\n            if (validateOperation) {\n                if (existingPathFragment === undefined) {\n                    if (obj[key] === undefined) {\n                        existingPathFragment = keys.slice(0, t).join('/');\n                    }\n                    else if (t == len - 1) {\n                        existingPathFragment = operation.path;\n                    }\n                    if (existingPathFragment !== undefined) {\n                        validateFunction(operation, 0, document, existingPathFragment);\n                    }\n                }\n            }\n            t++;\n            if (Array.isArray(obj)) {\n                if (key === '-') {\n                    key = obj.length;\n                }\n                else {\n                    if (validateOperation && !isInteger(key)) {\n                        throw new JsonPatchError(\"Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index\", \"OPERATION_PATH_ILLEGAL_ARRAY_INDEX\", index, operation, document);\n                    } // only parse key when it's an integer for `arr.prop` to work\n                    else if (isInteger(key)) {\n                        key = ~~key;\n                    }\n                }\n                if (t >= len) {\n                    if (validateOperation && operation.op === \"add\" && key > obj.length) {\n                        throw new JsonPatchError(\"The specified index MUST NOT be greater than the number of elements in the array\", \"OPERATION_VALUE_OUT_OF_BOUNDS\", index, operation, document);\n                    }\n                    var returnValue = arrOps[operation.op].call(operation, obj, key, document); // Apply patch\n                    if (returnValue.test === false) {\n                        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n                    }\n                    return returnValue;\n                }\n            }\n            else {\n                if (t >= len) {\n                    var returnValue = objOps[operation.op].call(operation, obj, key, document); // Apply patch\n                    if (returnValue.test === false) {\n                        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n                    }\n                    return returnValue;\n                }\n            }\n            obj = obj[key];\n            // If we have more keys in the path, but the next value isn't a non-null object,\n            // throw an OPERATION_PATH_UNRESOLVABLE error instead of iterating again.\n            if (validateOperation && t < len && (!obj || typeof obj !== \"object\")) {\n                throw new JsonPatchError('Cannot perform operation at the desired path', 'OPERATION_PATH_UNRESOLVABLE', index, operation, document);\n            }\n        }\n    }\n}\n/**\n * Apply a full JSON Patch array on a JSON document.\n * Returns the {newDocument, result} of the patch.\n * It modifies the `document` object and `patch` - it gets the values by reference.\n * If you would like to avoid touching your values, clone them:\n * `jsonpatch.applyPatch(document, jsonpatch._deepClone(patch))`.\n *\n * @param document The document to patch\n * @param patch The patch to apply\n * @param validateOperation `false` is without validation, `true` to use default jsonpatch's validation, or you can pass a `validateOperation` callback to be used for validation.\n * @param mutateDocument Whether to mutate the original document or clone it before applying\n * @param banPrototypeModifications Whether to ban modifications to `__proto__`, defaults to `true`.\n * @return An array of `{newDocument, result}` after the patch\n */\nexport function applyPatch(document, patch, validateOperation, mutateDocument, banPrototypeModifications) {\n    if (mutateDocument === void 0) { mutateDocument = true; }\n    if (banPrototypeModifications === void 0) { banPrototypeModifications = true; }\n    if (validateOperation) {\n        if (!Array.isArray(patch)) {\n            throw new JsonPatchError('Patch sequence must be an array', 'SEQUENCE_NOT_AN_ARRAY');\n        }\n    }\n    if (!mutateDocument) {\n        document = _deepClone(document);\n    }\n    var results = new Array(patch.length);\n    for (var i = 0, length_1 = patch.length; i < length_1; i++) {\n        // we don't need to pass mutateDocument argument because if it was true, we already deep cloned the object, we'll just pass `true`\n        results[i] = applyOperation(document, patch[i], validateOperation, true, banPrototypeModifications, i);\n        document = results[i].newDocument; // in case root was replaced\n    }\n    results.newDocument = document;\n    return results;\n}\n/**\n * Apply a single JSON Patch Operation on a JSON document.\n * Returns the updated document.\n * Suitable as a reducer.\n *\n * @param document The document to patch\n * @param operation The operation to apply\n * @return The updated document\n */\nexport function applyReducer(document, operation, index) {\n    var operationResult = applyOperation(document, operation);\n    if (operationResult.test === false) { // failed test\n        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n    }\n    return operationResult.newDocument;\n}\n/**\n * Validates a single operation. Called from `jsonpatch.validate`. Throws `JsonPatchError` in case of an error.\n * @param {object} operation - operation object (patch)\n * @param {number} index - index of operation in the sequence\n * @param {object} [document] - object where the operation is supposed to be applied\n * @param {string} [existingPathFragment] - comes along with `document`\n */\nexport function validator(operation, index, document, existingPathFragment) {\n    if (typeof operation !== 'object' || operation === null || Array.isArray(operation)) {\n        throw new JsonPatchError('Operation is not an object', 'OPERATION_NOT_AN_OBJECT', index, operation, document);\n    }\n    else if (!objOps[operation.op]) {\n        throw new JsonPatchError('Operation `op` property is not one of operations defined in RFC-6902', 'OPERATION_OP_INVALID', index, operation, document);\n    }\n    else if (typeof operation.path !== 'string') {\n        throw new JsonPatchError('Operation `path` property is not a string', 'OPERATION_PATH_INVALID', index, operation, document);\n    }\n    else if (operation.path.indexOf('/') !== 0 && operation.path.length > 0) {\n        // paths that aren't empty string should start with \"/\"\n        throw new JsonPatchError('Operation `path` property must start with \"/\"', 'OPERATION_PATH_INVALID', index, operation, document);\n    }\n    else if ((operation.op === 'move' || operation.op === 'copy') && typeof operation.from !== 'string') {\n        throw new JsonPatchError('Operation `from` property is not present (applicable in `move` and `copy` operations)', 'OPERATION_FROM_REQUIRED', index, operation, document);\n    }\n    else if ((operation.op === 'add' || operation.op === 'replace' || operation.op === 'test') && operation.value === undefined) {\n        throw new JsonPatchError('Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)', 'OPERATION_VALUE_REQUIRED', index, operation, document);\n    }\n    else if ((operation.op === 'add' || operation.op === 'replace' || operation.op === 'test') && hasUndefined(operation.value)) {\n        throw new JsonPatchError('Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)', 'OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED', index, operation, document);\n    }\n    else if (document) {\n        if (operation.op == \"add\") {\n            var pathLen = operation.path.split(\"/\").length;\n            var existingPathLen = existingPathFragment.split(\"/\").length;\n            if (pathLen !== existingPathLen + 1 && pathLen !== existingPathLen) {\n                throw new JsonPatchError('Cannot perform an `add` operation at the desired path', 'OPERATION_PATH_CANNOT_ADD', index, operation, document);\n            }\n        }\n        else if (operation.op === 'replace' || operation.op === 'remove' || operation.op === '_get') {\n            if (operation.path !== existingPathFragment) {\n                throw new JsonPatchError('Cannot perform the operation at a path that does not exist', 'OPERATION_PATH_UNRESOLVABLE', index, operation, document);\n            }\n        }\n        else if (operation.op === 'move' || operation.op === 'copy') {\n            var existingValue = { op: \"_get\", path: operation.from, value: undefined };\n            var error = validate([existingValue], document);\n            if (error && error.name === 'OPERATION_PATH_UNRESOLVABLE') {\n                throw new JsonPatchError('Cannot perform the operation from a path that does not exist', 'OPERATION_FROM_UNRESOLVABLE', index, operation, document);\n            }\n        }\n    }\n}\n/**\n * Validates a sequence of operations. If `document` parameter is provided, the sequence is additionally validated against the object document.\n * If error is encountered, returns a JsonPatchError object\n * @param sequence\n * @param document\n * @returns {JsonPatchError|undefined}\n */\nexport function validate(sequence, document, externalValidator) {\n    try {\n        if (!Array.isArray(sequence)) {\n            throw new JsonPatchError('Patch sequence must be an array', 'SEQUENCE_NOT_AN_ARRAY');\n        }\n        if (document) {\n            //clone document and sequence so that we can safely try applying operations\n            applyPatch(_deepClone(document), _deepClone(sequence), externalValidator || true);\n        }\n        else {\n            externalValidator = externalValidator || validator;\n            for (var i = 0; i < sequence.length; i++) {\n                externalValidator(sequence[i], i, document, undefined);\n            }\n        }\n    }\n    catch (e) {\n        if (e instanceof JsonPatchError) {\n            return e;\n        }\n        else {\n            throw e;\n        }\n    }\n}\n// based on https://github.com/epoberezkin/fast-deep-equal\n// MIT License\n// Copyright (c) 2017 Evgeny Poberezkin\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nexport function _areEquals(a, b) {\n    if (a === b)\n        return true;\n    if (a && b && typeof a == 'object' && typeof b == 'object') {\n        var arrA = Array.isArray(a), arrB = Array.isArray(b), i, length, key;\n        if (arrA && arrB) {\n            length = a.length;\n            if (length != b.length)\n                return false;\n            for (i = length; i-- !== 0;)\n                if (!_areEquals(a[i], b[i]))\n                    return false;\n            return true;\n        }\n        if (arrA != arrB)\n            return false;\n        var keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length)\n            return false;\n        for (i = length; i-- !== 0;)\n            if (!b.hasOwnProperty(keys[i]))\n                return false;\n        for (i = length; i-- !== 0;) {\n            key = keys[i];\n            if (!_areEquals(a[key], b[key]))\n                return false;\n        }\n        return true;\n    }\n    return a !== a && b !== b;\n}\n;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,eAAe;AACtG,OAAO,IAAIC,cAAc,GAAGL,UAAU;AACtC,OAAO,IAAIM,SAAS,GAAGL,UAAU;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIM,MAAM,GAAG;EACTC,GAAG,EAAE,SAAAA,CAAUC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC/BF,GAAG,CAACC,GAAG,CAAC,GAAG,IAAI,CAACE,KAAK;IACrB,OAAO;MAAEC,WAAW,EAAEF;IAAS,CAAC;EACpC,CAAC;EACDG,MAAM,EAAE,SAAAA,CAAUL,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAClC,IAAII,OAAO,GAAGN,GAAG,CAACC,GAAG,CAAC;IACtB,OAAOD,GAAG,CAACC,GAAG,CAAC;IACf,OAAO;MAAEG,WAAW,EAAEF,QAAQ;MAAEI,OAAO,EAAEA;IAAQ,CAAC;EACtD,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAUP,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IACnC,IAAII,OAAO,GAAGN,GAAG,CAACC,GAAG,CAAC;IACtBD,GAAG,CAACC,GAAG,CAAC,GAAG,IAAI,CAACE,KAAK;IACrB,OAAO;MAAEC,WAAW,EAAEF,QAAQ;MAAEI,OAAO,EAAEA;IAAQ,CAAC;EACtD,CAAC;EACDE,IAAI,EAAE,SAAAA,CAAUR,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAChC;AACR;AACA;IACQ,IAAII,OAAO,GAAGG,iBAAiB,CAACP,QAAQ,EAAE,IAAI,CAACQ,IAAI,CAAC;IACpD,IAAIJ,OAAO,EAAE;MACTA,OAAO,GAAGd,UAAU,CAACc,OAAO,CAAC;IACjC;IACA,IAAIK,aAAa,GAAGC,cAAc,CAACV,QAAQ,EAAE;MAAEW,EAAE,EAAE,QAAQ;MAAEH,IAAI,EAAE,IAAI,CAACI;IAAK,CAAC,CAAC,CAACR,OAAO;IACvFM,cAAc,CAACV,QAAQ,EAAE;MAAEW,EAAE,EAAE,KAAK;MAAEH,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEP,KAAK,EAAEQ;IAAc,CAAC,CAAC;IAC9E,OAAO;MAAEP,WAAW,EAAEF,QAAQ;MAAEI,OAAO,EAAEA;IAAQ,CAAC;EACtD,CAAC;EACDS,IAAI,EAAE,SAAAA,CAAUf,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAChC,IAAIc,WAAW,GAAGP,iBAAiB,CAACP,QAAQ,EAAE,IAAI,CAACY,IAAI,CAAC;IACxD;IACAF,cAAc,CAACV,QAAQ,EAAE;MAAEW,EAAE,EAAE,KAAK;MAAEH,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEP,KAAK,EAAEX,UAAU,CAACwB,WAAW;IAAE,CAAC,CAAC;IACxF,OAAO;MAAEZ,WAAW,EAAEF;IAAS,CAAC;EACpC,CAAC;EACDe,IAAI,EAAE,SAAAA,CAAUjB,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAChC,OAAO;MAAEE,WAAW,EAAEF,QAAQ;MAAEe,IAAI,EAAEC,UAAU,CAAClB,GAAG,CAACC,GAAG,CAAC,EAAE,IAAI,CAACE,KAAK;IAAE,CAAC;EAC5E,CAAC;EACDgB,IAAI,EAAE,SAAAA,CAAUnB,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAChC,IAAI,CAACC,KAAK,GAAGH,GAAG,CAACC,GAAG,CAAC;IACrB,OAAO;MAAEG,WAAW,EAAEF;IAAS,CAAC;EACpC;AACJ,CAAC;AACD;AACA,IAAIkB,MAAM,GAAG;EACTrB,GAAG,EAAE,SAAAA,CAAUsB,GAAG,EAAEC,CAAC,EAAEpB,QAAQ,EAAE;IAC7B,IAAIT,SAAS,CAAC6B,CAAC,CAAC,EAAE;MACdD,GAAG,CAACE,MAAM,CAACD,CAAC,EAAE,CAAC,EAAE,IAAI,CAACnB,KAAK,CAAC;IAChC,CAAC,MACI;MAAE;MACHkB,GAAG,CAACC,CAAC,CAAC,GAAG,IAAI,CAACnB,KAAK;IACvB;IACA;IACA,OAAO;MAAEC,WAAW,EAAEF,QAAQ;MAAEsB,KAAK,EAAEF;IAAE,CAAC;EAC9C,CAAC;EACDjB,MAAM,EAAE,SAAAA,CAAUgB,GAAG,EAAEC,CAAC,EAAEpB,QAAQ,EAAE;IAChC,IAAIuB,WAAW,GAAGJ,GAAG,CAACE,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;IAClC,OAAO;MAAElB,WAAW,EAAEF,QAAQ;MAAEI,OAAO,EAAEmB,WAAW,CAAC,CAAC;IAAE,CAAC;EAC7D,CAAC;EACDlB,OAAO,EAAE,SAAAA,CAAUc,GAAG,EAAEC,CAAC,EAAEpB,QAAQ,EAAE;IACjC,IAAII,OAAO,GAAGe,GAAG,CAACC,CAAC,CAAC;IACpBD,GAAG,CAACC,CAAC,CAAC,GAAG,IAAI,CAACnB,KAAK;IACnB,OAAO;MAAEC,WAAW,EAAEF,QAAQ;MAAEI,OAAO,EAAEA;IAAQ,CAAC;EACtD,CAAC;EACDE,IAAI,EAAEV,MAAM,CAACU,IAAI;EACjBO,IAAI,EAAEjB,MAAM,CAACiB,IAAI;EACjBE,IAAI,EAAEnB,MAAM,CAACmB,IAAI;EACjBE,IAAI,EAAErB,MAAM,CAACqB;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASV,iBAAiBA,CAACP,QAAQ,EAAEwB,OAAO,EAAE;EACjD,IAAIA,OAAO,IAAI,EAAE,EAAE;IACf,OAAOxB,QAAQ;EACnB;EACA,IAAIyB,sBAAsB,GAAG;IAAEd,EAAE,EAAE,MAAM;IAAEH,IAAI,EAAEgB;EAAQ,CAAC;EAC1Dd,cAAc,CAACV,QAAQ,EAAEyB,sBAAsB,CAAC;EAChD,OAAOA,sBAAsB,CAACxB,KAAK;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,cAAcA,CAACV,QAAQ,EAAE0B,SAAS,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,yBAAyB,EAAEP,KAAK,EAAE;EACrH,IAAIK,iBAAiB,KAAK,KAAK,CAAC,EAAE;IAAEA,iBAAiB,GAAG,KAAK;EAAE;EAC/D,IAAIC,cAAc,KAAK,KAAK,CAAC,EAAE;IAAEA,cAAc,GAAG,IAAI;EAAE;EACxD,IAAIC,yBAAyB,KAAK,KAAK,CAAC,EAAE;IAAEA,yBAAyB,GAAG,IAAI;EAAE;EAC9E,IAAIP,KAAK,KAAK,KAAK,CAAC,EAAE;IAAEA,KAAK,GAAG,CAAC;EAAE;EACnC,IAAIK,iBAAiB,EAAE;IACnB,IAAI,OAAOA,iBAAiB,IAAI,UAAU,EAAE;MACxCA,iBAAiB,CAACD,SAAS,EAAE,CAAC,EAAE1B,QAAQ,EAAE0B,SAAS,CAAClB,IAAI,CAAC;IAC7D,CAAC,MACI;MACDsB,SAAS,CAACJ,SAAS,EAAE,CAAC,CAAC;IAC3B;EACJ;EACA;EACA,IAAIA,SAAS,CAAClB,IAAI,KAAK,EAAE,EAAE;IACvB,IAAIuB,WAAW,GAAG;MAAE7B,WAAW,EAAEF;IAAS,CAAC;IAC3C,IAAI0B,SAAS,CAACf,EAAE,KAAK,KAAK,EAAE;MACxBoB,WAAW,CAAC7B,WAAW,GAAGwB,SAAS,CAACzB,KAAK;MACzC,OAAO8B,WAAW;IACtB,CAAC,MACI,IAAIL,SAAS,CAACf,EAAE,KAAK,SAAS,EAAE;MACjCoB,WAAW,CAAC7B,WAAW,GAAGwB,SAAS,CAACzB,KAAK;MACzC8B,WAAW,CAAC3B,OAAO,GAAGJ,QAAQ,CAAC,CAAC;MAChC,OAAO+B,WAAW;IACtB,CAAC,MACI,IAAIL,SAAS,CAACf,EAAE,KAAK,MAAM,IAAIe,SAAS,CAACf,EAAE,KAAK,MAAM,EAAE;MAAE;MAC3DoB,WAAW,CAAC7B,WAAW,GAAGK,iBAAiB,CAACP,QAAQ,EAAE0B,SAAS,CAACd,IAAI,CAAC,CAAC,CAAC;MACvE,IAAIc,SAAS,CAACf,EAAE,KAAK,MAAM,EAAE;QAAE;QAC3BoB,WAAW,CAAC3B,OAAO,GAAGJ,QAAQ;MAClC;MACA,OAAO+B,WAAW;IACtB,CAAC,MACI,IAAIL,SAAS,CAACf,EAAE,KAAK,MAAM,EAAE;MAC9BoB,WAAW,CAAChB,IAAI,GAAGC,UAAU,CAAChB,QAAQ,EAAE0B,SAAS,CAACzB,KAAK,CAAC;MACxD,IAAI8B,WAAW,CAAChB,IAAI,KAAK,KAAK,EAAE;QAC5B,MAAM,IAAIrB,cAAc,CAAC,uBAAuB,EAAE,uBAAuB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;MAC1G;MACA+B,WAAW,CAAC7B,WAAW,GAAGF,QAAQ;MAClC,OAAO+B,WAAW;IACtB,CAAC,MACI,IAAIL,SAAS,CAACf,EAAE,KAAK,QAAQ,EAAE;MAAE;MAClCoB,WAAW,CAAC3B,OAAO,GAAGJ,QAAQ;MAC9B+B,WAAW,CAAC7B,WAAW,GAAG,IAAI;MAC9B,OAAO6B,WAAW;IACtB,CAAC,MACI,IAAIL,SAAS,CAACf,EAAE,KAAK,MAAM,EAAE;MAC9Be,SAAS,CAACzB,KAAK,GAAGD,QAAQ;MAC1B,OAAO+B,WAAW;IACtB,CAAC,MACI;MAAE;MACH,IAAIJ,iBAAiB,EAAE;QACnB,MAAM,IAAIjC,cAAc,CAAC,sEAAsE,EAAE,sBAAsB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;MACxJ,CAAC,MACI;QACD,OAAO+B,WAAW;MACtB;IACJ;EACJ,CAAC,CAAC,8BACG;IACD,IAAI,CAACH,cAAc,EAAE;MACjB5B,QAAQ,GAAGV,UAAU,CAACU,QAAQ,CAAC;IACnC;IACA,IAAIQ,IAAI,GAAGkB,SAAS,CAAClB,IAAI,IAAI,EAAE;IAC/B,IAAIwB,IAAI,GAAGxB,IAAI,CAACyB,KAAK,CAAC,GAAG,CAAC;IAC1B,IAAInC,GAAG,GAAGE,QAAQ;IAClB,IAAIkC,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,IAAIC,GAAG,GAAGH,IAAI,CAACI,MAAM;IACrB,IAAIC,oBAAoB,GAAGC,SAAS;IACpC,IAAIvC,GAAG,GAAG,KAAK,CAAC;IAChB,IAAIwC,gBAAgB,GAAG,KAAK,CAAC;IAC7B,IAAI,OAAOZ,iBAAiB,IAAI,UAAU,EAAE;MACxCY,gBAAgB,GAAGZ,iBAAiB;IACxC,CAAC,MACI;MACDY,gBAAgB,GAAGT,SAAS;IAChC;IACA,OAAO,IAAI,EAAE;MACT/B,GAAG,GAAGiC,IAAI,CAACE,CAAC,CAAC;MACb,IAAInC,GAAG,IAAIA,GAAG,CAACyC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;QAC/BzC,GAAG,GAAGP,qBAAqB,CAACO,GAAG,CAAC;MACpC;MACA,IAAI8B,yBAAyB,KACxB9B,GAAG,IAAI,WAAW,IACdA,GAAG,IAAI,WAAW,IAAImC,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI,aAAc,CAAC,EAAE;QACpE,MAAM,IAAIO,SAAS,CAAC,+OAA+O,CAAC;MACxQ;MACA,IAAId,iBAAiB,EAAE;QACnB,IAAIU,oBAAoB,KAAKC,SAAS,EAAE;UACpC,IAAIxC,GAAG,CAACC,GAAG,CAAC,KAAKuC,SAAS,EAAE;YACxBD,oBAAoB,GAAGL,IAAI,CAACU,KAAK,CAAC,CAAC,EAAER,CAAC,CAAC,CAACS,IAAI,CAAC,GAAG,CAAC;UACrD,CAAC,MACI,IAAIT,CAAC,IAAIC,GAAG,GAAG,CAAC,EAAE;YACnBE,oBAAoB,GAAGX,SAAS,CAAClB,IAAI;UACzC;UACA,IAAI6B,oBAAoB,KAAKC,SAAS,EAAE;YACpCC,gBAAgB,CAACb,SAAS,EAAE,CAAC,EAAE1B,QAAQ,EAAEqC,oBAAoB,CAAC;UAClE;QACJ;MACJ;MACAH,CAAC,EAAE;MACH,IAAIU,KAAK,CAACC,OAAO,CAAC/C,GAAG,CAAC,EAAE;QACpB,IAAIC,GAAG,KAAK,GAAG,EAAE;UACbA,GAAG,GAAGD,GAAG,CAACsC,MAAM;QACpB,CAAC,MACI;UACD,IAAIT,iBAAiB,IAAI,CAACpC,SAAS,CAACQ,GAAG,CAAC,EAAE;YACtC,MAAM,IAAIL,cAAc,CAAC,yHAAyH,EAAE,oCAAoC,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;UACzN,CAAC,CAAC;UAAA,KACG,IAAIT,SAAS,CAACQ,GAAG,CAAC,EAAE;YACrBA,GAAG,GAAG,CAAC,CAACA,GAAG;UACf;QACJ;QACA,IAAImC,CAAC,IAAIC,GAAG,EAAE;UACV,IAAIR,iBAAiB,IAAID,SAAS,CAACf,EAAE,KAAK,KAAK,IAAIZ,GAAG,GAAGD,GAAG,CAACsC,MAAM,EAAE;YACjE,MAAM,IAAI1C,cAAc,CAAC,kFAAkF,EAAE,+BAA+B,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;UAC7K;UACA,IAAI+B,WAAW,GAAGb,MAAM,CAACQ,SAAS,CAACf,EAAE,CAAC,CAACmC,IAAI,CAACpB,SAAS,EAAE5B,GAAG,EAAEC,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAAC;UAC5E,IAAI+B,WAAW,CAAChB,IAAI,KAAK,KAAK,EAAE;YAC5B,MAAM,IAAIrB,cAAc,CAAC,uBAAuB,EAAE,uBAAuB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;UAC1G;UACA,OAAO+B,WAAW;QACtB;MACJ,CAAC,MACI;QACD,IAAIG,CAAC,IAAIC,GAAG,EAAE;UACV,IAAIJ,WAAW,GAAGnC,MAAM,CAAC8B,SAAS,CAACf,EAAE,CAAC,CAACmC,IAAI,CAACpB,SAAS,EAAE5B,GAAG,EAAEC,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAAC;UAC5E,IAAI+B,WAAW,CAAChB,IAAI,KAAK,KAAK,EAAE;YAC5B,MAAM,IAAIrB,cAAc,CAAC,uBAAuB,EAAE,uBAAuB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;UAC1G;UACA,OAAO+B,WAAW;QACtB;MACJ;MACAjC,GAAG,GAAGA,GAAG,CAACC,GAAG,CAAC;MACd;MACA;MACA,IAAI4B,iBAAiB,IAAIO,CAAC,GAAGC,GAAG,KAAK,CAACrC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,EAAE;QACnE,MAAM,IAAIJ,cAAc,CAAC,8CAA8C,EAAE,6BAA6B,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;MACvI;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+C,UAAUA,CAAC/C,QAAQ,EAAEgD,KAAK,EAAErB,iBAAiB,EAAEC,cAAc,EAAEC,yBAAyB,EAAE;EACtG,IAAID,cAAc,KAAK,KAAK,CAAC,EAAE;IAAEA,cAAc,GAAG,IAAI;EAAE;EACxD,IAAIC,yBAAyB,KAAK,KAAK,CAAC,EAAE;IAAEA,yBAAyB,GAAG,IAAI;EAAE;EAC9E,IAAIF,iBAAiB,EAAE;IACnB,IAAI,CAACiB,KAAK,CAACC,OAAO,CAACG,KAAK,CAAC,EAAE;MACvB,MAAM,IAAItD,cAAc,CAAC,iCAAiC,EAAE,uBAAuB,CAAC;IACxF;EACJ;EACA,IAAI,CAACkC,cAAc,EAAE;IACjB5B,QAAQ,GAAGV,UAAU,CAACU,QAAQ,CAAC;EACnC;EACA,IAAIiD,OAAO,GAAG,IAAIL,KAAK,CAACI,KAAK,CAACZ,MAAM,CAAC;EACrC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAE8B,QAAQ,GAAGF,KAAK,CAACZ,MAAM,EAAEhB,CAAC,GAAG8B,QAAQ,EAAE9B,CAAC,EAAE,EAAE;IACxD;IACA6B,OAAO,CAAC7B,CAAC,CAAC,GAAGV,cAAc,CAACV,QAAQ,EAAEgD,KAAK,CAAC5B,CAAC,CAAC,EAAEO,iBAAiB,EAAE,IAAI,EAAEE,yBAAyB,EAAET,CAAC,CAAC;IACtGpB,QAAQ,GAAGiD,OAAO,CAAC7B,CAAC,CAAC,CAAClB,WAAW,CAAC,CAAC;EACvC;EACA+C,OAAO,CAAC/C,WAAW,GAAGF,QAAQ;EAC9B,OAAOiD,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,YAAYA,CAACnD,QAAQ,EAAE0B,SAAS,EAAEJ,KAAK,EAAE;EACrD,IAAI8B,eAAe,GAAG1C,cAAc,CAACV,QAAQ,EAAE0B,SAAS,CAAC;EACzD,IAAI0B,eAAe,CAACrC,IAAI,KAAK,KAAK,EAAE;IAAE;IAClC,MAAM,IAAIrB,cAAc,CAAC,uBAAuB,EAAE,uBAAuB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;EAC1G;EACA,OAAOoD,eAAe,CAAClD,WAAW;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS4B,SAASA,CAACJ,SAAS,EAAEJ,KAAK,EAAEtB,QAAQ,EAAEqC,oBAAoB,EAAE;EACxE,IAAI,OAAOX,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,IAAI,IAAIkB,KAAK,CAACC,OAAO,CAACnB,SAAS,CAAC,EAAE;IACjF,MAAM,IAAIhC,cAAc,CAAC,4BAA4B,EAAE,yBAAyB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;EACjH,CAAC,MACI,IAAI,CAACJ,MAAM,CAAC8B,SAAS,CAACf,EAAE,CAAC,EAAE;IAC5B,MAAM,IAAIjB,cAAc,CAAC,sEAAsE,EAAE,sBAAsB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;EACxJ,CAAC,MACI,IAAI,OAAO0B,SAAS,CAAClB,IAAI,KAAK,QAAQ,EAAE;IACzC,MAAM,IAAId,cAAc,CAAC,2CAA2C,EAAE,wBAAwB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;EAC/H,CAAC,MACI,IAAI0B,SAAS,CAAClB,IAAI,CAACgC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAId,SAAS,CAAClB,IAAI,CAAC4B,MAAM,GAAG,CAAC,EAAE;IACrE;IACA,MAAM,IAAI1C,cAAc,CAAC,+CAA+C,EAAE,wBAAwB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;EACnI,CAAC,MACI,IAAI,CAAC0B,SAAS,CAACf,EAAE,KAAK,MAAM,IAAIe,SAAS,CAACf,EAAE,KAAK,MAAM,KAAK,OAAOe,SAAS,CAACd,IAAI,KAAK,QAAQ,EAAE;IACjG,MAAM,IAAIlB,cAAc,CAAC,uFAAuF,EAAE,yBAAyB,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;EAC5K,CAAC,MACI,IAAI,CAAC0B,SAAS,CAACf,EAAE,KAAK,KAAK,IAAIe,SAAS,CAACf,EAAE,KAAK,SAAS,IAAIe,SAAS,CAACf,EAAE,KAAK,MAAM,KAAKe,SAAS,CAACzB,KAAK,KAAKqC,SAAS,EAAE;IACzH,MAAM,IAAI5C,cAAc,CAAC,kGAAkG,EAAE,0BAA0B,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;EACxL,CAAC,MACI,IAAI,CAAC0B,SAAS,CAACf,EAAE,KAAK,KAAK,IAAIe,SAAS,CAACf,EAAE,KAAK,SAAS,IAAIe,SAAS,CAACf,EAAE,KAAK,MAAM,KAAKlB,YAAY,CAACiC,SAAS,CAACzB,KAAK,CAAC,EAAE;IACzH,MAAM,IAAIP,cAAc,CAAC,kGAAkG,EAAE,0CAA0C,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;EACxM,CAAC,MACI,IAAIA,QAAQ,EAAE;IACf,IAAI0B,SAAS,CAACf,EAAE,IAAI,KAAK,EAAE;MACvB,IAAI0C,OAAO,GAAG3B,SAAS,CAAClB,IAAI,CAACyB,KAAK,CAAC,GAAG,CAAC,CAACG,MAAM;MAC9C,IAAIkB,eAAe,GAAGjB,oBAAoB,CAACJ,KAAK,CAAC,GAAG,CAAC,CAACG,MAAM;MAC5D,IAAIiB,OAAO,KAAKC,eAAe,GAAG,CAAC,IAAID,OAAO,KAAKC,eAAe,EAAE;QAChE,MAAM,IAAI5D,cAAc,CAAC,uDAAuD,EAAE,2BAA2B,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;MAC9I;IACJ,CAAC,MACI,IAAI0B,SAAS,CAACf,EAAE,KAAK,SAAS,IAAIe,SAAS,CAACf,EAAE,KAAK,QAAQ,IAAIe,SAAS,CAACf,EAAE,KAAK,MAAM,EAAE;MACzF,IAAIe,SAAS,CAAClB,IAAI,KAAK6B,oBAAoB,EAAE;QACzC,MAAM,IAAI3C,cAAc,CAAC,4DAA4D,EAAE,6BAA6B,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;MACrJ;IACJ,CAAC,MACI,IAAI0B,SAAS,CAACf,EAAE,KAAK,MAAM,IAAIe,SAAS,CAACf,EAAE,KAAK,MAAM,EAAE;MACzD,IAAI4C,aAAa,GAAG;QAAE5C,EAAE,EAAE,MAAM;QAAEH,IAAI,EAAEkB,SAAS,CAACd,IAAI;QAAEX,KAAK,EAAEqC;MAAU,CAAC;MAC1E,IAAIkB,KAAK,GAAGC,QAAQ,CAAC,CAACF,aAAa,CAAC,EAAEvD,QAAQ,CAAC;MAC/C,IAAIwD,KAAK,IAAIA,KAAK,CAACE,IAAI,KAAK,6BAA6B,EAAE;QACvD,MAAM,IAAIhE,cAAc,CAAC,8DAA8D,EAAE,6BAA6B,EAAE4B,KAAK,EAAEI,SAAS,EAAE1B,QAAQ,CAAC;MACvJ;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASyD,QAAQA,CAACE,QAAQ,EAAE3D,QAAQ,EAAE4D,iBAAiB,EAAE;EAC5D,IAAI;IACA,IAAI,CAAChB,KAAK,CAACC,OAAO,CAACc,QAAQ,CAAC,EAAE;MAC1B,MAAM,IAAIjE,cAAc,CAAC,iCAAiC,EAAE,uBAAuB,CAAC;IACxF;IACA,IAAIM,QAAQ,EAAE;MACV;MACA+C,UAAU,CAACzD,UAAU,CAACU,QAAQ,CAAC,EAAEV,UAAU,CAACqE,QAAQ,CAAC,EAAEC,iBAAiB,IAAI,IAAI,CAAC;IACrF,CAAC,MACI;MACDA,iBAAiB,GAAGA,iBAAiB,IAAI9B,SAAS;MAClD,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,QAAQ,CAACvB,MAAM,EAAEhB,CAAC,EAAE,EAAE;QACtCwC,iBAAiB,CAACD,QAAQ,CAACvC,CAAC,CAAC,EAAEA,CAAC,EAAEpB,QAAQ,EAAEsC,SAAS,CAAC;MAC1D;IACJ;EACJ,CAAC,CACD,OAAOuB,CAAC,EAAE;IACN,IAAIA,CAAC,YAAYnE,cAAc,EAAE;MAC7B,OAAOmE,CAAC;IACZ,CAAC,MACI;MACD,MAAMA,CAAC;IACX;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS7C,UAAUA,CAAC8C,CAAC,EAAEC,CAAC,EAAE;EAC7B,IAAID,CAAC,KAAKC,CAAC,EACP,OAAO,IAAI;EACf,IAAID,CAAC,IAAIC,CAAC,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,EAAE;IACxD,IAAIC,IAAI,GAAGpB,KAAK,CAACC,OAAO,CAACiB,CAAC,CAAC;MAAEG,IAAI,GAAGrB,KAAK,CAACC,OAAO,CAACkB,CAAC,CAAC;MAAE3C,CAAC;MAAEgB,MAAM;MAAErC,GAAG;IACpE,IAAIiE,IAAI,IAAIC,IAAI,EAAE;MACd7B,MAAM,GAAG0B,CAAC,CAAC1B,MAAM;MACjB,IAAIA,MAAM,IAAI2B,CAAC,CAAC3B,MAAM,EAClB,OAAO,KAAK;MAChB,KAAKhB,CAAC,GAAGgB,MAAM,EAAEhB,CAAC,EAAE,KAAK,CAAC,GACtB,IAAI,CAACJ,UAAU,CAAC8C,CAAC,CAAC1C,CAAC,CAAC,EAAE2C,CAAC,CAAC3C,CAAC,CAAC,CAAC,EACvB,OAAO,KAAK;MACpB,OAAO,IAAI;IACf;IACA,IAAI4C,IAAI,IAAIC,IAAI,EACZ,OAAO,KAAK;IAChB,IAAIjC,IAAI,GAAGkC,MAAM,CAAClC,IAAI,CAAC8B,CAAC,CAAC;IACzB1B,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpB,IAAIA,MAAM,KAAK8B,MAAM,CAAClC,IAAI,CAAC+B,CAAC,CAAC,CAAC3B,MAAM,EAChC,OAAO,KAAK;IAChB,KAAKhB,CAAC,GAAGgB,MAAM,EAAEhB,CAAC,EAAE,KAAK,CAAC,GACtB,IAAI,CAAC2C,CAAC,CAACI,cAAc,CAACnC,IAAI,CAACZ,CAAC,CAAC,CAAC,EAC1B,OAAO,KAAK;IACpB,KAAKA,CAAC,GAAGgB,MAAM,EAAEhB,CAAC,EAAE,KAAK,CAAC,GAAG;MACzBrB,GAAG,GAAGiC,IAAI,CAACZ,CAAC,CAAC;MACb,IAAI,CAACJ,UAAU,CAAC8C,CAAC,CAAC/D,GAAG,CAAC,EAAEgE,CAAC,CAAChE,GAAG,CAAC,CAAC,EAC3B,OAAO,KAAK;IACpB;IACA,OAAO,IAAI;EACf;EACA,OAAO+D,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC;AAC7B;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}