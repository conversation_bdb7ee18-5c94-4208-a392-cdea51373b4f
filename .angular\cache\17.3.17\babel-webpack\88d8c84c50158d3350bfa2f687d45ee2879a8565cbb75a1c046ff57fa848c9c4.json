{"ast": null, "code": "var assignValue = require('./_assignValue'),\n  castPath = require('./_castPath'),\n  isIndex = require('./_isIndex'),\n  isObject = require('./isObject'),\n  toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n  var index = -1,\n    length = path.length,\n    lastIndex = length - 1,\n    nested = object;\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n      newValue = value;\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue) ? objValue : isIndex(path[index + 1]) ? [] : {};\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\nmodule.exports = baseSet;", "map": {"version": 3, "names": ["assignValue", "require", "<PERSON><PERSON><PERSON>", "isIndex", "isObject", "to<PERSON><PERSON>", "baseSet", "object", "path", "value", "customizer", "index", "length", "lastIndex", "nested", "key", "newValue", "objValue", "undefined", "module", "exports"], "sources": ["D:/workspace/formtest_aug/node_modules/lodash/_baseSet.js"], "sourcesContent": ["var assignValue = require('./_assignValue'),\n    castPath = require('./_castPath'),\n    isIndex = require('./_isIndex'),\n    isObject = require('./isObject'),\n    toKey = require('./_toKey');\n\n/**\n * The base implementation of `_.set`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {*} value The value to set.\n * @param {Function} [customizer] The function to customize path creation.\n * @returns {Object} Returns `object`.\n */\nfunction baseSet(object, path, value, customizer) {\n  if (!isObject(object)) {\n    return object;\n  }\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      lastIndex = length - 1,\n      nested = object;\n\n  while (nested != null && ++index < length) {\n    var key = toKey(path[index]),\n        newValue = value;\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return object;\n    }\n\n    if (index != lastIndex) {\n      var objValue = nested[key];\n      newValue = customizer ? customizer(objValue, key, nested) : undefined;\n      if (newValue === undefined) {\n        newValue = isObject(objValue)\n          ? objValue\n          : (isIndex(path[index + 1]) ? [] : {});\n      }\n    }\n    assignValue(nested, key, newValue);\n    nested = nested[key];\n  }\n  return object;\n}\n\nmodule.exports = baseSet;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACvCC,QAAQ,GAAGD,OAAO,CAAC,aAAa,CAAC;EACjCE,OAAO,GAAGF,OAAO,CAAC,YAAY,CAAC;EAC/BG,QAAQ,GAAGH,OAAO,CAAC,YAAY,CAAC;EAChCI,KAAK,GAAGJ,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,OAAOA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAE;EAChD,IAAI,CAACN,QAAQ,CAACG,MAAM,CAAC,EAAE;IACrB,OAAOA,MAAM;EACf;EACAC,IAAI,GAAGN,QAAQ,CAACM,IAAI,EAAED,MAAM,CAAC;EAE7B,IAAII,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,SAAS,GAAGD,MAAM,GAAG,CAAC;IACtBE,MAAM,GAAGP,MAAM;EAEnB,OAAOO,MAAM,IAAI,IAAI,IAAI,EAAEH,KAAK,GAAGC,MAAM,EAAE;IACzC,IAAIG,GAAG,GAAGV,KAAK,CAACG,IAAI,CAACG,KAAK,CAAC,CAAC;MACxBK,QAAQ,GAAGP,KAAK;IAEpB,IAAIM,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,aAAa,IAAIA,GAAG,KAAK,WAAW,EAAE;MACvE,OAAOR,MAAM;IACf;IAEA,IAAII,KAAK,IAAIE,SAAS,EAAE;MACtB,IAAII,QAAQ,GAAGH,MAAM,CAACC,GAAG,CAAC;MAC1BC,QAAQ,GAAGN,UAAU,GAAGA,UAAU,CAACO,QAAQ,EAAEF,GAAG,EAAED,MAAM,CAAC,GAAGI,SAAS;MACrE,IAAIF,QAAQ,KAAKE,SAAS,EAAE;QAC1BF,QAAQ,GAAGZ,QAAQ,CAACa,QAAQ,CAAC,GACzBA,QAAQ,GACPd,OAAO,CAACK,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAE;MAC1C;IACF;IACAX,WAAW,CAACc,MAAM,EAAEC,GAAG,EAAEC,QAAQ,CAAC;IAClCF,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC;EACtB;EACA,OAAOR,MAAM;AACf;AAEAY,MAAM,CAACC,OAAO,GAAGd,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}