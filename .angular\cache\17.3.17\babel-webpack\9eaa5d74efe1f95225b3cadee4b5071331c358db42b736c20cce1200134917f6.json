{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMaximumSelectedCountInfo = exports.validateMaximumSelectedCountSync = exports.validateMaximumSelectedCount = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableSelectBoxesComponent = component => {\n  var _a;\n  return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('maxSelectedCount'));\n};\nconst getValidationSetting = component => {\n  var _a;\n  let max = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.maxSelectedCount;\n  if (typeof max === 'string') {\n    max = parseFloat(max);\n  }\n  return max;\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!isValidatableSelectBoxesComponent(component)) {\n    return false;\n  }\n  if (!value) {\n    return false;\n  }\n  if (!getValidationSetting(component)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nfunction validateValue(value, context) {\n  if (value == null || typeof value !== 'object') {\n    throw new error_1.ProcessorError(`Cannot validate maximum selected count for value ${value} as it is not an object`, context, 'validate:validateMaximumSelectedCount');\n  }\n  const subValues = Object.values(value);\n  if (!subValues.every(value => typeof value === 'boolean')) {\n    throw new error_1.ProcessorError(`Cannot validate maximum selected count for value ${value} because it has non-boolean members`, context, 'validate:validateMaximumSelectedCount');\n  }\n}\nconst validateMaximumSelectedCount = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMaximumSelectedCountSync)(context);\n});\nexports.validateMaximumSelectedCount = validateMaximumSelectedCount;\nconst validateMaximumSelectedCountSync = context => {\n  const {\n    component,\n    value\n  } = context;\n  try {\n    if (!(0, exports.shouldValidate)(context)) {\n      return null;\n    }\n    validateValue(value, context);\n    const max = getValidationSetting(component);\n    if (!max) {\n      return null;\n    }\n    const count = Object.keys(value).reduce((sum, key) => value[key] ? ++sum : sum, 0);\n    // Should not be triggered if there is no options selected at all\n    if (count <= 0) {\n      return null;\n    }\n    return count > max ? new error_1.FieldError(component.maxSelectedCountMessage || 'maxSelectedCount', Object.assign(Object.assign({}, context), {\n      maxCount: String(max),\n      setting: String(max)\n    })) : null;\n  } catch (err) {\n    throw new error_1.ProcessorError(err.message || err, context, 'validate:validateMaximumSelectedCount');\n  }\n};\nexports.validateMaximumSelectedCountSync = validateMaximumSelectedCountSync;\nexports.validateMaximumSelectedCountInfo = {\n  name: 'validateMaximumSelectedCount',\n  process: exports.validateMaximumSelectedCount,\n  processSync: exports.validateMaximumSelectedCountSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateMaximumSelectedCountInfo", "validateMaximumSelectedCountSync", "validateMaximumSelectedCount", "shouldValidate", "error_1", "require", "isValidatableSelectBoxesComponent", "component", "_a", "validate", "hasOwnProperty", "getValidationSetting", "max", "maxSelectedCount", "parseFloat", "context", "validate<PERSON><PERSON>ue", "ProcessorError", "subValues", "values", "every", "count", "keys", "reduce", "sum", "key", "FieldError", "maxSelectedCountMessage", "assign", "maxCount", "String", "setting", "err", "message", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMaximumSelectedCount.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMaximumSelectedCountInfo = exports.validateMaximumSelectedCountSync = exports.validateMaximumSelectedCount = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableSelectBoxesComponent = (component) => {\n    var _a;\n    return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('maxSelectedCount'));\n};\nconst getValidationSetting = (component) => {\n    var _a;\n    let max = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.maxSelectedCount;\n    if (typeof max === 'string') {\n        max = parseFloat(max);\n    }\n    return max;\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!isValidatableSelectBoxesComponent(component)) {\n        return false;\n    }\n    if (!value) {\n        return false;\n    }\n    if (!getValidationSetting(component)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nfunction validateValue(value, context) {\n    if (value == null || typeof value !== 'object') {\n        throw new error_1.ProcessorError(`Cannot validate maximum selected count for value ${value} as it is not an object`, context, 'validate:validateMaximumSelectedCount');\n    }\n    const subValues = Object.values(value);\n    if (!subValues.every((value) => typeof value === 'boolean')) {\n        throw new error_1.ProcessorError(`Cannot validate maximum selected count for value ${value} because it has non-boolean members`, context, 'validate:validateMaximumSelectedCount');\n    }\n}\nconst validateMaximumSelectedCount = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMaximumSelectedCountSync)(context);\n});\nexports.validateMaximumSelectedCount = validateMaximumSelectedCount;\nconst validateMaximumSelectedCountSync = (context) => {\n    const { component, value } = context;\n    try {\n        if (!(0, exports.shouldValidate)(context)) {\n            return null;\n        }\n        validateValue(value, context);\n        const max = getValidationSetting(component);\n        if (!max) {\n            return null;\n        }\n        const count = Object.keys(value).reduce((sum, key) => (value[key] ? ++sum : sum), 0);\n        // Should not be triggered if there is no options selected at all\n        if (count <= 0) {\n            return null;\n        }\n        return count > max\n            ? new error_1.FieldError(component.maxSelectedCountMessage || 'maxSelectedCount', Object.assign(Object.assign({}, context), { maxCount: String(max), setting: String(max) }))\n            : null;\n    }\n    catch (err) {\n        throw new error_1.ProcessorError(err.message || err, context, 'validate:validateMaximumSelectedCount');\n    }\n};\nexports.validateMaximumSelectedCountSync = validateMaximumSelectedCountSync;\nexports.validateMaximumSelectedCountInfo = {\n    name: 'validateMaximumSelectedCount',\n    process: exports.validateMaximumSelectedCount,\n    processSync: exports.validateMaximumSelectedCountSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,gCAAgC,GAAGD,OAAO,CAACE,gCAAgC,GAAGF,OAAO,CAACG,4BAA4B,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AAC5J,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,iCAAiC,GAAIC,SAAS,IAAK;EACrD,IAAIC,EAAE;EACN,OAAOD,SAAS,KAAK,CAACC,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,cAAc,CAAC,kBAAkB,CAAC,CAAC;AAC9H,CAAC;AACD,MAAMC,oBAAoB,GAAIJ,SAAS,IAAK;EACxC,IAAIC,EAAE;EACN,IAAII,GAAG,GAAG,CAACJ,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,gBAAgB;EAC5F,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IACzBA,GAAG,GAAGE,UAAU,CAACF,GAAG,CAAC;EACzB;EACA,OAAOA,GAAG;AACd,CAAC;AACD,MAAMT,cAAc,GAAIY,OAAO,IAAK;EAChC,MAAM;IAAER,SAAS;IAAEvB;EAAM,CAAC,GAAG+B,OAAO;EACpC,IAAI,CAACT,iCAAiC,CAACC,SAAS,CAAC,EAAE;IAC/C,OAAO,KAAK;EAChB;EACA,IAAI,CAACvB,KAAK,EAAE;IACR,OAAO,KAAK;EAChB;EACA,IAAI,CAAC2B,oBAAoB,CAACJ,SAAS,CAAC,EAAE;IAClC,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDR,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,SAASa,aAAaA,CAAChC,KAAK,EAAE+B,OAAO,EAAE;EACnC,IAAI/B,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC5C,MAAM,IAAIoB,OAAO,CAACa,cAAc,CAAC,oDAAoDjC,KAAK,yBAAyB,EAAE+B,OAAO,EAAE,uCAAuC,CAAC;EAC1K;EACA,MAAMG,SAAS,GAAGrB,MAAM,CAACsB,MAAM,CAACnC,KAAK,CAAC;EACtC,IAAI,CAACkC,SAAS,CAACE,KAAK,CAAEpC,KAAK,IAAK,OAAOA,KAAK,KAAK,SAAS,CAAC,EAAE;IACzD,MAAM,IAAIoB,OAAO,CAACa,cAAc,CAAC,oDAAoDjC,KAAK,qCAAqC,EAAE+B,OAAO,EAAE,uCAAuC,CAAC;EACtL;AACJ;AACA,MAAMb,4BAA4B,GAAIa,OAAO,IAAKrC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC7F,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,gCAAgC,EAAEc,OAAO,CAAC;AACjE,CAAC,CAAC;AACFhB,OAAO,CAACG,4BAA4B,GAAGA,4BAA4B;AACnE,MAAMD,gCAAgC,GAAIc,OAAO,IAAK;EAClD,MAAM;IAAER,SAAS;IAAEvB;EAAM,CAAC,GAAG+B,OAAO;EACpC,IAAI;IACA,IAAI,CAAC,CAAC,CAAC,EAAEhB,OAAO,CAACI,cAAc,EAAEY,OAAO,CAAC,EAAE;MACvC,OAAO,IAAI;IACf;IACAC,aAAa,CAAChC,KAAK,EAAE+B,OAAO,CAAC;IAC7B,MAAMH,GAAG,GAAGD,oBAAoB,CAACJ,SAAS,CAAC;IAC3C,IAAI,CAACK,GAAG,EAAE;MACN,OAAO,IAAI;IACf;IACA,MAAMS,KAAK,GAAGxB,MAAM,CAACyB,IAAI,CAACtC,KAAK,CAAC,CAACuC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAMzC,KAAK,CAACyC,GAAG,CAAC,GAAG,EAAED,GAAG,GAAGA,GAAI,EAAE,CAAC,CAAC;IACpF;IACA,IAAIH,KAAK,IAAI,CAAC,EAAE;MACZ,OAAO,IAAI;IACf;IACA,OAAOA,KAAK,GAAGT,GAAG,GACZ,IAAIR,OAAO,CAACsB,UAAU,CAACnB,SAAS,CAACoB,uBAAuB,IAAI,kBAAkB,EAAE9B,MAAM,CAAC+B,MAAM,CAAC/B,MAAM,CAAC+B,MAAM,CAAC,CAAC,CAAC,EAAEb,OAAO,CAAC,EAAE;MAAEc,QAAQ,EAAEC,MAAM,CAAClB,GAAG,CAAC;MAAEmB,OAAO,EAAED,MAAM,CAAClB,GAAG;IAAE,CAAC,CAAC,CAAC,GAC3K,IAAI;EACd,CAAC,CACD,OAAOoB,GAAG,EAAE;IACR,MAAM,IAAI5B,OAAO,CAACa,cAAc,CAACe,GAAG,CAACC,OAAO,IAAID,GAAG,EAAEjB,OAAO,EAAE,uCAAuC,CAAC;EAC1G;AACJ,CAAC;AACDhB,OAAO,CAACE,gCAAgC,GAAGA,gCAAgC;AAC3EF,OAAO,CAACC,gCAAgC,GAAG;EACvCkC,IAAI,EAAE,8BAA8B;EACpCC,OAAO,EAAEpC,OAAO,CAACG,4BAA4B;EAC7CkC,WAAW,EAAErC,OAAO,CAACE,gCAAgC;EACrDoC,aAAa,EAAEtC,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}