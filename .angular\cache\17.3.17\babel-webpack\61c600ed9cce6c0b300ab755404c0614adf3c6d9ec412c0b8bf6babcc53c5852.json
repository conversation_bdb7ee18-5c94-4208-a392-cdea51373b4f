{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.calculateProcessInfo = exports.calculateProcess = exports.calculateProcessSync = exports.shouldCalculate = void 0;\nconst lodash_1 = require(\"lodash\");\nconst utils_1 = require(\"../../utils\");\nconst shouldCalculate = context => {\n  const {\n    component,\n    config\n  } = context;\n  if (!component.calculateValue || (config === null || config === void 0 ? void 0 : config.server) && !component.calculateServer) {\n    return false;\n  }\n  return true;\n};\nexports.shouldCalculate = shouldCalculate;\nconst calculateProcessSync = context => {\n  const {\n    component,\n    data,\n    scope,\n    path,\n    value\n  } = context;\n  if (!(0, exports.shouldCalculate)(context) || !component.calculateValue) {\n    return;\n  }\n  const calculationContext = scope.fetched ? Object.assign(Object.assign({}, context), {\n    data: Object.assign(Object.assign({}, data), scope.fetched)\n  }) : context;\n  if (!scope.calculated) scope.calculated = [];\n  const newValue = (0, utils_1.evaluate)(component.calculateValue, calculationContext, 'value', false, context => {\n    context.value = value || null;\n  });\n  // Only set a new value if it is not \"null\" which would be the case if no calculation occurred.\n  if (newValue !== null) {\n    scope.calculated.push({\n      path,\n      value: newValue\n    });\n    (0, lodash_1.set)(data, path, newValue);\n  }\n  return;\n};\nexports.calculateProcessSync = calculateProcessSync;\nconst calculateProcess = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.calculateProcessSync)(context);\n});\nexports.calculateProcess = calculateProcess;\nexports.calculateProcessInfo = {\n  name: 'calculate',\n  process: exports.calculateProcess,\n  processSync: exports.calculateProcessSync,\n  shouldProcess: exports.shouldCalculate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "calculateProcessInfo", "calculateProcess", "calculateProcessSync", "shouldCalculate", "lodash_1", "require", "utils_1", "context", "component", "config", "calculateValue", "server", "calculateServer", "data", "scope", "path", "calculationContext", "fetched", "assign", "calculated", "newValue", "evaluate", "push", "set", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/calculation/index.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.calculateProcessInfo = exports.calculateProcess = exports.calculateProcessSync = exports.shouldCalculate = void 0;\nconst lodash_1 = require(\"lodash\");\nconst utils_1 = require(\"../../utils\");\nconst shouldCalculate = (context) => {\n    const { component, config } = context;\n    if (!component.calculateValue || ((config === null || config === void 0 ? void 0 : config.server) && !component.calculateServer)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldCalculate = shouldCalculate;\nconst calculateProcessSync = (context) => {\n    const { component, data, scope, path, value } = context;\n    if (!(0, exports.shouldCalculate)(context) || !component.calculateValue) {\n        return;\n    }\n    const calculationContext = scope.fetched\n        ? Object.assign(Object.assign({}, context), { data: Object.assign(Object.assign({}, data), scope.fetched) }) : context;\n    if (!scope.calculated)\n        scope.calculated = [];\n    const newValue = (0, utils_1.evaluate)(component.calculateValue, calculationContext, 'value', false, (context) => {\n        context.value = value || null;\n    });\n    // Only set a new value if it is not \"null\" which would be the case if no calculation occurred.\n    if (newValue !== null) {\n        scope.calculated.push({\n            path,\n            value: newValue,\n        });\n        (0, lodash_1.set)(data, path, newValue);\n    }\n    return;\n};\nexports.calculateProcessSync = calculateProcessSync;\nconst calculateProcess = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.calculateProcessSync)(context);\n});\nexports.calculateProcess = calculateProcess;\nexports.calculateProcessInfo = {\n    name: 'calculate',\n    process: exports.calculateProcess,\n    processSync: exports.calculateProcessSync,\n    shouldProcess: exports.shouldCalculate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,oBAAoB,GAAGD,OAAO,CAACE,gBAAgB,GAAGF,OAAO,CAACG,oBAAoB,GAAGH,OAAO,CAACI,eAAe,GAAG,KAAK,CAAC;AACzH,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,OAAO,GAAGD,OAAO,CAAC,aAAa,CAAC;AACtC,MAAMF,eAAe,GAAII,OAAO,IAAK;EACjC,MAAM;IAAEC,SAAS;IAAEC;EAAO,CAAC,GAAGF,OAAO;EACrC,IAAI,CAACC,SAAS,CAACE,cAAc,IAAK,CAACD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,MAAM,KAAK,CAACH,SAAS,CAACI,eAAgB,EAAE;IAC9H,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDb,OAAO,CAACI,eAAe,GAAGA,eAAe;AACzC,MAAMD,oBAAoB,GAAIK,OAAO,IAAK;EACtC,MAAM;IAAEC,SAAS;IAAEK,IAAI;IAAEC,KAAK;IAAEC,IAAI;IAAE/B;EAAM,CAAC,GAAGuB,OAAO;EACvD,IAAI,CAAC,CAAC,CAAC,EAAER,OAAO,CAACI,eAAe,EAAEI,OAAO,CAAC,IAAI,CAACC,SAAS,CAACE,cAAc,EAAE;IACrE;EACJ;EACA,MAAMM,kBAAkB,GAAGF,KAAK,CAACG,OAAO,GAClCpB,MAAM,CAACqB,MAAM,CAACrB,MAAM,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAEX,OAAO,CAAC,EAAE;IAAEM,IAAI,EAAEhB,MAAM,CAACqB,MAAM,CAACrB,MAAM,CAACqB,MAAM,CAAC,CAAC,CAAC,EAAEL,IAAI,CAAC,EAAEC,KAAK,CAACG,OAAO;EAAE,CAAC,CAAC,GAAGV,OAAO;EAC1H,IAAI,CAACO,KAAK,CAACK,UAAU,EACjBL,KAAK,CAACK,UAAU,GAAG,EAAE;EACzB,MAAMC,QAAQ,GAAG,CAAC,CAAC,EAAEd,OAAO,CAACe,QAAQ,EAAEb,SAAS,CAACE,cAAc,EAAEM,kBAAkB,EAAE,OAAO,EAAE,KAAK,EAAGT,OAAO,IAAK;IAC9GA,OAAO,CAACvB,KAAK,GAAGA,KAAK,IAAI,IAAI;EACjC,CAAC,CAAC;EACF;EACA,IAAIoC,QAAQ,KAAK,IAAI,EAAE;IACnBN,KAAK,CAACK,UAAU,CAACG,IAAI,CAAC;MAClBP,IAAI;MACJ/B,KAAK,EAAEoC;IACX,CAAC,CAAC;IACF,CAAC,CAAC,EAAEhB,QAAQ,CAACmB,GAAG,EAAEV,IAAI,EAAEE,IAAI,EAAEK,QAAQ,CAAC;EAC3C;EACA;AACJ,CAAC;AACDrB,OAAO,CAACG,oBAAoB,GAAGA,oBAAoB;AACnD,MAAMD,gBAAgB,GAAIM,OAAO,IAAK7B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACjF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACG,oBAAoB,EAAEK,OAAO,CAAC;AACrD,CAAC,CAAC;AACFR,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3CF,OAAO,CAACC,oBAAoB,GAAG;EAC3BwB,IAAI,EAAE,WAAW;EACjBC,OAAO,EAAE1B,OAAO,CAACE,gBAAgB;EACjCyB,WAAW,EAAE3B,OAAO,CAACG,oBAAoB;EACzCyB,aAAa,EAAE5B,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}