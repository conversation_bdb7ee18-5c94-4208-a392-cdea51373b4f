{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateNumberInfo = exports.validateNumberSync = exports.validateNumber = exports.shouldValidate = void 0;\nconst FieldError_1 = require(\"../../../error/FieldError\");\nconst isValidatableNumberComponent = component => {\n  return component && component.type === 'number';\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!value) {\n    return false;\n  }\n  if (component.multiple && Array.isArray(value) && value.length === 0) {\n    return false;\n  }\n  if (!isValidatableNumberComponent(component)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateNumber = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateNumberSync)(context);\n});\nexports.validateNumber = validateNumber;\nconst validateNumberSync = context => {\n  const error = new FieldError_1.FieldError('number', context);\n  const {\n    value\n  } = context;\n  if (value && typeof value !== 'number') {\n    return error;\n  }\n  return null;\n};\nexports.validateNumberSync = validateNumberSync;\nexports.validateNumberInfo = {\n  name: 'validateNumber',\n  process: exports.validateNumber,\n  processSync: exports.validateNumberSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateNumberInfo", "validateNumberSync", "validateNumber", "shouldValidate", "FieldError_1", "require", "isValidatableNumberComponent", "component", "type", "context", "multiple", "Array", "isArray", "length", "error", "FieldError", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateNumber.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateNumberInfo = exports.validateNumberSync = exports.validateNumber = exports.shouldValidate = void 0;\nconst FieldError_1 = require(\"../../../error/FieldError\");\nconst isValidatableNumberComponent = (component) => {\n    return component && component.type === 'number';\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!value) {\n        return false;\n    }\n    if (component.multiple && Array.isArray(value) && value.length === 0) {\n        return false;\n    }\n    if (!isValidatableNumberComponent(component)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateNumber = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateNumberSync)(context);\n});\nexports.validateNumber = validateNumber;\nconst validateNumberSync = (context) => {\n    const error = new FieldError_1.FieldError('number', context);\n    const { value } = context;\n    if (value && typeof value !== 'number') {\n        return error;\n    }\n    return null;\n};\nexports.validateNumberSync = validateNumberSync;\nexports.validateNumberInfo = {\n    name: 'validateNumber',\n    process: exports.validateNumber,\n    processSync: exports.validateNumberSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,kBAAkB,GAAGD,OAAO,CAACE,kBAAkB,GAAGF,OAAO,CAACG,cAAc,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AAClH,MAAMC,YAAY,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACzD,MAAMC,4BAA4B,GAAIC,SAAS,IAAK;EAChD,OAAOA,SAAS,IAAIA,SAAS,CAACC,IAAI,KAAK,QAAQ;AACnD,CAAC;AACD,MAAML,cAAc,GAAIM,OAAO,IAAK;EAChC,MAAM;IAAEF,SAAS;IAAEvB;EAAM,CAAC,GAAGyB,OAAO;EACpC,IAAI,CAACzB,KAAK,EAAE;IACR,OAAO,KAAK;EAChB;EACA,IAAIuB,SAAS,CAACG,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAAC5B,KAAK,CAAC,IAAIA,KAAK,CAAC6B,MAAM,KAAK,CAAC,EAAE;IAClE,OAAO,KAAK;EAChB;EACA,IAAI,CAACP,4BAA4B,CAACC,SAAS,CAAC,EAAE;IAC1C,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDR,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,cAAc,GAAIO,OAAO,IAAK/B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC/E,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,kBAAkB,EAAEQ,OAAO,CAAC;AACnD,CAAC,CAAC;AACFV,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvC,MAAMD,kBAAkB,GAAIQ,OAAO,IAAK;EACpC,MAAMK,KAAK,GAAG,IAAIV,YAAY,CAACW,UAAU,CAAC,QAAQ,EAAEN,OAAO,CAAC;EAC5D,MAAM;IAAEzB;EAAM,CAAC,GAAGyB,OAAO;EACzB,IAAIzB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACpC,OAAO8B,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDf,OAAO,CAACE,kBAAkB,GAAGA,kBAAkB;AAC/CF,OAAO,CAACC,kBAAkB,GAAG;EACzBgB,IAAI,EAAE,gBAAgB;EACtBC,OAAO,EAAElB,OAAO,CAACG,cAAc;EAC/BgB,WAAW,EAAEnB,OAAO,CAACE,kBAAkB;EACvCkB,aAAa,EAAEpB,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}