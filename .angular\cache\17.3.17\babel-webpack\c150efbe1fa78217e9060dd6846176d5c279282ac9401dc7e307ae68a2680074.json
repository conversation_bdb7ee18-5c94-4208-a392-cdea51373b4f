{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _native = _interopRequireDefault(require(\"./native.js\"));\nvar _rng = _interopRequireDefault(require(\"./rng.js\"));\nvar _stringify = require(\"./stringify.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction v4(options, buf, offset) {\n  if (_native.default.randomUUID && !buf && !options) {\n    return _native.default.randomUUID();\n  }\n  options = options || {};\n  const rnds = options.random || (options.rng || _rng.default)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n    return buf;\n  }\n  return (0, _stringify.unsafeStringify)(rnds);\n}\nvar _default = v4;\nexports.default = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "_native", "_interopRequireDefault", "require", "_rng", "_stringify", "obj", "__esModule", "v4", "options", "buf", "offset", "randomUUID", "rnds", "random", "rng", "i", "unsafeStringify", "_default"], "sources": ["D:/workspace/formtest_aug/node_modules/uuid/dist/commonjs-browser/v4.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _native = _interopRequireDefault(require(\"./native.js\"));\n\nvar _rng = _interopRequireDefault(require(\"./rng.js\"));\n\nvar _stringify = require(\"./stringify.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction v4(options, buf, offset) {\n  if (_native.default.randomUUID && !buf && !options) {\n    return _native.default.randomUUID();\n  }\n\n  options = options || {};\n\n  const rnds = options.random || (options.rng || _rng.default)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return (0, _stringify.unsafeStringify)(rnds);\n}\n\nvar _default = v4;\nexports.default = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAExB,IAAIC,OAAO,GAAGC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE5D,IAAIC,IAAI,GAAGF,sBAAsB,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;AAEtD,IAAIE,UAAU,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AAE1C,SAASD,sBAAsBA,CAACI,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEN,OAAO,EAAEM;EAAI,CAAC;AAAE;AAE9F,SAASE,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAChC,IAAIV,OAAO,CAACD,OAAO,CAACY,UAAU,IAAI,CAACF,GAAG,IAAI,CAACD,OAAO,EAAE;IAClD,OAAOR,OAAO,CAACD,OAAO,CAACY,UAAU,CAAC,CAAC;EACrC;EAEAH,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,MAAMI,IAAI,GAAGJ,OAAO,CAACK,MAAM,IAAI,CAACL,OAAO,CAACM,GAAG,IAAIX,IAAI,CAACJ,OAAO,EAAE,CAAC,CAAC,CAAC;;EAGhEa,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;EAC/BA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;EAEjC,IAAIH,GAAG,EAAE;IACPC,MAAM,GAAGA,MAAM,IAAI,CAAC;IAEpB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC3BN,GAAG,CAACC,MAAM,GAAGK,CAAC,CAAC,GAAGH,IAAI,CAACG,CAAC,CAAC;IAC3B;IAEA,OAAON,GAAG;EACZ;EAEA,OAAO,CAAC,CAAC,EAAEL,UAAU,CAACY,eAAe,EAAEJ,IAAI,CAAC;AAC9C;AAEA,IAAIK,QAAQ,GAAGV,EAAE;AACjBV,OAAO,CAACE,OAAO,GAAGkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}