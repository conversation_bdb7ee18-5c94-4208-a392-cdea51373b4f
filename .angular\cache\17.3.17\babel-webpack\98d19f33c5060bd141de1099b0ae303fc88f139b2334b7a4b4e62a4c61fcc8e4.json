{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateUniqueInfo = exports.validateUnique = exports.shouldValidate = void 0;\nconst FieldError_1 = require(\"../../../error/FieldError\");\nconst util_1 = require(\"../util\");\nconst error_1 = require(\"../../../error\");\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!component.unique) {\n    return false;\n  }\n  if (!value || (0, util_1.isEmptyObject)(value)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateUnique = context => __awaiter(void 0, void 0, void 0, function* () {\n  var _a;\n  const {\n    value,\n    config,\n    component\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  if (!config || !config.database) {\n    throw new error_1.ProcessorError(\"Can't test for unique value without a database config object\", context, 'validate:validateUnique');\n  }\n  try {\n    const isUnique = yield (_a = config.database) === null || _a === void 0 ? void 0 : _a.isUnique(context, value);\n    if (typeof isUnique === 'string') {\n      return new FieldError_1.FieldError('unique', Object.assign(Object.assign({}, context), {\n        component: Object.assign(Object.assign({}, component), {\n          conflictId: isUnique\n        })\n      }));\n    }\n    return isUnique === true ? null : new FieldError_1.FieldError('unique', context);\n  } catch (err) {\n    throw new error_1.ProcessorError(err.message || err, context, 'validate:validateUnique');\n  }\n});\nexports.validateUnique = validateUnique;\nexports.validateUniqueInfo = {\n  name: 'validateUnique',\n  fullValue: true,\n  process: exports.validateUnique,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateUniqueInfo", "validateUnique", "shouldValidate", "FieldError_1", "require", "util_1", "error_1", "context", "component", "unique", "isEmptyObject", "_a", "config", "database", "ProcessorError", "isUnique", "FieldError", "assign", "conflictId", "err", "message", "name", "fullValue", "process", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateUnique.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateUniqueInfo = exports.validateUnique = exports.shouldValidate = void 0;\nconst FieldError_1 = require(\"../../../error/FieldError\");\nconst util_1 = require(\"../util\");\nconst error_1 = require(\"../../../error\");\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!component.unique) {\n        return false;\n    }\n    if (!value || (0, util_1.isEmptyObject)(value)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateUnique = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    const { value, config, component } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    if (!config || !config.database) {\n        throw new error_1.ProcessorError(\"Can't test for unique value without a database config object\", context, 'validate:validateUnique');\n    }\n    try {\n        const isUnique = yield ((_a = config.database) === null || _a === void 0 ? void 0 : _a.isUnique(context, value));\n        if (typeof isUnique === 'string') {\n            return new FieldError_1.FieldError('unique', Object.assign(Object.assign({}, context), { component: Object.assign(Object.assign({}, component), { conflictId: isUnique }) }));\n        }\n        return isUnique === true ? null : new FieldError_1.FieldError('unique', context);\n    }\n    catch (err) {\n        throw new error_1.ProcessorError(err.message || err, context, 'validate:validateUnique');\n    }\n});\nexports.validateUnique = validateUnique;\nexports.validateUniqueInfo = {\n    name: 'validateUnique',\n    fullValue: true,\n    process: exports.validateUnique,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,kBAAkB,GAAGD,OAAO,CAACE,cAAc,GAAGF,OAAO,CAACG,cAAc,GAAG,KAAK,CAAC;AACrF,MAAMC,YAAY,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACzD,MAAMC,MAAM,GAAGD,OAAO,CAAC,SAAS,CAAC;AACjC,MAAME,OAAO,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMF,cAAc,GAAIK,OAAO,IAAK;EAChC,MAAM;IAAEC,SAAS;IAAExB;EAAM,CAAC,GAAGuB,OAAO;EACpC,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE;IACnB,OAAO,KAAK;EAChB;EACA,IAAI,CAACzB,KAAK,IAAI,CAAC,CAAC,EAAEqB,MAAM,CAACK,aAAa,EAAE1B,KAAK,CAAC,EAAE;IAC5C,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDe,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvC,MAAMD,cAAc,GAAIM,OAAO,IAAK7B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC/E,IAAIiC,EAAE;EACN,MAAM;IAAE3B,KAAK;IAAE4B,MAAM;IAAEJ;EAAU,CAAC,GAAGD,OAAO;EAC5C,IAAI,CAAC,CAAC,CAAC,EAAER,OAAO,CAACG,cAAc,EAAEK,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAI,CAACK,MAAM,IAAI,CAACA,MAAM,CAACC,QAAQ,EAAE;IAC7B,MAAM,IAAIP,OAAO,CAACQ,cAAc,CAAC,8DAA8D,EAAEP,OAAO,EAAE,yBAAyB,CAAC;EACxI;EACA,IAAI;IACA,MAAMQ,QAAQ,GAAG,MAAO,CAACJ,EAAE,GAAGC,MAAM,CAACC,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,CAACR,OAAO,EAAEvB,KAAK,CAAE;IAChH,IAAI,OAAO+B,QAAQ,KAAK,QAAQ,EAAE;MAC9B,OAAO,IAAIZ,YAAY,CAACa,UAAU,CAAC,QAAQ,EAAEnB,MAAM,CAACoB,MAAM,CAACpB,MAAM,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAEV,OAAO,CAAC,EAAE;QAAEC,SAAS,EAAEX,MAAM,CAACoB,MAAM,CAACpB,MAAM,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAET,SAAS,CAAC,EAAE;UAAEU,UAAU,EAAEH;QAAS,CAAC;MAAE,CAAC,CAAC,CAAC;IACjL;IACA,OAAOA,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAIZ,YAAY,CAACa,UAAU,CAAC,QAAQ,EAAET,OAAO,CAAC;EACpF,CAAC,CACD,OAAOY,GAAG,EAAE;IACR,MAAM,IAAIb,OAAO,CAACQ,cAAc,CAACK,GAAG,CAACC,OAAO,IAAID,GAAG,EAAEZ,OAAO,EAAE,yBAAyB,CAAC;EAC5F;AACJ,CAAC,CAAC;AACFR,OAAO,CAACE,cAAc,GAAGA,cAAc;AACvCF,OAAO,CAACC,kBAAkB,GAAG;EACzBqB,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAExB,OAAO,CAACE,cAAc;EAC/BuB,aAAa,EAAEzB,OAAO,CAACG;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}