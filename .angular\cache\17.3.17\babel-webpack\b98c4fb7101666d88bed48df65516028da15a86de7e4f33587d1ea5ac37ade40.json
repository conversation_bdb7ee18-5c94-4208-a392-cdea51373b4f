{"ast": null, "code": "/*!\n * https://github.com/Starcounter-Jack/JSON-Patch\n * (c) 2017-2021 <PERSON>\n * MIT license\n */\nimport { _deepClone, _objectKeys, escapePathComponent, hasOwnProperty } from './helpers.mjs';\nimport { applyPatch } from './core.mjs';\nvar beforeDict = new WeakMap();\nvar Mirror = /** @class */function () {\n  function Mirror(obj) {\n    this.observers = new Map();\n    this.obj = obj;\n  }\n  return Mirror;\n}();\nvar ObserverInfo = /** @class */function () {\n  function ObserverInfo(callback, observer) {\n    this.callback = callback;\n    this.observer = observer;\n  }\n  return ObserverInfo;\n}();\nfunction getMirror(obj) {\n  return beforeDict.get(obj);\n}\nfunction getObserverFromMirror(mirror, callback) {\n  return mirror.observers.get(callback);\n}\nfunction removeObserverFromMirror(mirror, observer) {\n  mirror.observers.delete(observer.callback);\n}\n/**\n * Detach an observer from an object\n */\nexport function unobserve(root, observer) {\n  observer.unobserve();\n}\n/**\n * Observes changes made to an object, which can then be retrieved using generate\n */\nexport function observe(obj, callback) {\n  var patches = [];\n  var observer;\n  var mirror = getMirror(obj);\n  if (!mirror) {\n    mirror = new Mirror(obj);\n    beforeDict.set(obj, mirror);\n  } else {\n    var observerInfo = getObserverFromMirror(mirror, callback);\n    observer = observerInfo && observerInfo.observer;\n  }\n  if (observer) {\n    return observer;\n  }\n  observer = {};\n  mirror.value = _deepClone(obj);\n  if (callback) {\n    observer.callback = callback;\n    observer.next = null;\n    var dirtyCheck = function () {\n      generate(observer);\n    };\n    var fastCheck = function () {\n      clearTimeout(observer.next);\n      observer.next = setTimeout(dirtyCheck);\n    };\n    if (typeof window !== 'undefined') {\n      //not Node\n      window.addEventListener('mouseup', fastCheck);\n      window.addEventListener('keyup', fastCheck);\n      window.addEventListener('mousedown', fastCheck);\n      window.addEventListener('keydown', fastCheck);\n      window.addEventListener('change', fastCheck);\n    }\n  }\n  observer.patches = patches;\n  observer.object = obj;\n  observer.unobserve = function () {\n    generate(observer);\n    clearTimeout(observer.next);\n    removeObserverFromMirror(mirror, observer);\n    if (typeof window !== 'undefined') {\n      window.removeEventListener('mouseup', fastCheck);\n      window.removeEventListener('keyup', fastCheck);\n      window.removeEventListener('mousedown', fastCheck);\n      window.removeEventListener('keydown', fastCheck);\n      window.removeEventListener('change', fastCheck);\n    }\n  };\n  mirror.observers.set(callback, new ObserverInfo(callback, observer));\n  return observer;\n}\n/**\n * Generate an array of patches from an observer\n */\nexport function generate(observer, invertible) {\n  if (invertible === void 0) {\n    invertible = false;\n  }\n  var mirror = beforeDict.get(observer.object);\n  _generate(mirror.value, observer.object, observer.patches, \"\", invertible);\n  if (observer.patches.length) {\n    applyPatch(mirror.value, observer.patches);\n  }\n  var temp = observer.patches;\n  if (temp.length > 0) {\n    observer.patches = [];\n    if (observer.callback) {\n      observer.callback(temp);\n    }\n  }\n  return temp;\n}\n// Dirty check if obj is different from mirror, generate patches and update mirror\nfunction _generate(mirror, obj, patches, path, invertible) {\n  if (obj === mirror) {\n    return;\n  }\n  if (typeof obj.toJSON === \"function\") {\n    obj = obj.toJSON();\n  }\n  var newKeys = _objectKeys(obj);\n  var oldKeys = _objectKeys(mirror);\n  var changed = false;\n  var deleted = false;\n  //if ever \"move\" operation is implemented here, make sure this test runs OK: \"should not generate the same patch twice (move)\"\n  for (var t = oldKeys.length - 1; t >= 0; t--) {\n    var key = oldKeys[t];\n    var oldVal = mirror[key];\n    if (hasOwnProperty(obj, key) && !(obj[key] === undefined && oldVal !== undefined && Array.isArray(obj) === false)) {\n      var newVal = obj[key];\n      if (typeof oldVal == \"object\" && oldVal != null && typeof newVal == \"object\" && newVal != null && Array.isArray(oldVal) === Array.isArray(newVal)) {\n        _generate(oldVal, newVal, patches, path + \"/\" + escapePathComponent(key), invertible);\n      } else {\n        if (oldVal !== newVal) {\n          changed = true;\n          if (invertible) {\n            patches.push({\n              op: \"test\",\n              path: path + \"/\" + escapePathComponent(key),\n              value: _deepClone(oldVal)\n            });\n          }\n          patches.push({\n            op: \"replace\",\n            path: path + \"/\" + escapePathComponent(key),\n            value: _deepClone(newVal)\n          });\n        }\n      }\n    } else if (Array.isArray(mirror) === Array.isArray(obj)) {\n      if (invertible) {\n        patches.push({\n          op: \"test\",\n          path: path + \"/\" + escapePathComponent(key),\n          value: _deepClone(oldVal)\n        });\n      }\n      patches.push({\n        op: \"remove\",\n        path: path + \"/\" + escapePathComponent(key)\n      });\n      deleted = true; // property has been deleted\n    } else {\n      if (invertible) {\n        patches.push({\n          op: \"test\",\n          path: path,\n          value: mirror\n        });\n      }\n      patches.push({\n        op: \"replace\",\n        path: path,\n        value: obj\n      });\n      changed = true;\n    }\n  }\n  if (!deleted && newKeys.length == oldKeys.length) {\n    return;\n  }\n  for (var t = 0; t < newKeys.length; t++) {\n    var key = newKeys[t];\n    if (!hasOwnProperty(mirror, key) && obj[key] !== undefined) {\n      patches.push({\n        op: \"add\",\n        path: path + \"/\" + escapePathComponent(key),\n        value: _deepClone(obj[key])\n      });\n    }\n  }\n}\n/**\n * Create an array of patches from the differences in two objects\n */\nexport function compare(tree1, tree2, invertible) {\n  if (invertible === void 0) {\n    invertible = false;\n  }\n  var patches = [];\n  _generate(tree1, tree2, patches, '', invertible);\n  return patches;\n}", "map": {"version": 3, "names": ["_deepClone", "_objectKeys", "escapePathComponent", "hasOwnProperty", "applyPatch", "beforeDict", "WeakMap", "Mirror", "obj", "observers", "Map", "ObserverInfo", "callback", "observer", "getMirror", "get", "getObserverFromMirror", "mirror", "removeObserverFromMirror", "delete", "unobserve", "root", "observe", "patches", "set", "observerInfo", "value", "next", "<PERSON><PERSON><PERSON><PERSON>", "generate", "fastCheck", "clearTimeout", "setTimeout", "window", "addEventListener", "object", "removeEventListener", "invertible", "_generate", "length", "temp", "path", "toJSON", "newKeys", "oldKeys", "changed", "deleted", "t", "key", "oldVal", "undefined", "Array", "isArray", "newVal", "push", "op", "compare", "tree1", "tree2"], "sources": ["D:/workspace/formtest_aug/node_modules/fast-json-patch/module/duplex.mjs"], "sourcesContent": ["/*!\n * https://github.com/Starcounter-Jack/JSON-Patch\n * (c) 2017-2021 <PERSON>\n * MIT license\n */\nimport { _deepClone, _objectKeys, escapePathComponent, hasOwnProperty } from './helpers.mjs';\nimport { applyPatch } from './core.mjs';\nvar beforeDict = new WeakMap();\nvar Mirror = /** @class */ (function () {\n    function Mirror(obj) {\n        this.observers = new Map();\n        this.obj = obj;\n    }\n    return Mirror;\n}());\nvar ObserverInfo = /** @class */ (function () {\n    function ObserverInfo(callback, observer) {\n        this.callback = callback;\n        this.observer = observer;\n    }\n    return ObserverInfo;\n}());\nfunction getMirror(obj) {\n    return beforeDict.get(obj);\n}\nfunction getObserverFromMirror(mirror, callback) {\n    return mirror.observers.get(callback);\n}\nfunction removeObserverFromMirror(mirror, observer) {\n    mirror.observers.delete(observer.callback);\n}\n/**\n * Detach an observer from an object\n */\nexport function unobserve(root, observer) {\n    observer.unobserve();\n}\n/**\n * Observes changes made to an object, which can then be retrieved using generate\n */\nexport function observe(obj, callback) {\n    var patches = [];\n    var observer;\n    var mirror = getMirror(obj);\n    if (!mirror) {\n        mirror = new Mirror(obj);\n        beforeDict.set(obj, mirror);\n    }\n    else {\n        var observerInfo = getObserverFromMirror(mirror, callback);\n        observer = observerInfo && observerInfo.observer;\n    }\n    if (observer) {\n        return observer;\n    }\n    observer = {};\n    mirror.value = _deepClone(obj);\n    if (callback) {\n        observer.callback = callback;\n        observer.next = null;\n        var dirtyCheck = function () {\n            generate(observer);\n        };\n        var fastCheck = function () {\n            clearTimeout(observer.next);\n            observer.next = setTimeout(dirtyCheck);\n        };\n        if (typeof window !== 'undefined') { //not Node\n            window.addEventListener('mouseup', fastCheck);\n            window.addEventListener('keyup', fastCheck);\n            window.addEventListener('mousedown', fastCheck);\n            window.addEventListener('keydown', fastCheck);\n            window.addEventListener('change', fastCheck);\n        }\n    }\n    observer.patches = patches;\n    observer.object = obj;\n    observer.unobserve = function () {\n        generate(observer);\n        clearTimeout(observer.next);\n        removeObserverFromMirror(mirror, observer);\n        if (typeof window !== 'undefined') {\n            window.removeEventListener('mouseup', fastCheck);\n            window.removeEventListener('keyup', fastCheck);\n            window.removeEventListener('mousedown', fastCheck);\n            window.removeEventListener('keydown', fastCheck);\n            window.removeEventListener('change', fastCheck);\n        }\n    };\n    mirror.observers.set(callback, new ObserverInfo(callback, observer));\n    return observer;\n}\n/**\n * Generate an array of patches from an observer\n */\nexport function generate(observer, invertible) {\n    if (invertible === void 0) { invertible = false; }\n    var mirror = beforeDict.get(observer.object);\n    _generate(mirror.value, observer.object, observer.patches, \"\", invertible);\n    if (observer.patches.length) {\n        applyPatch(mirror.value, observer.patches);\n    }\n    var temp = observer.patches;\n    if (temp.length > 0) {\n        observer.patches = [];\n        if (observer.callback) {\n            observer.callback(temp);\n        }\n    }\n    return temp;\n}\n// Dirty check if obj is different from mirror, generate patches and update mirror\nfunction _generate(mirror, obj, patches, path, invertible) {\n    if (obj === mirror) {\n        return;\n    }\n    if (typeof obj.toJSON === \"function\") {\n        obj = obj.toJSON();\n    }\n    var newKeys = _objectKeys(obj);\n    var oldKeys = _objectKeys(mirror);\n    var changed = false;\n    var deleted = false;\n    //if ever \"move\" operation is implemented here, make sure this test runs OK: \"should not generate the same patch twice (move)\"\n    for (var t = oldKeys.length - 1; t >= 0; t--) {\n        var key = oldKeys[t];\n        var oldVal = mirror[key];\n        if (hasOwnProperty(obj, key) && !(obj[key] === undefined && oldVal !== undefined && Array.isArray(obj) === false)) {\n            var newVal = obj[key];\n            if (typeof oldVal == \"object\" && oldVal != null && typeof newVal == \"object\" && newVal != null && Array.isArray(oldVal) === Array.isArray(newVal)) {\n                _generate(oldVal, newVal, patches, path + \"/\" + escapePathComponent(key), invertible);\n            }\n            else {\n                if (oldVal !== newVal) {\n                    changed = true;\n                    if (invertible) {\n                        patches.push({ op: \"test\", path: path + \"/\" + escapePathComponent(key), value: _deepClone(oldVal) });\n                    }\n                    patches.push({ op: \"replace\", path: path + \"/\" + escapePathComponent(key), value: _deepClone(newVal) });\n                }\n            }\n        }\n        else if (Array.isArray(mirror) === Array.isArray(obj)) {\n            if (invertible) {\n                patches.push({ op: \"test\", path: path + \"/\" + escapePathComponent(key), value: _deepClone(oldVal) });\n            }\n            patches.push({ op: \"remove\", path: path + \"/\" + escapePathComponent(key) });\n            deleted = true; // property has been deleted\n        }\n        else {\n            if (invertible) {\n                patches.push({ op: \"test\", path: path, value: mirror });\n            }\n            patches.push({ op: \"replace\", path: path, value: obj });\n            changed = true;\n        }\n    }\n    if (!deleted && newKeys.length == oldKeys.length) {\n        return;\n    }\n    for (var t = 0; t < newKeys.length; t++) {\n        var key = newKeys[t];\n        if (!hasOwnProperty(mirror, key) && obj[key] !== undefined) {\n            patches.push({ op: \"add\", path: path + \"/\" + escapePathComponent(key), value: _deepClone(obj[key]) });\n        }\n    }\n}\n/**\n * Create an array of patches from the differences in two objects\n */\nexport function compare(tree1, tree2, invertible) {\n    if (invertible === void 0) { invertible = false; }\n    var patches = [];\n    _generate(tree1, tree2, patches, '', invertible);\n    return patches;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,cAAc,QAAQ,eAAe;AAC5F,SAASC,UAAU,QAAQ,YAAY;AACvC,IAAIC,UAAU,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC9B,IAAIC,MAAM,GAAG,aAAe,YAAY;EACpC,SAASA,MAAMA,CAACC,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACF,GAAG,GAAGA,GAAG;EAClB;EACA,OAAOD,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,IAAII,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IACtC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACA,OAAOF,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASG,SAASA,CAACN,GAAG,EAAE;EACpB,OAAOH,UAAU,CAACU,GAAG,CAACP,GAAG,CAAC;AAC9B;AACA,SAASQ,qBAAqBA,CAACC,MAAM,EAAEL,QAAQ,EAAE;EAC7C,OAAOK,MAAM,CAACR,SAAS,CAACM,GAAG,CAACH,QAAQ,CAAC;AACzC;AACA,SAASM,wBAAwBA,CAACD,MAAM,EAAEJ,QAAQ,EAAE;EAChDI,MAAM,CAACR,SAAS,CAACU,MAAM,CAACN,QAAQ,CAACD,QAAQ,CAAC;AAC9C;AACA;AACA;AACA;AACA,OAAO,SAASQ,SAASA,CAACC,IAAI,EAAER,QAAQ,EAAE;EACtCA,QAAQ,CAACO,SAAS,CAAC,CAAC;AACxB;AACA;AACA;AACA;AACA,OAAO,SAASE,OAAOA,CAACd,GAAG,EAAEI,QAAQ,EAAE;EACnC,IAAIW,OAAO,GAAG,EAAE;EAChB,IAAIV,QAAQ;EACZ,IAAII,MAAM,GAAGH,SAAS,CAACN,GAAG,CAAC;EAC3B,IAAI,CAACS,MAAM,EAAE;IACTA,MAAM,GAAG,IAAIV,MAAM,CAACC,GAAG,CAAC;IACxBH,UAAU,CAACmB,GAAG,CAAChB,GAAG,EAAES,MAAM,CAAC;EAC/B,CAAC,MACI;IACD,IAAIQ,YAAY,GAAGT,qBAAqB,CAACC,MAAM,EAAEL,QAAQ,CAAC;IAC1DC,QAAQ,GAAGY,YAAY,IAAIA,YAAY,CAACZ,QAAQ;EACpD;EACA,IAAIA,QAAQ,EAAE;IACV,OAAOA,QAAQ;EACnB;EACAA,QAAQ,GAAG,CAAC,CAAC;EACbI,MAAM,CAACS,KAAK,GAAG1B,UAAU,CAACQ,GAAG,CAAC;EAC9B,IAAII,QAAQ,EAAE;IACVC,QAAQ,CAACD,QAAQ,GAAGA,QAAQ;IAC5BC,QAAQ,CAACc,IAAI,GAAG,IAAI;IACpB,IAAIC,UAAU,GAAG,SAAAA,CAAA,EAAY;MACzBC,QAAQ,CAAChB,QAAQ,CAAC;IACtB,CAAC;IACD,IAAIiB,SAAS,GAAG,SAAAA,CAAA,EAAY;MACxBC,YAAY,CAAClB,QAAQ,CAACc,IAAI,CAAC;MAC3Bd,QAAQ,CAACc,IAAI,GAAGK,UAAU,CAACJ,UAAU,CAAC;IAC1C,CAAC;IACD,IAAI,OAAOK,MAAM,KAAK,WAAW,EAAE;MAAE;MACjCA,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,SAAS,CAAC;MAC7CG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEJ,SAAS,CAAC;MAC3CG,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAAEJ,SAAS,CAAC;MAC/CG,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,SAAS,CAAC;MAC7CG,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,SAAS,CAAC;IAChD;EACJ;EACAjB,QAAQ,CAACU,OAAO,GAAGA,OAAO;EAC1BV,QAAQ,CAACsB,MAAM,GAAG3B,GAAG;EACrBK,QAAQ,CAACO,SAAS,GAAG,YAAY;IAC7BS,QAAQ,CAAChB,QAAQ,CAAC;IAClBkB,YAAY,CAAClB,QAAQ,CAACc,IAAI,CAAC;IAC3BT,wBAAwB,CAACD,MAAM,EAAEJ,QAAQ,CAAC;IAC1C,IAAI,OAAOoB,MAAM,KAAK,WAAW,EAAE;MAC/BA,MAAM,CAACG,mBAAmB,CAAC,SAAS,EAAEN,SAAS,CAAC;MAChDG,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEN,SAAS,CAAC;MAC9CG,MAAM,CAACG,mBAAmB,CAAC,WAAW,EAAEN,SAAS,CAAC;MAClDG,MAAM,CAACG,mBAAmB,CAAC,SAAS,EAAEN,SAAS,CAAC;MAChDG,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEN,SAAS,CAAC;IACnD;EACJ,CAAC;EACDb,MAAM,CAACR,SAAS,CAACe,GAAG,CAACZ,QAAQ,EAAE,IAAID,YAAY,CAACC,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EACpE,OAAOA,QAAQ;AACnB;AACA;AACA;AACA;AACA,OAAO,SAASgB,QAAQA,CAAChB,QAAQ,EAAEwB,UAAU,EAAE;EAC3C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAG,KAAK;EAAE;EACjD,IAAIpB,MAAM,GAAGZ,UAAU,CAACU,GAAG,CAACF,QAAQ,CAACsB,MAAM,CAAC;EAC5CG,SAAS,CAACrB,MAAM,CAACS,KAAK,EAAEb,QAAQ,CAACsB,MAAM,EAAEtB,QAAQ,CAACU,OAAO,EAAE,EAAE,EAAEc,UAAU,CAAC;EAC1E,IAAIxB,QAAQ,CAACU,OAAO,CAACgB,MAAM,EAAE;IACzBnC,UAAU,CAACa,MAAM,CAACS,KAAK,EAAEb,QAAQ,CAACU,OAAO,CAAC;EAC9C;EACA,IAAIiB,IAAI,GAAG3B,QAAQ,CAACU,OAAO;EAC3B,IAAIiB,IAAI,CAACD,MAAM,GAAG,CAAC,EAAE;IACjB1B,QAAQ,CAACU,OAAO,GAAG,EAAE;IACrB,IAAIV,QAAQ,CAACD,QAAQ,EAAE;MACnBC,QAAQ,CAACD,QAAQ,CAAC4B,IAAI,CAAC;IAC3B;EACJ;EACA,OAAOA,IAAI;AACf;AACA;AACA,SAASF,SAASA,CAACrB,MAAM,EAAET,GAAG,EAAEe,OAAO,EAAEkB,IAAI,EAAEJ,UAAU,EAAE;EACvD,IAAI7B,GAAG,KAAKS,MAAM,EAAE;IAChB;EACJ;EACA,IAAI,OAAOT,GAAG,CAACkC,MAAM,KAAK,UAAU,EAAE;IAClClC,GAAG,GAAGA,GAAG,CAACkC,MAAM,CAAC,CAAC;EACtB;EACA,IAAIC,OAAO,GAAG1C,WAAW,CAACO,GAAG,CAAC;EAC9B,IAAIoC,OAAO,GAAG3C,WAAW,CAACgB,MAAM,CAAC;EACjC,IAAI4B,OAAO,GAAG,KAAK;EACnB,IAAIC,OAAO,GAAG,KAAK;EACnB;EACA,KAAK,IAAIC,CAAC,GAAGH,OAAO,CAACL,MAAM,GAAG,CAAC,EAAEQ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1C,IAAIC,GAAG,GAAGJ,OAAO,CAACG,CAAC,CAAC;IACpB,IAAIE,MAAM,GAAGhC,MAAM,CAAC+B,GAAG,CAAC;IACxB,IAAI7C,cAAc,CAACK,GAAG,EAAEwC,GAAG,CAAC,IAAI,EAAExC,GAAG,CAACwC,GAAG,CAAC,KAAKE,SAAS,IAAID,MAAM,KAAKC,SAAS,IAAIC,KAAK,CAACC,OAAO,CAAC5C,GAAG,CAAC,KAAK,KAAK,CAAC,EAAE;MAC/G,IAAI6C,MAAM,GAAG7C,GAAG,CAACwC,GAAG,CAAC;MACrB,IAAI,OAAOC,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAI,IAAI,IAAI,OAAOI,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAI,IAAI,IAAIF,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,KAAKE,KAAK,CAACC,OAAO,CAACC,MAAM,CAAC,EAAE;QAC/If,SAAS,CAACW,MAAM,EAAEI,MAAM,EAAE9B,OAAO,EAAEkB,IAAI,GAAG,GAAG,GAAGvC,mBAAmB,CAAC8C,GAAG,CAAC,EAAEX,UAAU,CAAC;MACzF,CAAC,MACI;QACD,IAAIY,MAAM,KAAKI,MAAM,EAAE;UACnBR,OAAO,GAAG,IAAI;UACd,IAAIR,UAAU,EAAE;YACZd,OAAO,CAAC+B,IAAI,CAAC;cAAEC,EAAE,EAAE,MAAM;cAAEd,IAAI,EAAEA,IAAI,GAAG,GAAG,GAAGvC,mBAAmB,CAAC8C,GAAG,CAAC;cAAEtB,KAAK,EAAE1B,UAAU,CAACiD,MAAM;YAAE,CAAC,CAAC;UACxG;UACA1B,OAAO,CAAC+B,IAAI,CAAC;YAAEC,EAAE,EAAE,SAAS;YAAEd,IAAI,EAAEA,IAAI,GAAG,GAAG,GAAGvC,mBAAmB,CAAC8C,GAAG,CAAC;YAAEtB,KAAK,EAAE1B,UAAU,CAACqD,MAAM;UAAE,CAAC,CAAC;QAC3G;MACJ;IACJ,CAAC,MACI,IAAIF,KAAK,CAACC,OAAO,CAACnC,MAAM,CAAC,KAAKkC,KAAK,CAACC,OAAO,CAAC5C,GAAG,CAAC,EAAE;MACnD,IAAI6B,UAAU,EAAE;QACZd,OAAO,CAAC+B,IAAI,CAAC;UAAEC,EAAE,EAAE,MAAM;UAAEd,IAAI,EAAEA,IAAI,GAAG,GAAG,GAAGvC,mBAAmB,CAAC8C,GAAG,CAAC;UAAEtB,KAAK,EAAE1B,UAAU,CAACiD,MAAM;QAAE,CAAC,CAAC;MACxG;MACA1B,OAAO,CAAC+B,IAAI,CAAC;QAAEC,EAAE,EAAE,QAAQ;QAAEd,IAAI,EAAEA,IAAI,GAAG,GAAG,GAAGvC,mBAAmB,CAAC8C,GAAG;MAAE,CAAC,CAAC;MAC3EF,OAAO,GAAG,IAAI,CAAC,CAAC;IACpB,CAAC,MACI;MACD,IAAIT,UAAU,EAAE;QACZd,OAAO,CAAC+B,IAAI,CAAC;UAAEC,EAAE,EAAE,MAAM;UAAEd,IAAI,EAAEA,IAAI;UAAEf,KAAK,EAAET;QAAO,CAAC,CAAC;MAC3D;MACAM,OAAO,CAAC+B,IAAI,CAAC;QAAEC,EAAE,EAAE,SAAS;QAAEd,IAAI,EAAEA,IAAI;QAAEf,KAAK,EAAElB;MAAI,CAAC,CAAC;MACvDqC,OAAO,GAAG,IAAI;IAClB;EACJ;EACA,IAAI,CAACC,OAAO,IAAIH,OAAO,CAACJ,MAAM,IAAIK,OAAO,CAACL,MAAM,EAAE;IAC9C;EACJ;EACA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACJ,MAAM,EAAEQ,CAAC,EAAE,EAAE;IACrC,IAAIC,GAAG,GAAGL,OAAO,CAACI,CAAC,CAAC;IACpB,IAAI,CAAC5C,cAAc,CAACc,MAAM,EAAE+B,GAAG,CAAC,IAAIxC,GAAG,CAACwC,GAAG,CAAC,KAAKE,SAAS,EAAE;MACxD3B,OAAO,CAAC+B,IAAI,CAAC;QAAEC,EAAE,EAAE,KAAK;QAAEd,IAAI,EAAEA,IAAI,GAAG,GAAG,GAAGvC,mBAAmB,CAAC8C,GAAG,CAAC;QAAEtB,KAAK,EAAE1B,UAAU,CAACQ,GAAG,CAACwC,GAAG,CAAC;MAAE,CAAC,CAAC;IACzG;EACJ;AACJ;AACA;AACA;AACA;AACA,OAAO,SAASQ,OAAOA,CAACC,KAAK,EAAEC,KAAK,EAAErB,UAAU,EAAE;EAC9C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IAAEA,UAAU,GAAG,KAAK;EAAE;EACjD,IAAId,OAAO,GAAG,EAAE;EAChBe,SAAS,CAACmB,KAAK,EAAEC,KAAK,EAAEnC,OAAO,EAAE,EAAE,EAAEc,UAAU,CAAC;EAChD,OAAOd,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}