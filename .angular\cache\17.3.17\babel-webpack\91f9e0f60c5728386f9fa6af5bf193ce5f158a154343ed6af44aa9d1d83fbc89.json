{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.doesArrayDataHaveValue = exports.hasValue = exports.interpolateErrors = exports.isObject = exports.isPromise = exports.toBoolean = exports.isEmptyObject = exports.isComponentProtected = exports.isComponentPersistent = void 0;\nconst utils_1 = require(\"../../utils\");\nconst i18n_1 = require(\"./i18n\");\nconst isEmpty_1 = __importDefault(require(\"lodash/isEmpty\"));\nconst isObject_1 = __importDefault(require(\"lodash/isObject\"));\nconst isPlainObject_1 = __importDefault(require(\"lodash/isPlainObject\"));\nfunction isComponentPersistent(component) {\n  return component.persistent ? component.persistent : true;\n}\nexports.isComponentPersistent = isComponentPersistent;\nfunction isComponentProtected(component) {\n  return component.protected ? component.protected : false;\n}\nexports.isComponentProtected = isComponentProtected;\nfunction isEmptyObject(obj) {\n  return !!obj && Object.keys(obj).length === 0 && obj.constructor === Object;\n}\nexports.isEmptyObject = isEmptyObject;\nfunction toBoolean(value) {\n  switch (typeof value) {\n    case 'string':\n      if (value === 'true' || value === '1') {\n        return true;\n      } else if (value === 'false' || value === '0') {\n        return false;\n      } else {\n        throw `Cannot coerce string ${value} to boolean}`;\n      }\n    case 'boolean':\n      return value;\n    default:\n      return !!value;\n  }\n}\nexports.toBoolean = toBoolean;\nfunction isPromise(value) {\n  return value && value.then && typeof value.then === 'function' && Object.prototype.toString.call(value) === '[object Promise]';\n}\nexports.isPromise = isPromise;\nfunction isObject(obj) {\n  return obj != null && (typeof obj === 'object' || typeof obj === 'function');\n}\nexports.isObject = isObject;\nconst getCustomErrorMessage = ({\n  errorKeyOrMessage,\n  context\n}) => {\n  var _a, _b;\n  return ((_b = (_a = context.component) === null || _a === void 0 ? void 0 : _a.errors) === null || _b === void 0 ? void 0 : _b[errorKeyOrMessage]) || '';\n};\n/**\n * Interpolates @formio/core errors so that they are compatible with the renderer\n * @param {FieldError[]} errors\n * @param firstPass\n * @returns {[]}\n */\nconst interpolateErrors = (errors, lang = 'en') => {\n  return errors.map(error => {\n    const {\n      errorKeyOrMessage,\n      context\n    } = error;\n    const i18n = i18n_1.VALIDATION_ERRORS[lang] || {};\n    const toInterpolate = getCustomErrorMessage(error) || i18n[errorKeyOrMessage] || errorKeyOrMessage;\n    const paths = [];\n    context.path.split('.').forEach(part => {\n      const match = part.match(/\\[([0-9]+)\\]$/);\n      if (match) {\n        paths.push(part.substring(0, match.index));\n        paths.push(parseInt(match[1]));\n      } else {\n        paths.push(part);\n      }\n    });\n    return {\n      message: (0, utils_1.unescapeHTML)(utils_1.Evaluator.interpolateString(toInterpolate, context)),\n      level: error.level,\n      path: paths,\n      context: {\n        validator: error.ruleName,\n        hasLabel: context.hasLabel,\n        key: context.component.key,\n        label: context.component.label || context.component.placeholder || context.component.key,\n        path: context.path,\n        value: context.value,\n        setting: context.setting,\n        index: context.index || 0\n      }\n    };\n  });\n};\nexports.interpolateErrors = interpolateErrors;\nconst hasValue = value => {\n  if ((0, isObject_1.default)(value)) {\n    return !(0, isEmpty_1.default)(value);\n  }\n  return typeof value === 'number' && !Number.isNaN(value) || !!value;\n};\nexports.hasValue = hasValue;\nconst doesArrayDataHaveValue = (dataValue = []) => {\n  if (!Array.isArray(dataValue)) {\n    return !!dataValue;\n  }\n  if (!dataValue.length) {\n    return false;\n  }\n  const isArrayDataComponent = dataValue.every(isPlainObject_1.default);\n  if (isArrayDataComponent) {\n    return dataValue.some(value => Object.values(value).some(exports.hasValue));\n  }\n  return dataValue.some(exports.hasValue);\n};\nexports.doesArrayDataHaveValue = doesArrayDataHaveValue;", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "doesArrayDataHaveValue", "hasValue", "interpolateErrors", "isObject", "isPromise", "toBoolean", "isEmptyObject", "isComponentProtected", "isComponentPersistent", "utils_1", "require", "i18n_1", "isEmpty_1", "isObject_1", "isPlainObject_1", "component", "persistent", "protected", "obj", "keys", "length", "constructor", "then", "prototype", "toString", "call", "getCustomErrorMessage", "errorKeyOrMessage", "context", "_a", "_b", "errors", "lang", "map", "error", "i18n", "VALIDATION_ERRORS", "toInterpolate", "paths", "path", "split", "for<PERSON>ach", "part", "match", "push", "substring", "index", "parseInt", "message", "unescapeHTML", "Evaluator", "interpolateString", "level", "validator", "ruleName", "<PERSON><PERSON><PERSON><PERSON>", "key", "label", "placeholder", "setting", "default", "Number", "isNaN", "dataValue", "Array", "isArray", "isArrayDataComponent", "every", "some", "values"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/util.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.doesArrayDataHaveValue = exports.hasValue = exports.interpolateErrors = exports.isObject = exports.isPromise = exports.toBoolean = exports.isEmptyObject = exports.isComponentProtected = exports.isComponentPersistent = void 0;\nconst utils_1 = require(\"../../utils\");\nconst i18n_1 = require(\"./i18n\");\nconst isEmpty_1 = __importDefault(require(\"lodash/isEmpty\"));\nconst isObject_1 = __importDefault(require(\"lodash/isObject\"));\nconst isPlainObject_1 = __importDefault(require(\"lodash/isPlainObject\"));\nfunction isComponentPersistent(component) {\n    return component.persistent ? component.persistent : true;\n}\nexports.isComponentPersistent = isComponentPersistent;\nfunction isComponentProtected(component) {\n    return component.protected ? component.protected : false;\n}\nexports.isComponentProtected = isComponentProtected;\nfunction isEmptyObject(obj) {\n    return !!obj && Object.keys(obj).length === 0 && obj.constructor === Object;\n}\nexports.isEmptyObject = isEmptyObject;\nfunction toBoolean(value) {\n    switch (typeof value) {\n        case 'string':\n            if (value === 'true' || value === '1') {\n                return true;\n            }\n            else if (value === 'false' || value === '0') {\n                return false;\n            }\n            else {\n                throw `Cannot coerce string ${value} to boolean}`;\n            }\n        case 'boolean':\n            return value;\n        default:\n            return !!value;\n    }\n}\nexports.toBoolean = toBoolean;\nfunction isPromise(value) {\n    return (value &&\n        value.then &&\n        typeof value.then === 'function' &&\n        Object.prototype.toString.call(value) === '[object Promise]');\n}\nexports.isPromise = isPromise;\nfunction isObject(obj) {\n    return obj != null && (typeof obj === 'object' || typeof obj === 'function');\n}\nexports.isObject = isObject;\nconst getCustomErrorMessage = ({ errorKeyOrMessage, context }) => { var _a, _b; return ((_b = (_a = context.component) === null || _a === void 0 ? void 0 : _a.errors) === null || _b === void 0 ? void 0 : _b[errorKeyOrMessage]) || ''; };\n/**\n * Interpolates @formio/core errors so that they are compatible with the renderer\n * @param {FieldError[]} errors\n * @param firstPass\n * @returns {[]}\n */\nconst interpolateErrors = (errors, lang = 'en') => {\n    return errors.map((error) => {\n        const { errorKeyOrMessage, context } = error;\n        const i18n = i18n_1.VALIDATION_ERRORS[lang] || {};\n        const toInterpolate = getCustomErrorMessage(error) || i18n[errorKeyOrMessage] || errorKeyOrMessage;\n        const paths = [];\n        context.path.split('.').forEach((part) => {\n            const match = part.match(/\\[([0-9]+)\\]$/);\n            if (match) {\n                paths.push(part.substring(0, match.index));\n                paths.push(parseInt(match[1]));\n            }\n            else {\n                paths.push(part);\n            }\n        });\n        return {\n            message: (0, utils_1.unescapeHTML)(utils_1.Evaluator.interpolateString(toInterpolate, context)),\n            level: error.level,\n            path: paths,\n            context: {\n                validator: error.ruleName,\n                hasLabel: context.hasLabel,\n                key: context.component.key,\n                label: context.component.label || context.component.placeholder || context.component.key,\n                path: context.path,\n                value: context.value,\n                setting: context.setting,\n                index: context.index || 0,\n            },\n        };\n    });\n};\nexports.interpolateErrors = interpolateErrors;\nconst hasValue = (value) => {\n    if ((0, isObject_1.default)(value)) {\n        return !(0, isEmpty_1.default)(value);\n    }\n    return (typeof value === 'number' && !Number.isNaN(value)) || !!value;\n};\nexports.hasValue = hasValue;\nconst doesArrayDataHaveValue = (dataValue = []) => {\n    if (!Array.isArray(dataValue)) {\n        return !!dataValue;\n    }\n    if (!dataValue.length) {\n        return false;\n    }\n    const isArrayDataComponent = dataValue.every(isPlainObject_1.default);\n    if (isArrayDataComponent) {\n        return dataValue.some((value) => Object.values(value).some(exports.hasValue));\n    }\n    return dataValue.some(exports.hasValue);\n};\nexports.doesArrayDataHaveValue = doesArrayDataHaveValue;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,sBAAsB,GAAGF,OAAO,CAACG,QAAQ,GAAGH,OAAO,CAACI,iBAAiB,GAAGJ,OAAO,CAACK,QAAQ,GAAGL,OAAO,CAACM,SAAS,GAAGN,OAAO,CAACO,SAAS,GAAGP,OAAO,CAACQ,aAAa,GAAGR,OAAO,CAACS,oBAAoB,GAAGT,OAAO,CAACU,qBAAqB,GAAG,KAAK,CAAC;AACxO,MAAMC,OAAO,GAAGC,OAAO,CAAC,aAAa,CAAC;AACtC,MAAMC,MAAM,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAME,SAAS,GAAGnB,eAAe,CAACiB,OAAO,CAAC,gBAAgB,CAAC,CAAC;AAC5D,MAAMG,UAAU,GAAGpB,eAAe,CAACiB,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC9D,MAAMI,eAAe,GAAGrB,eAAe,CAACiB,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACxE,SAASF,qBAAqBA,CAACO,SAAS,EAAE;EACtC,OAAOA,SAAS,CAACC,UAAU,GAAGD,SAAS,CAACC,UAAU,GAAG,IAAI;AAC7D;AACAlB,OAAO,CAACU,qBAAqB,GAAGA,qBAAqB;AACrD,SAASD,oBAAoBA,CAACQ,SAAS,EAAE;EACrC,OAAOA,SAAS,CAACE,SAAS,GAAGF,SAAS,CAACE,SAAS,GAAG,KAAK;AAC5D;AACAnB,OAAO,CAACS,oBAAoB,GAAGA,oBAAoB;AACnD,SAASD,aAAaA,CAACY,GAAG,EAAE;EACxB,OAAO,CAAC,CAACA,GAAG,IAAItB,MAAM,CAACuB,IAAI,CAACD,GAAG,CAAC,CAACE,MAAM,KAAK,CAAC,IAAIF,GAAG,CAACG,WAAW,KAAKzB,MAAM;AAC/E;AACAE,OAAO,CAACQ,aAAa,GAAGA,aAAa;AACrC,SAASD,SAASA,CAACN,KAAK,EAAE;EACtB,QAAQ,OAAOA,KAAK;IAChB,KAAK,QAAQ;MACT,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,GAAG,EAAE;QACnC,OAAO,IAAI;MACf,CAAC,MACI,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,GAAG,EAAE;QACzC,OAAO,KAAK;MAChB,CAAC,MACI;QACD,MAAM,wBAAwBA,KAAK,cAAc;MACrD;IACJ,KAAK,SAAS;MACV,OAAOA,KAAK;IAChB;MACI,OAAO,CAAC,CAACA,KAAK;EACtB;AACJ;AACAD,OAAO,CAACO,SAAS,GAAGA,SAAS;AAC7B,SAASD,SAASA,CAACL,KAAK,EAAE;EACtB,OAAQA,KAAK,IACTA,KAAK,CAACuB,IAAI,IACV,OAAOvB,KAAK,CAACuB,IAAI,KAAK,UAAU,IAChC1B,MAAM,CAAC2B,SAAS,CAACC,QAAQ,CAACC,IAAI,CAAC1B,KAAK,CAAC,KAAK,kBAAkB;AACpE;AACAD,OAAO,CAACM,SAAS,GAAGA,SAAS;AAC7B,SAASD,QAAQA,CAACe,GAAG,EAAE;EACnB,OAAOA,GAAG,IAAI,IAAI,KAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC;AAChF;AACApB,OAAO,CAACK,QAAQ,GAAGA,QAAQ;AAC3B,MAAMuB,qBAAqB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC;AAAQ,CAAC,KAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,OAAO,CAAC,CAACA,EAAE,GAAG,CAACD,EAAE,GAAGD,OAAO,CAACb,SAAS,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,MAAM,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACH,iBAAiB,CAAC,KAAK,EAAE;AAAE,CAAC;AAC3O;AACA;AACA;AACA;AACA;AACA;AACA,MAAMzB,iBAAiB,GAAGA,CAAC6B,MAAM,EAAEC,IAAI,GAAG,IAAI,KAAK;EAC/C,OAAOD,MAAM,CAACE,GAAG,CAAEC,KAAK,IAAK;IACzB,MAAM;MAAEP,iBAAiB;MAAEC;IAAQ,CAAC,GAAGM,KAAK;IAC5C,MAAMC,IAAI,GAAGxB,MAAM,CAACyB,iBAAiB,CAACJ,IAAI,CAAC,IAAI,CAAC,CAAC;IACjD,MAAMK,aAAa,GAAGX,qBAAqB,CAACQ,KAAK,CAAC,IAAIC,IAAI,CAACR,iBAAiB,CAAC,IAAIA,iBAAiB;IAClG,MAAMW,KAAK,GAAG,EAAE;IAChBV,OAAO,CAACW,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;MACtC,MAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,eAAe,CAAC;MACzC,IAAIA,KAAK,EAAE;QACPL,KAAK,CAACM,IAAI,CAACF,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,KAAK,CAACG,KAAK,CAAC,CAAC;QAC1CR,KAAK,CAACM,IAAI,CAACG,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,MACI;QACDL,KAAK,CAACM,IAAI,CAACF,IAAI,CAAC;MACpB;IACJ,CAAC,CAAC;IACF,OAAO;MACHM,OAAO,EAAE,CAAC,CAAC,EAAEvC,OAAO,CAACwC,YAAY,EAAExC,OAAO,CAACyC,SAAS,CAACC,iBAAiB,CAACd,aAAa,EAAET,OAAO,CAAC,CAAC;MAC/FwB,KAAK,EAAElB,KAAK,CAACkB,KAAK;MAClBb,IAAI,EAAED,KAAK;MACXV,OAAO,EAAE;QACLyB,SAAS,EAAEnB,KAAK,CAACoB,QAAQ;QACzBC,QAAQ,EAAE3B,OAAO,CAAC2B,QAAQ;QAC1BC,GAAG,EAAE5B,OAAO,CAACb,SAAS,CAACyC,GAAG;QAC1BC,KAAK,EAAE7B,OAAO,CAACb,SAAS,CAAC0C,KAAK,IAAI7B,OAAO,CAACb,SAAS,CAAC2C,WAAW,IAAI9B,OAAO,CAACb,SAAS,CAACyC,GAAG;QACxFjB,IAAI,EAAEX,OAAO,CAACW,IAAI;QAClBxC,KAAK,EAAE6B,OAAO,CAAC7B,KAAK;QACpB4D,OAAO,EAAE/B,OAAO,CAAC+B,OAAO;QACxBb,KAAK,EAAElB,OAAO,CAACkB,KAAK,IAAI;MAC5B;IACJ,CAAC;EACL,CAAC,CAAC;AACN,CAAC;AACDhD,OAAO,CAACI,iBAAiB,GAAGA,iBAAiB;AAC7C,MAAMD,QAAQ,GAAIF,KAAK,IAAK;EACxB,IAAI,CAAC,CAAC,EAAEc,UAAU,CAAC+C,OAAO,EAAE7D,KAAK,CAAC,EAAE;IAChC,OAAO,CAAC,CAAC,CAAC,EAAEa,SAAS,CAACgD,OAAO,EAAE7D,KAAK,CAAC;EACzC;EACA,OAAQ,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC8D,MAAM,CAACC,KAAK,CAAC/D,KAAK,CAAC,IAAK,CAAC,CAACA,KAAK;AACzE,CAAC;AACDD,OAAO,CAACG,QAAQ,GAAGA,QAAQ;AAC3B,MAAMD,sBAAsB,GAAGA,CAAC+D,SAAS,GAAG,EAAE,KAAK;EAC/C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;IAC3B,OAAO,CAAC,CAACA,SAAS;EACtB;EACA,IAAI,CAACA,SAAS,CAAC3C,MAAM,EAAE;IACnB,OAAO,KAAK;EAChB;EACA,MAAM8C,oBAAoB,GAAGH,SAAS,CAACI,KAAK,CAACrD,eAAe,CAAC8C,OAAO,CAAC;EACrE,IAAIM,oBAAoB,EAAE;IACtB,OAAOH,SAAS,CAACK,IAAI,CAAErE,KAAK,IAAKH,MAAM,CAACyE,MAAM,CAACtE,KAAK,CAAC,CAACqE,IAAI,CAACtE,OAAO,CAACG,QAAQ,CAAC,CAAC;EACjF;EACA,OAAO8D,SAAS,CAACK,IAAI,CAACtE,OAAO,CAACG,QAAQ,CAAC;AAC3C,CAAC;AACDH,OAAO,CAACE,sBAAsB,GAAGA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}