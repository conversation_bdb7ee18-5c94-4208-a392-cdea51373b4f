{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ProcessType = void 0;\nvar ProcessType;\n(function (ProcessType) {\n  ProcessType[\"Change\"] = \"change\";\n  ProcessType[\"Submit\"] = \"submit\";\n  ProcessType[\"Save\"] = \"save\";\n})(ProcessType || (exports.ProcessType = ProcessType = {}));", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "ProcessType"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/types/process/ProcessType.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ProcessType = void 0;\nvar ProcessType;\n(function (ProcessType) {\n    ProcessType[\"Change\"] = \"change\";\n    ProcessType[\"Submit\"] = \"submit\";\n    ProcessType[\"Save\"] = \"save\";\n})(ProcessType || (exports.ProcessType = ProcessType = {}));\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,WAAW,GAAG,KAAK,CAAC;AAC5B,IAAIA,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChCA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChCA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;AAChC,CAAC,EAAEA,WAAW,KAAKF,OAAO,CAACE,WAAW,GAAGA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}