{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateUrlSelectValueInfo = exports.validateUrlSelectValue = exports.shouldValidate = exports.generateUrl = void 0;\nconst error_1 = require(\"../../../error\");\nconst utils_1 = require(\"../../../utils\");\nconst util_1 = require(\"../util\");\nconst error_2 = require(\"../../../utils/error\");\nconst isValidatableSelectComponent = component => {\n  var _a;\n  return component && component.type === 'select' && (0, util_1.toBoolean)(component.dataSrc === 'url') && (0, util_1.toBoolean)((_a = component.validate) === null || _a === void 0 ? void 0 : _a.select);\n};\nconst generateUrl = (baseUrl, component, value) => {\n  const url = baseUrl;\n  const query = url.searchParams;\n  if (component.searchField) {\n    let searchValue = value;\n    if (component.valueProperty) {\n      searchValue = value[component.valueProperty];\n    } else {\n      searchValue = value;\n    }\n    query.set(component.searchField, typeof searchValue === 'string' ? searchValue : JSON.stringify(searchValue));\n  }\n  if (component.selectFields) {\n    query.set('select', component.selectFields);\n  }\n  if (component.sort) {\n    query.set('sort', component.sort);\n  }\n  if (component.filter) {\n    const filterQueryStrings = new URLSearchParams(component.filter);\n    filterQueryStrings.forEach((value, key) => query.set(key, value));\n  }\n  return url;\n};\nexports.generateUrl = generateUrl;\nconst shouldValidate = context => {\n  var _a;\n  const {\n    component,\n    value,\n    config\n  } = context;\n  // Only run this validation if server-side\n  if (!(config === null || config === void 0 ? void 0 : config.server)) {\n    return false;\n  }\n  if (!isValidatableSelectComponent(component)) {\n    return false;\n  }\n  if (!value || (0, util_1.isEmptyObject)(value) || Array.isArray(value) && value.length === 0) {\n    return false;\n  }\n  // If given an invalid configuration, do not validate the remote value\n  if (component.dataSrc !== 'url' || !((_a = component.data) === null || _a === void 0 ? void 0 : _a.url) || !component.searchField) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateUrlSelectValue = context => __awaiter(void 0, void 0, void 0, function* () {\n  const {\n    component,\n    value,\n    data,\n    config\n  } = context;\n  let _fetch = null;\n  try {\n    _fetch = context.fetch ? context.fetch : fetch;\n  } catch (ignoreError) {\n    _fetch = null;\n  }\n  try {\n    if (!_fetch) {\n      console.log('You must provide a fetch interface to the fetch processor.');\n      return null;\n    }\n    if (!(0, exports.shouldValidate)(context)) {\n      return null;\n    }\n    const baseUrl = new URL(utils_1.Evaluator ? utils_1.Evaluator.interpolate(component.data.url, data, {}) : component.data.url);\n    const url = (0, exports.generateUrl)(baseUrl, component, value);\n    const headers = component.data.headers ? component.data.headers.reduce((acc, header) => Object.assign(Object.assign({}, acc), {\n      [header.key]: header.value\n    }), {}) : {};\n    // Set form.io authentication\n    if (component.authenticate && config && config.tokens) {\n      Object.assign(headers, config.tokens);\n    }\n    try {\n      const response = yield _fetch(url.toString(), {\n        method: 'GET',\n        headers\n      });\n      // TODO: should we always expect JSON here?\n      if (response.ok) {\n        const data = yield response.json();\n        const error = new error_1.FieldError('select', context);\n        if (Array.isArray(data)) {\n          return data && data.length ? null : error;\n        }\n        return data ? (0, util_1.isEmptyObject)(data) ? error : null : error;\n      }\n      const data = yield response.text();\n      throw new error_1.ProcessorError(`Component with path ${component.key} returned an error while validating remote value: ${data}`, context, 'validate:validateRemoteSelectValue');\n    } catch (err) {\n      throw new error_1.ProcessorError(`Component with path ${component.key} returned an error while validating remote value: ${err}`, context, 'validate:validateRemoteSelectValue');\n    }\n  } catch (err) {\n    console.error((0, error_2.getErrorMessage)(err));\n    return null;\n  }\n});\nexports.validateUrlSelectValue = validateUrlSelectValue;\nexports.validateUrlSelectValueInfo = {\n  name: 'validateUrlSelectValue',\n  process: exports.validateUrlSelectValue,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateUrlSelectValueInfo", "validateUrlSelectValue", "shouldValidate", "generateUrl", "error_1", "require", "utils_1", "util_1", "error_2", "isValidatableSelectComponent", "component", "_a", "type", "toBoolean", "dataSrc", "validate", "select", "baseUrl", "url", "query", "searchParams", "searchField", "searchValue", "valueProperty", "set", "JSON", "stringify", "selectFields", "sort", "filter", "filterQueryStrings", "URLSearchParams", "for<PERSON>ach", "key", "context", "config", "server", "isEmptyObject", "Array", "isArray", "length", "data", "_fetch", "fetch", "ignoreError", "console", "log", "URL", "Evaluator", "interpolate", "headers", "reduce", "acc", "header", "assign", "authenticate", "tokens", "response", "toString", "method", "ok", "json", "error", "FieldError", "text", "ProcessorError", "err", "getErrorMessage", "name", "process", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateUrlSelectValue.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateUrlSelectValueInfo = exports.validateUrlSelectValue = exports.shouldValidate = exports.generateUrl = void 0;\nconst error_1 = require(\"../../../error\");\nconst utils_1 = require(\"../../../utils\");\nconst util_1 = require(\"../util\");\nconst error_2 = require(\"../../../utils/error\");\nconst isValidatableSelectComponent = (component) => {\n    var _a;\n    return (component &&\n        component.type === 'select' &&\n        (0, util_1.toBoolean)(component.dataSrc === 'url') &&\n        (0, util_1.toBoolean)((_a = component.validate) === null || _a === void 0 ? void 0 : _a.select));\n};\nconst generateUrl = (baseUrl, component, value) => {\n    const url = baseUrl;\n    const query = url.searchParams;\n    if (component.searchField) {\n        let searchValue = value;\n        if (component.valueProperty) {\n            searchValue = value[component.valueProperty];\n        }\n        else {\n            searchValue = value;\n        }\n        query.set(component.searchField, typeof searchValue === 'string' ? searchValue : JSON.stringify(searchValue));\n    }\n    if (component.selectFields) {\n        query.set('select', component.selectFields);\n    }\n    if (component.sort) {\n        query.set('sort', component.sort);\n    }\n    if (component.filter) {\n        const filterQueryStrings = new URLSearchParams(component.filter);\n        filterQueryStrings.forEach((value, key) => query.set(key, value));\n    }\n    return url;\n};\nexports.generateUrl = generateUrl;\nconst shouldValidate = (context) => {\n    var _a;\n    const { component, value, config } = context;\n    // Only run this validation if server-side\n    if (!(config === null || config === void 0 ? void 0 : config.server)) {\n        return false;\n    }\n    if (!isValidatableSelectComponent(component)) {\n        return false;\n    }\n    if (!value ||\n        (0, util_1.isEmptyObject)(value) ||\n        (Array.isArray(value) && value.length === 0)) {\n        return false;\n    }\n    // If given an invalid configuration, do not validate the remote value\n    if (component.dataSrc !== 'url' || !((_a = component.data) === null || _a === void 0 ? void 0 : _a.url) || !component.searchField) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateUrlSelectValue = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    const { component, value, data, config } = context;\n    let _fetch = null;\n    try {\n        _fetch = context.fetch ? context.fetch : fetch;\n    }\n    catch (ignoreError) {\n        _fetch = null;\n    }\n    try {\n        if (!_fetch) {\n            console.log('You must provide a fetch interface to the fetch processor.');\n            return null;\n        }\n        if (!(0, exports.shouldValidate)(context)) {\n            return null;\n        }\n        const baseUrl = new URL(utils_1.Evaluator\n            ? utils_1.Evaluator.interpolate(component.data.url, data, {})\n            : component.data.url);\n        const url = (0, exports.generateUrl)(baseUrl, component, value);\n        const headers = component.data.headers\n            ? component.data.headers.reduce((acc, header) => (Object.assign(Object.assign({}, acc), { [header.key]: header.value })), {})\n            : {};\n        // Set form.io authentication\n        if (component.authenticate && config && config.tokens) {\n            Object.assign(headers, config.tokens);\n        }\n        try {\n            const response = yield _fetch(url.toString(), { method: 'GET', headers });\n            // TODO: should we always expect JSON here?\n            if (response.ok) {\n                const data = yield response.json();\n                const error = new error_1.FieldError('select', context);\n                if (Array.isArray(data)) {\n                    return data && data.length ? null : error;\n                }\n                return data ? ((0, util_1.isEmptyObject)(data) ? error : null) : error;\n            }\n            const data = yield response.text();\n            throw new error_1.ProcessorError(`Component with path ${component.key} returned an error while validating remote value: ${data}`, context, 'validate:validateRemoteSelectValue');\n        }\n        catch (err) {\n            throw new error_1.ProcessorError(`Component with path ${component.key} returned an error while validating remote value: ${err}`, context, 'validate:validateRemoteSelectValue');\n        }\n    }\n    catch (err) {\n        console.error((0, error_2.getErrorMessage)(err));\n        return null;\n    }\n});\nexports.validateUrlSelectValue = validateUrlSelectValue;\nexports.validateUrlSelectValueInfo = {\n    name: 'validateUrlSelectValue',\n    process: exports.validateUrlSelectValue,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,0BAA0B,GAAGD,OAAO,CAACE,sBAAsB,GAAGF,OAAO,CAACG,cAAc,GAAGH,OAAO,CAACI,WAAW,GAAG,KAAK,CAAC;AAC3H,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,OAAO,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAME,MAAM,GAAGF,OAAO,CAAC,SAAS,CAAC;AACjC,MAAMG,OAAO,GAAGH,OAAO,CAAC,sBAAsB,CAAC;AAC/C,MAAMI,4BAA4B,GAAIC,SAAS,IAAK;EAChD,IAAIC,EAAE;EACN,OAAQD,SAAS,IACbA,SAAS,CAACE,IAAI,KAAK,QAAQ,IAC3B,CAAC,CAAC,EAAEL,MAAM,CAACM,SAAS,EAAEH,SAAS,CAACI,OAAO,KAAK,KAAK,CAAC,IAClD,CAAC,CAAC,EAAEP,MAAM,CAACM,SAAS,EAAE,CAACF,EAAE,GAAGD,SAAS,CAACK,QAAQ,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,MAAM,CAAC;AACvG,CAAC;AACD,MAAMb,WAAW,GAAGA,CAACc,OAAO,EAAEP,SAAS,EAAE1B,KAAK,KAAK;EAC/C,MAAMkC,GAAG,GAAGD,OAAO;EACnB,MAAME,KAAK,GAAGD,GAAG,CAACE,YAAY;EAC9B,IAAIV,SAAS,CAACW,WAAW,EAAE;IACvB,IAAIC,WAAW,GAAGtC,KAAK;IACvB,IAAI0B,SAAS,CAACa,aAAa,EAAE;MACzBD,WAAW,GAAGtC,KAAK,CAAC0B,SAAS,CAACa,aAAa,CAAC;IAChD,CAAC,MACI;MACDD,WAAW,GAAGtC,KAAK;IACvB;IACAmC,KAAK,CAACK,GAAG,CAACd,SAAS,CAACW,WAAW,EAAE,OAAOC,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGG,IAAI,CAACC,SAAS,CAACJ,WAAW,CAAC,CAAC;EACjH;EACA,IAAIZ,SAAS,CAACiB,YAAY,EAAE;IACxBR,KAAK,CAACK,GAAG,CAAC,QAAQ,EAAEd,SAAS,CAACiB,YAAY,CAAC;EAC/C;EACA,IAAIjB,SAAS,CAACkB,IAAI,EAAE;IAChBT,KAAK,CAACK,GAAG,CAAC,MAAM,EAAEd,SAAS,CAACkB,IAAI,CAAC;EACrC;EACA,IAAIlB,SAAS,CAACmB,MAAM,EAAE;IAClB,MAAMC,kBAAkB,GAAG,IAAIC,eAAe,CAACrB,SAAS,CAACmB,MAAM,CAAC;IAChEC,kBAAkB,CAACE,OAAO,CAAC,CAAChD,KAAK,EAAEiD,GAAG,KAAKd,KAAK,CAACK,GAAG,CAACS,GAAG,EAAEjD,KAAK,CAAC,CAAC;EACrE;EACA,OAAOkC,GAAG;AACd,CAAC;AACDnB,OAAO,CAACI,WAAW,GAAGA,WAAW;AACjC,MAAMD,cAAc,GAAIgC,OAAO,IAAK;EAChC,IAAIvB,EAAE;EACN,MAAM;IAAED,SAAS;IAAE1B,KAAK;IAAEmD;EAAO,CAAC,GAAGD,OAAO;EAC5C;EACA,IAAI,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,MAAM,CAAC,EAAE;IAClE,OAAO,KAAK;EAChB;EACA,IAAI,CAAC3B,4BAA4B,CAACC,SAAS,CAAC,EAAE;IAC1C,OAAO,KAAK;EAChB;EACA,IAAI,CAAC1B,KAAK,IACN,CAAC,CAAC,EAAEuB,MAAM,CAAC8B,aAAa,EAAErD,KAAK,CAAC,IAC/BsD,KAAK,CAACC,OAAO,CAACvD,KAAK,CAAC,IAAIA,KAAK,CAACwD,MAAM,KAAK,CAAE,EAAE;IAC9C,OAAO,KAAK;EAChB;EACA;EACA,IAAI9B,SAAS,CAACI,OAAO,KAAK,KAAK,IAAI,EAAE,CAACH,EAAE,GAAGD,SAAS,CAAC+B,IAAI,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,GAAG,CAAC,IAAI,CAACR,SAAS,CAACW,WAAW,EAAE;IAC/H,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDtB,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvC,MAAMD,sBAAsB,GAAIiC,OAAO,IAAKxD,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACvF,MAAM;IAAEgC,SAAS;IAAE1B,KAAK;IAAEyD,IAAI;IAAEN;EAAO,CAAC,GAAGD,OAAO;EAClD,IAAIQ,MAAM,GAAG,IAAI;EACjB,IAAI;IACAA,MAAM,GAAGR,OAAO,CAACS,KAAK,GAAGT,OAAO,CAACS,KAAK,GAAGA,KAAK;EAClD,CAAC,CACD,OAAOC,WAAW,EAAE;IAChBF,MAAM,GAAG,IAAI;EACjB;EACA,IAAI;IACA,IAAI,CAACA,MAAM,EAAE;MACTG,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,OAAO,IAAI;IACf;IACA,IAAI,CAAC,CAAC,CAAC,EAAE/C,OAAO,CAACG,cAAc,EAAEgC,OAAO,CAAC,EAAE;MACvC,OAAO,IAAI;IACf;IACA,MAAMjB,OAAO,GAAG,IAAI8B,GAAG,CAACzC,OAAO,CAAC0C,SAAS,GACnC1C,OAAO,CAAC0C,SAAS,CAACC,WAAW,CAACvC,SAAS,CAAC+B,IAAI,CAACvB,GAAG,EAAEuB,IAAI,EAAE,CAAC,CAAC,CAAC,GAC3D/B,SAAS,CAAC+B,IAAI,CAACvB,GAAG,CAAC;IACzB,MAAMA,GAAG,GAAG,CAAC,CAAC,EAAEnB,OAAO,CAACI,WAAW,EAAEc,OAAO,EAAEP,SAAS,EAAE1B,KAAK,CAAC;IAC/D,MAAMkE,OAAO,GAAGxC,SAAS,CAAC+B,IAAI,CAACS,OAAO,GAChCxC,SAAS,CAAC+B,IAAI,CAACS,OAAO,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAMxD,MAAM,CAACyD,MAAM,CAACzD,MAAM,CAACyD,MAAM,CAAC,CAAC,CAAC,EAAEF,GAAG,CAAC,EAAE;MAAE,CAACC,MAAM,CAACpB,GAAG,GAAGoB,MAAM,CAACrE;IAAM,CAAC,CAAE,EAAE,CAAC,CAAC,CAAC,GAC3H,CAAC,CAAC;IACR;IACA,IAAI0B,SAAS,CAAC6C,YAAY,IAAIpB,MAAM,IAAIA,MAAM,CAACqB,MAAM,EAAE;MACnD3D,MAAM,CAACyD,MAAM,CAACJ,OAAO,EAAEf,MAAM,CAACqB,MAAM,CAAC;IACzC;IACA,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMf,MAAM,CAACxB,GAAG,CAACwC,QAAQ,CAAC,CAAC,EAAE;QAAEC,MAAM,EAAE,KAAK;QAAET;MAAQ,CAAC,CAAC;MACzE;MACA,IAAIO,QAAQ,CAACG,EAAE,EAAE;QACb,MAAMnB,IAAI,GAAG,MAAMgB,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC,MAAMC,KAAK,GAAG,IAAI1D,OAAO,CAAC2D,UAAU,CAAC,QAAQ,EAAE7B,OAAO,CAAC;QACvD,IAAII,KAAK,CAACC,OAAO,CAACE,IAAI,CAAC,EAAE;UACrB,OAAOA,IAAI,IAAIA,IAAI,CAACD,MAAM,GAAG,IAAI,GAAGsB,KAAK;QAC7C;QACA,OAAOrB,IAAI,GAAI,CAAC,CAAC,EAAElC,MAAM,CAAC8B,aAAa,EAAEI,IAAI,CAAC,GAAGqB,KAAK,GAAG,IAAI,GAAIA,KAAK;MAC1E;MACA,MAAMrB,IAAI,GAAG,MAAMgB,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,MAAM,IAAI5D,OAAO,CAAC6D,cAAc,CAAC,uBAAuBvD,SAAS,CAACuB,GAAG,qDAAqDQ,IAAI,EAAE,EAAEP,OAAO,EAAE,oCAAoC,CAAC;IACpL,CAAC,CACD,OAAOgC,GAAG,EAAE;MACR,MAAM,IAAI9D,OAAO,CAAC6D,cAAc,CAAC,uBAAuBvD,SAAS,CAACuB,GAAG,qDAAqDiC,GAAG,EAAE,EAAEhC,OAAO,EAAE,oCAAoC,CAAC;IACnL;EACJ,CAAC,CACD,OAAOgC,GAAG,EAAE;IACRrB,OAAO,CAACiB,KAAK,CAAC,CAAC,CAAC,EAAEtD,OAAO,CAAC2D,eAAe,EAAED,GAAG,CAAC,CAAC;IAChD,OAAO,IAAI;EACf;AACJ,CAAC,CAAC;AACFnE,OAAO,CAACE,sBAAsB,GAAGA,sBAAsB;AACvDF,OAAO,CAACC,0BAA0B,GAAG;EACjCoE,IAAI,EAAE,wBAAwB;EAC9BC,OAAO,EAAEtE,OAAO,CAACE,sBAAsB;EACvCqE,aAAa,EAAEvE,OAAO,CAACG;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}