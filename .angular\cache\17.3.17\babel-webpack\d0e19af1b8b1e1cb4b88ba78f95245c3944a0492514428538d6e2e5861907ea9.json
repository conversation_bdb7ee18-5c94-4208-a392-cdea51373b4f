{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst lodash_1 = require(\"lodash\");\nclass ConditionOperator {\n  static get operatorKey() {\n    return '';\n  }\n  static get displayedName() {\n    return '';\n  }\n  static get requireValue() {\n    return true;\n  }\n  execute() {\n    return true;\n  }\n  getResult(options = {}) {\n    const {\n      value\n    } = options;\n    if ((0, lodash_1.isArray)(value)) {\n      return (0, lodash_1.some)(value, valueItem => this.execute(Object.assign(Object.assign({}, options), {\n        value: valueItem\n      })));\n    }\n    return this.execute(options);\n  }\n}\nexports.default = ConditionOperator;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "lodash_1", "require", "ConditionOperator", "operatorKey", "displayedName", "requireValue", "execute", "getResult", "options", "isArray", "some", "valueItem", "assign", "default"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/utils/operators/ConditionOperator.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst lodash_1 = require(\"lodash\");\nclass ConditionOperator {\n    static get operatorKey() {\n        return '';\n    }\n    static get displayedName() {\n        return '';\n    }\n    static get requireValue() {\n        return true;\n    }\n    execute() {\n        return true;\n    }\n    getResult(options = {}) {\n        const { value } = options;\n        if ((0, lodash_1.isArray)(value)) {\n            return (0, lodash_1.some)(value, (valueItem) => this.execute(Object.assign(Object.assign({}, options), { value: valueItem })));\n        }\n        return this.execute(options);\n    }\n}\nexports.default = ConditionOperator;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,iBAAiB,CAAC;EACpB,WAAWC,WAAWA,CAAA,EAAG;IACrB,OAAO,EAAE;EACb;EACA,WAAWC,aAAaA,CAAA,EAAG;IACvB,OAAO,EAAE;EACb;EACA,WAAWC,YAAYA,CAAA,EAAG;IACtB,OAAO,IAAI;EACf;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI;EACf;EACAC,SAASA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpB,MAAM;MAAET;IAAM,CAAC,GAAGS,OAAO;IACzB,IAAI,CAAC,CAAC,EAAER,QAAQ,CAACS,OAAO,EAAEV,KAAK,CAAC,EAAE;MAC9B,OAAO,CAAC,CAAC,EAAEC,QAAQ,CAACU,IAAI,EAAEX,KAAK,EAAGY,SAAS,IAAK,IAAI,CAACL,OAAO,CAACV,MAAM,CAACgB,MAAM,CAAChB,MAAM,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAE;QAAET,KAAK,EAAEY;MAAU,CAAC,CAAC,CAAC,CAAC;IAClI;IACA,OAAO,IAAI,CAACL,OAAO,CAACE,OAAO,CAAC;EAChC;AACJ;AACAV,OAAO,CAACe,OAAO,GAAGX,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}