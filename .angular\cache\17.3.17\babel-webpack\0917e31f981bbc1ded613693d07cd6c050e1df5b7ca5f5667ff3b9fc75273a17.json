{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.logicProcessInfo = exports.logicProcess = exports.logicProcessSync = void 0;\nconst logic_1 = require(\"../../utils/logic\");\n// This processor ensures that a \"linked\" row context is provided to every component.\nconst logicProcessSync = context => {\n  return (0, logic_1.applyActions)(context);\n};\nexports.logicProcessSync = logicProcessSync;\nconst logicProcess = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.logicProcessSync)(context);\n});\nexports.logicProcess = logicProcess;\nexports.logicProcessInfo = {\n  name: 'logic',\n  process: exports.logicProcess,\n  processSync: exports.logicProcessSync,\n  shouldProcess: logic_1.hasLogic\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "logicProcessInfo", "logicProcess", "logicProcessSync", "logic_1", "require", "context", "applyActions", "name", "process", "processSync", "shouldProcess", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/logic/index.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.logicProcessInfo = exports.logicProcess = exports.logicProcessSync = void 0;\nconst logic_1 = require(\"../../utils/logic\");\n// This processor ensures that a \"linked\" row context is provided to every component.\nconst logicProcessSync = (context) => {\n    return (0, logic_1.applyActions)(context);\n};\nexports.logicProcessSync = logicProcessSync;\nconst logicProcess = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.logicProcessSync)(context);\n});\nexports.logicProcess = logicProcess;\nexports.logicProcessInfo = {\n    name: 'logic',\n    process: exports.logicProcess,\n    processSync: exports.logicProcessSync,\n    shouldProcess: logic_1.hasLogic,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,gBAAgB,GAAGD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,gBAAgB,GAAG,KAAK,CAAC;AACnF,MAAMC,OAAO,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAC5C;AACA,MAAMF,gBAAgB,GAAIG,OAAO,IAAK;EAClC,OAAO,CAAC,CAAC,EAAEF,OAAO,CAACG,YAAY,EAAED,OAAO,CAAC;AAC7C,CAAC;AACDN,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMD,YAAY,GAAII,OAAO,IAAK3B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC7E,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACG,gBAAgB,EAAEG,OAAO,CAAC;AACjD,CAAC,CAAC;AACFN,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnCF,OAAO,CAACC,gBAAgB,GAAG;EACvBO,IAAI,EAAE,OAAO;EACbC,OAAO,EAAET,OAAO,CAACE,YAAY;EAC7BQ,WAAW,EAAEV,OAAO,CAACG,gBAAgB;EACrCQ,aAAa,EAAEP,OAAO,CAACQ;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}