(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.pa = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Punjabi = {
      weekdays: {
          shorthand: ["ਐਤ", "ਸੋਮ", "ਮੰਗਲ", "ਬੁੱਧ", "ਵੀਰ", "ਸ਼ੁੱਕਰ", "ਸ਼ਨਿੱਚਰ"],
          longhand: [
              "ਐਤਵਾਰ",
              "ਸੋਮਵਾਰ",
              "ਮੰਗਲਵਾਰ",
              "ਬੁੱਧਵਾਰ",
              "ਵੀਰਵਾਰ",
              "ਸ਼ੁੱਕਰਵਾਰ",
              "ਸ਼ਨਿੱਚਰਵਾਰ",
          ],
      },
      months: {
          shorthand: [
              "ਜਨ",
              "ਫ਼ਰ",
              "ਮਾਰ",
              "ਅਪ੍ਰੈ",
              "ਮਈ",
              "ਜੂਨ",
              "ਜੁਲਾ",
              "ਅਗ",
              "ਸਤੰ",
              "ਅਕ",
              "ਨਵੰ",
              "ਦਸੰ",
          ],
          longhand: [
              "ਜਨਵਰੀ",
              "ਫ਼ਰਵਰੀ",
              "ਮਾਰਚ",
              "ਅਪ੍ਰੈਲ",
              "ਮਈ",
              "ਜੂਨ",
              "ਜੁਲਾਈ",
              "ਅਗਸਤ",
              "ਸਤੰਬਰ",
              "ਅਕਤੂਬਰ",
              "ਨਵੰਬਰ",
              "ਦਸੰਬਰ",
          ],
      },
      time_24hr: true,
  };
  fp.l10ns.pa = Punjabi;
  var pa = fp.l10ns;

  exports.Punjabi = Punjabi;
  exports.default = pa;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
