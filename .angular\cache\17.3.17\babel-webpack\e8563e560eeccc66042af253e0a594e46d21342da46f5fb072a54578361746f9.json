{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateRegexPatternInfo = exports.validateRegexPatternSync = exports.validateRegexPattern = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableTextFieldComponent = component => {\n  var _a;\n  return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('pattern'));\n};\nconst shouldValidate = context => {\n  var _a;\n  const {\n    component,\n    value\n  } = context;\n  if (!isValidatableTextFieldComponent(component) || !value) {\n    return false;\n  }\n  const pattern = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.pattern;\n  if (!pattern || !value || typeof value !== 'string') {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateRegexPattern = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateRegexPatternSync)(context);\n});\nexports.validateRegexPattern = validateRegexPattern;\nconst validateRegexPatternSync = context => {\n  var _a, _b;\n  const {\n    component,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context) || !isValidatableTextFieldComponent(component)) {\n    return null;\n  }\n  const pattern = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.pattern;\n  const regex = new RegExp(`^${pattern}$`);\n  return typeof value === 'string' && regex.test(value) ? null : new error_1.FieldError(((_b = component.validate) === null || _b === void 0 ? void 0 : _b.patternMessage) || 'pattern', Object.assign(Object.assign({}, context), {\n    regex: pattern,\n    pattern: pattern,\n    setting: pattern\n  }), 'pattern');\n};\nexports.validateRegexPatternSync = validateRegexPatternSync;\nexports.validateRegexPatternInfo = {\n  name: 'validateRegexPattern',\n  process: exports.validateRegexPattern,\n  processSync: exports.validateRegexPatternSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateRegexPatternInfo", "validateRegexPatternSync", "validateRegexPattern", "shouldValidate", "error_1", "require", "isValidatableTextFieldComponent", "component", "_a", "validate", "hasOwnProperty", "context", "pattern", "_b", "regex", "RegExp", "test", "FieldError", "patternMessage", "assign", "setting", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateRegexPattern.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateRegexPatternInfo = exports.validateRegexPatternSync = exports.validateRegexPattern = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableTextFieldComponent = (component) => {\n    var _a;\n    return component && ((_a = component.validate) === null || _a === void 0 ? void 0 : _a.hasOwnProperty('pattern'));\n};\nconst shouldValidate = (context) => {\n    var _a;\n    const { component, value } = context;\n    if (!isValidatableTextFieldComponent(component) || !value) {\n        return false;\n    }\n    const pattern = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.pattern;\n    if (!pattern || !value || typeof value !== 'string') {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateRegexPattern = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateRegexPatternSync)(context);\n});\nexports.validateRegexPattern = validateRegexPattern;\nconst validateRegexPatternSync = (context) => {\n    var _a, _b;\n    const { component, value } = context;\n    if (!(0, exports.shouldValidate)(context) || !isValidatableTextFieldComponent(component)) {\n        return null;\n    }\n    const pattern = (_a = component.validate) === null || _a === void 0 ? void 0 : _a.pattern;\n    const regex = new RegExp(`^${pattern}$`);\n    return typeof value === 'string' && regex.test(value)\n        ? null\n        : new error_1.FieldError(((_b = component.validate) === null || _b === void 0 ? void 0 : _b.patternMessage) || 'pattern', Object.assign(Object.assign({}, context), { regex: pattern, pattern: pattern, setting: pattern }), 'pattern');\n};\nexports.validateRegexPatternSync = validateRegexPatternSync;\nexports.validateRegexPatternInfo = {\n    name: 'validateRegexPattern',\n    process: exports.validateRegexPattern,\n    processSync: exports.validateRegexPatternSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,wBAAwB,GAAGD,OAAO,CAACE,wBAAwB,GAAGF,OAAO,CAACG,oBAAoB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AACpI,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,+BAA+B,GAAIC,SAAS,IAAK;EACnD,IAAIC,EAAE;EACN,OAAOD,SAAS,KAAK,CAACC,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,cAAc,CAAC,SAAS,CAAC,CAAC;AACrH,CAAC;AACD,MAAMP,cAAc,GAAIQ,OAAO,IAAK;EAChC,IAAIH,EAAE;EACN,MAAM;IAAED,SAAS;IAAEvB;EAAM,CAAC,GAAG2B,OAAO;EACpC,IAAI,CAACL,+BAA+B,CAACC,SAAS,CAAC,IAAI,CAACvB,KAAK,EAAE;IACvD,OAAO,KAAK;EAChB;EACA,MAAM4B,OAAO,GAAG,CAACJ,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,OAAO;EACzF,IAAI,CAACA,OAAO,IAAI,CAAC5B,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACjD,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDe,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,oBAAoB,GAAIS,OAAO,IAAKjC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACrF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,wBAAwB,EAAEU,OAAO,CAAC;AACzD,CAAC,CAAC;AACFZ,OAAO,CAACG,oBAAoB,GAAGA,oBAAoB;AACnD,MAAMD,wBAAwB,GAAIU,OAAO,IAAK;EAC1C,IAAIH,EAAE,EAAEK,EAAE;EACV,MAAM;IAAEN,SAAS;IAAEvB;EAAM,CAAC,GAAG2B,OAAO;EACpC,IAAI,CAAC,CAAC,CAAC,EAAEZ,OAAO,CAACI,cAAc,EAAEQ,OAAO,CAAC,IAAI,CAACL,+BAA+B,CAACC,SAAS,CAAC,EAAE;IACtF,OAAO,IAAI;EACf;EACA,MAAMK,OAAO,GAAG,CAACJ,EAAE,GAAGD,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,OAAO;EACzF,MAAME,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIH,OAAO,GAAG,CAAC;EACxC,OAAO,OAAO5B,KAAK,KAAK,QAAQ,IAAI8B,KAAK,CAACE,IAAI,CAAChC,KAAK,CAAC,GAC/C,IAAI,GACJ,IAAIoB,OAAO,CAACa,UAAU,CAAC,CAAC,CAACJ,EAAE,GAAGN,SAAS,CAACE,QAAQ,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,cAAc,KAAK,SAAS,EAAErB,MAAM,CAACsB,MAAM,CAACtB,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,EAAER,OAAO,CAAC,EAAE;IAAEG,KAAK,EAAEF,OAAO;IAAEA,OAAO,EAAEA,OAAO;IAAEQ,OAAO,EAAER;EAAQ,CAAC,CAAC,EAAE,SAAS,CAAC;AAC/O,CAAC;AACDb,OAAO,CAACE,wBAAwB,GAAGA,wBAAwB;AAC3DF,OAAO,CAACC,wBAAwB,GAAG;EAC/BqB,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,EAAEvB,OAAO,CAACG,oBAAoB;EACrCqB,WAAW,EAAExB,OAAO,CAACE,wBAAwB;EAC7CuB,aAAa,EAAEzB,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}