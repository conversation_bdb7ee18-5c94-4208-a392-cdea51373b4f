{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateDateInfo = exports.validateDateSync = exports.validateDate = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableDateTimeComponent = obj => {\n  return !!obj && !!obj.type && obj.type === 'datetime';\n};\nconst isValidatable = component => {\n  return isValidatableDateTimeComponent(component);\n};\nconst shouldValidate = context => {\n  const {\n    component,\n    value\n  } = context;\n  if (!value || !isValidatable(component)) {\n    return false;\n  }\n  if (component.multiple && Array.isArray(value) && value.length === 0) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateDate = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateDateSync)(context);\n});\nexports.validateDate = validateDate;\nconst validateDateSync = context => {\n  const error = new error_1.FieldError('invalidDate', context, 'date');\n  const {\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  // TODO: is this right?\n  if (typeof value === 'string') {\n    if (value.toLowerCase() === 'invalid date') {\n      return error;\n    }\n    if (new Date(value).toString() === 'Invalid Date') {\n      return error;\n    }\n    return null;\n  } else if (value instanceof Date) {\n    return value.toString() !== 'Invalid Date' ? null : error;\n  }\n  return error;\n};\nexports.validateDateSync = validateDateSync;\nexports.validateDateInfo = {\n  name: 'validateDate',\n  process: exports.validateDate,\n  processSync: exports.validateDateSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateDateInfo", "validateDateSync", "validateDate", "shouldValidate", "error_1", "require", "isValidatableDateTimeComponent", "obj", "type", "isValidatable", "component", "context", "multiple", "Array", "isArray", "length", "error", "FieldError", "toLowerCase", "Date", "toString", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateDate.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateDateInfo = exports.validateDateSync = exports.validateDate = exports.shouldValidate = void 0;\nconst error_1 = require(\"../../../error\");\nconst isValidatableDateTimeComponent = (obj) => {\n    return !!obj && !!obj.type && obj.type === 'datetime';\n};\nconst isValidatable = (component) => {\n    return isValidatableDateTimeComponent(component);\n};\nconst shouldValidate = (context) => {\n    const { component, value } = context;\n    if (!value || !isValidatable(component)) {\n        return false;\n    }\n    if (component.multiple && Array.isArray(value) && value.length === 0) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateDate = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateDateSync)(context);\n});\nexports.validateDate = validateDate;\nconst validateDateSync = (context) => {\n    const error = new error_1.FieldError('invalidDate', context, 'date');\n    const { value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    // TODO: is this right?\n    if (typeof value === 'string') {\n        if (value.toLowerCase() === 'invalid date') {\n            return error;\n        }\n        if (new Date(value).toString() === 'Invalid Date') {\n            return error;\n        }\n        return null;\n    }\n    else if (value instanceof Date) {\n        return value.toString() !== 'Invalid Date' ? null : error;\n    }\n    return error;\n};\nexports.validateDateSync = validateDateSync;\nexports.validateDateInfo = {\n    name: 'validateDate',\n    process: exports.validateDate,\n    processSync: exports.validateDateSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,gBAAgB,GAAGD,OAAO,CAACE,gBAAgB,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AAC5G,MAAMC,OAAO,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAMC,8BAA8B,GAAIC,GAAG,IAAK;EAC5C,OAAO,CAAC,CAACA,GAAG,IAAI,CAAC,CAACA,GAAG,CAACC,IAAI,IAAID,GAAG,CAACC,IAAI,KAAK,UAAU;AACzD,CAAC;AACD,MAAMC,aAAa,GAAIC,SAAS,IAAK;EACjC,OAAOJ,8BAA8B,CAACI,SAAS,CAAC;AACpD,CAAC;AACD,MAAMP,cAAc,GAAIQ,OAAO,IAAK;EAChC,MAAM;IAAED,SAAS;IAAE1B;EAAM,CAAC,GAAG2B,OAAO;EACpC,IAAI,CAAC3B,KAAK,IAAI,CAACyB,aAAa,CAACC,SAAS,CAAC,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,IAAIA,SAAS,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAAC9B,KAAK,CAAC,IAAIA,KAAK,CAAC+B,MAAM,KAAK,CAAC,EAAE;IAClE,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDhB,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,YAAY,GAAIS,OAAO,IAAKjC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC7E,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,gBAAgB,EAAEU,OAAO,CAAC;AACjD,CAAC,CAAC;AACFZ,OAAO,CAACG,YAAY,GAAGA,YAAY;AACnC,MAAMD,gBAAgB,GAAIU,OAAO,IAAK;EAClC,MAAMK,KAAK,GAAG,IAAIZ,OAAO,CAACa,UAAU,CAAC,aAAa,EAAEN,OAAO,EAAE,MAAM,CAAC;EACpE,MAAM;IAAE3B;EAAM,CAAC,GAAG2B,OAAO;EACzB,IAAI,CAAC,CAAC,CAAC,EAAEZ,OAAO,CAACI,cAAc,EAAEQ,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA;EACA,IAAI,OAAO3B,KAAK,KAAK,QAAQ,EAAE;IAC3B,IAAIA,KAAK,CAACkC,WAAW,CAAC,CAAC,KAAK,cAAc,EAAE;MACxC,OAAOF,KAAK;IAChB;IACA,IAAI,IAAIG,IAAI,CAACnC,KAAK,CAAC,CAACoC,QAAQ,CAAC,CAAC,KAAK,cAAc,EAAE;MAC/C,OAAOJ,KAAK;IAChB;IACA,OAAO,IAAI;EACf,CAAC,MACI,IAAIhC,KAAK,YAAYmC,IAAI,EAAE;IAC5B,OAAOnC,KAAK,CAACoC,QAAQ,CAAC,CAAC,KAAK,cAAc,GAAG,IAAI,GAAGJ,KAAK;EAC7D;EACA,OAAOA,KAAK;AAChB,CAAC;AACDjB,OAAO,CAACE,gBAAgB,GAAGA,gBAAgB;AAC3CF,OAAO,CAACC,gBAAgB,GAAG;EACvBqB,IAAI,EAAE,cAAc;EACpBC,OAAO,EAAEvB,OAAO,CAACG,YAAY;EAC7BqB,WAAW,EAAExB,OAAO,CAACE,gBAAgB;EACrCuB,aAAa,EAAEzB,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}