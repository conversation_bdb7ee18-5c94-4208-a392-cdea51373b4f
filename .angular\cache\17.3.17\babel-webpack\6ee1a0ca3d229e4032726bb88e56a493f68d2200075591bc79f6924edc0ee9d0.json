{"ast": null, "code": "export * from './module/core.mjs';\nexport * from './module/duplex.mjs';\nexport { PatchError as JsonPatchError, _deepClone as deepClone, escapePathComponent, unescapePathComponent } from './module/helpers.mjs';\n\n/**\n * Default export for backwards compat\n */\n\nimport * as core from './module/core.mjs';\nimport * as duplex from './module/duplex.mjs';\nimport { PatchError as JsonPatchError, _deepClone as deepClone, escapePathComponent, unescapePathComponent } from './module/helpers.mjs';\nexport default Object.assign({}, core, duplex, {\n  JsonPatchError,\n  deepClone,\n  escapePathComponent,\n  unescapePathComponent\n});", "map": {"version": 3, "names": ["Patch<PERSON><PERSON>r", "JsonPatchError", "_deepClone", "deepClone", "escapePathComponent", "unescapePathComponent", "core", "duplex", "Object", "assign"], "sources": ["D:/workspace/formtest_aug/node_modules/fast-json-patch/index.mjs"], "sourcesContent": ["export * from './module/core.mjs';\nexport * from './module/duplex.mjs';\nexport {\n    PatchError as JsonPatchError,\n    _deepClone as deepClone,\n    escapePathComponent,\n    unescapePathComponent\n} from './module/helpers.mjs';\n\n\n/**\n * Default export for backwards compat\n */\n\nimport * as core from './module/core.mjs';\nimport * as duplex from './module/duplex.mjs';\nimport {\n    PatchError as JsonPatchError,\n    _deepClone as deepClone,\n    escapePathComponent,\n    unescapePathComponent\n} from './module/helpers.mjs';\n\nexport default Object.assign({}, core, duplex, {\n    JsonPatchError,\n    deepClone,\n    escapePathComponent,\n    unescapePathComponent\n});"], "mappings": "AAAA,cAAc,mBAAmB;AACjC,cAAc,qBAAqB;AACnC,SACIA,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,SAAS,EACvBC,mBAAmB,EACnBC,qBAAqB,QAClB,sBAAsB;;AAG7B;AACA;AACA;;AAEA,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,OAAO,KAAKC,MAAM,MAAM,qBAAqB;AAC7C,SACIP,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,SAAS,EACvBC,mBAAmB,EACnBC,qBAAqB,QAClB,sBAAsB;AAE7B,eAAeG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,IAAI,EAAEC,MAAM,EAAE;EAC3CN,cAAc;EACdE,SAAS;EACTC,mBAAmB;EACnBC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}