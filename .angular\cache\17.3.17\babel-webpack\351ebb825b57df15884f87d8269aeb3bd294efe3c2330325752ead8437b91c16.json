{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateTimeInfo = exports.validateTime = exports.validateTimeSync = exports.shouldValidate = void 0;\nconst formUtil_1 = require(\"../../../utils/formUtil\");\nconst error_1 = require(\"../../../error\");\nconst date_1 = require(\"../../../utils/date\");\nconst customParseFormat_1 = __importDefault(require(\"dayjs/plugin/customParseFormat\"));\ndate_1.dayjs.extend(customParseFormat_1.default);\nconst isValidatableTimeComponent = comp => {\n  return comp && comp.type === 'time';\n};\nconst shouldValidate = context => {\n  const {\n    component\n  } = context;\n  if (!isValidatableTimeComponent(component)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateTimeSync = context => {\n  const {\n    component,\n    data,\n    path,\n    value\n  } = context;\n  if (!(0, exports.shouldValidate)(context)) {\n    return null;\n  }\n  try {\n    if (!value || (0, formUtil_1.isComponentDataEmpty)(component, data, path)) return null;\n    const format = component.dataFormat || 'HH:mm:ss';\n    const isValid = (0, date_1.dayjs)(String(value), format, true).isValid();\n    return isValid ? null : new error_1.FieldError('time', context);\n  } catch (ignoreErr) {\n    throw new error_1.ProcessorError(`Could not validate time component ${component.key} with value ${value}`, context, 'validate:validateTime');\n  }\n};\nexports.validateTimeSync = validateTimeSync;\nconst validateTime = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateTimeSync)(context);\n});\nexports.validateTime = validateTime;\nexports.validateTimeInfo = {\n  name: 'validateTime',\n  process: exports.validateTime,\n  processSync: exports.validateTimeSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "validateTimeInfo", "validateTime", "validateTimeSync", "shouldValidate", "formUtil_1", "require", "error_1", "date_1", "customParseFormat_1", "dayjs", "extend", "default", "isValidatableTimeComponent", "comp", "type", "context", "component", "data", "path", "isComponentDataEmpty", "format", "dataFormat", "<PERSON><PERSON><PERSON><PERSON>", "String", "FieldError", "ignoreErr", "ProcessorError", "key", "name", "process", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateTime.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateTimeInfo = exports.validateTime = exports.validateTimeSync = exports.shouldValidate = void 0;\nconst formUtil_1 = require(\"../../../utils/formUtil\");\nconst error_1 = require(\"../../../error\");\nconst date_1 = require(\"../../../utils/date\");\nconst customParseFormat_1 = __importDefault(require(\"dayjs/plugin/customParseFormat\"));\ndate_1.dayjs.extend(customParseFormat_1.default);\nconst isValidatableTimeComponent = (comp) => {\n    return comp && comp.type === 'time';\n};\nconst shouldValidate = (context) => {\n    const { component } = context;\n    if (!isValidatableTimeComponent(component)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateTimeSync = (context) => {\n    const { component, data, path, value } = context;\n    if (!(0, exports.shouldValidate)(context)) {\n        return null;\n    }\n    try {\n        if (!value || (0, formUtil_1.isComponentDataEmpty)(component, data, path))\n            return null;\n        const format = component.dataFormat || 'HH:mm:ss';\n        const isValid = (0, date_1.dayjs)(String(value), format, true).isValid();\n        return isValid ? null : new error_1.FieldError('time', context);\n    }\n    catch (ignoreErr) {\n        throw new error_1.ProcessorError(`Could not validate time component ${component.key} with value ${value}`, context, 'validate:validateTime');\n    }\n};\nexports.validateTimeSync = validateTimeSync;\nconst validateTime = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateTimeSync)(context);\n});\nexports.validateTime = validateTime;\nexports.validateTimeInfo = {\n    name: 'validateTime',\n    process: exports.validateTime,\n    processSync: exports.validateTimeSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,IAAIO,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAElB,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DkB,OAAO,CAACC,gBAAgB,GAAGD,OAAO,CAACE,YAAY,GAAGF,OAAO,CAACG,gBAAgB,GAAGH,OAAO,CAACI,cAAc,GAAG,KAAK,CAAC;AAC5G,MAAMC,UAAU,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AACrD,MAAMC,OAAO,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAME,MAAM,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAC7C,MAAMG,mBAAmB,GAAGd,eAAe,CAACW,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACtFE,MAAM,CAACE,KAAK,CAACC,MAAM,CAACF,mBAAmB,CAACG,OAAO,CAAC;AAChD,MAAMC,0BAA0B,GAAIC,IAAI,IAAK;EACzC,OAAOA,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,MAAM;AACvC,CAAC;AACD,MAAMX,cAAc,GAAIY,OAAO,IAAK;EAChC,MAAM;IAAEC;EAAU,CAAC,GAAGD,OAAO;EAC7B,IAAI,CAACH,0BAA0B,CAACI,SAAS,CAAC,EAAE;IACxC,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDjB,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,gBAAgB,GAAIa,OAAO,IAAK;EAClC,MAAM;IAAEC,SAAS;IAAEC,IAAI;IAAEC,IAAI;IAAErC;EAAM,CAAC,GAAGkC,OAAO;EAChD,IAAI,CAAC,CAAC,CAAC,EAAEhB,OAAO,CAACI,cAAc,EAAEY,OAAO,CAAC,EAAE;IACvC,OAAO,IAAI;EACf;EACA,IAAI;IACA,IAAI,CAAClC,KAAK,IAAI,CAAC,CAAC,EAAEuB,UAAU,CAACe,oBAAoB,EAAEH,SAAS,EAAEC,IAAI,EAAEC,IAAI,CAAC,EACrE,OAAO,IAAI;IACf,MAAME,MAAM,GAAGJ,SAAS,CAACK,UAAU,IAAI,UAAU;IACjD,MAAMC,OAAO,GAAG,CAAC,CAAC,EAAEf,MAAM,CAACE,KAAK,EAAEc,MAAM,CAAC1C,KAAK,CAAC,EAAEuC,MAAM,EAAE,IAAI,CAAC,CAACE,OAAO,CAAC,CAAC;IACxE,OAAOA,OAAO,GAAG,IAAI,GAAG,IAAIhB,OAAO,CAACkB,UAAU,CAAC,MAAM,EAAET,OAAO,CAAC;EACnE,CAAC,CACD,OAAOU,SAAS,EAAE;IACd,MAAM,IAAInB,OAAO,CAACoB,cAAc,CAAC,qCAAqCV,SAAS,CAACW,GAAG,eAAe9C,KAAK,EAAE,EAAEkC,OAAO,EAAE,uBAAuB,CAAC;EAChJ;AACJ,CAAC;AACDhB,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMD,YAAY,GAAIc,OAAO,IAAKxC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC7E,OAAO,CAAC,CAAC,EAAEwB,OAAO,CAACG,gBAAgB,EAAEa,OAAO,CAAC;AACjD,CAAC,CAAC;AACFhB,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnCF,OAAO,CAACC,gBAAgB,GAAG;EACvB4B,IAAI,EAAE,cAAc;EACpBC,OAAO,EAAE9B,OAAO,CAACE,YAAY;EAC7B6B,WAAW,EAAE/B,OAAO,CAACG,gBAAgB;EACrC6B,aAAa,EAAEhC,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}