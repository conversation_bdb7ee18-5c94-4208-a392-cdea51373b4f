{"ast": null, "code": "\"use strict\";\n\nvar __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.validateMultipleInfo = exports.validateMultipleSync = exports.validateMultiple = exports.shouldValidate = exports.emptyValueIsArray = exports.isEligible = void 0;\nconst lodash_1 = require(\"lodash\");\nconst error_1 = require(\"../../../error\");\nconst formUtil_1 = require(\"../../../utils/formUtil\");\nconst isEligible = component => {\n  // TODO: would be nice if this was type safe\n  switch (component.type) {\n    case 'hidden':\n    case 'address':\n      if (!component.multiple) {\n        return false;\n      }\n      return true;\n    case 'textarea':\n      if (!component.as || component.as !== 'json' || component.as === 'json' && !component.multiple) {\n        return false;\n      }\n      return true;\n    // TODO: For backwards compatibility, skip multiple validation for select components until we can investigate\n    // how this validation might break existing forms\n    case 'select':\n      return false;\n    default:\n      return true;\n  }\n};\nexports.isEligible = isEligible;\nconst isTagsComponent = component => {\n  return (component === null || component === void 0 ? void 0 : component.type) === 'tags';\n};\nconst emptyValueIsArray = component => {\n  // TODO: How do we infer the data model of the compoennt given only its JSON? For now, we have to hardcode component types\n  switch (component.type) {\n    case 'datagrid':\n    case 'editgrid':\n    case 'tagpad':\n    case 'sketchpad':\n    case 'datatable':\n    case 'dynamicWizard':\n    case 'file':\n      return true;\n    case 'select':\n    case 'textfield':\n      return !!component.multiple;\n    case 'tags':\n      return component.storeas !== 'string';\n    default:\n      return true;\n  }\n};\nexports.emptyValueIsArray = emptyValueIsArray;\nconst shouldValidate = context => {\n  const {\n    component\n  } = context;\n  if (!(0, exports.isEligible)(component)) {\n    return false;\n  }\n  return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMultiple = context => __awaiter(void 0, void 0, void 0, function* () {\n  return (0, exports.validateMultipleSync)(context);\n});\nexports.validateMultiple = validateMultiple;\nconst validateMultipleSync = context => {\n  var _a;\n  const {\n    component,\n    value\n  } = context;\n  // Skip multiple validation if the component tells us to\n  if (!(0, exports.isEligible)(component)) {\n    return null;\n  }\n  const shouldBeMultipleArray = !!component.multiple;\n  const isRequired = !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.required);\n  const compModelType = (0, formUtil_1.getModelType)(component);\n  const underlyingValueShouldBeArray = ['nestedArray', 'nestedDataArray'].indexOf(compModelType) !== -1 || isTagsComponent(component) && component.storeas === 'array';\n  const valueIsArray = Array.isArray(value);\n  if (shouldBeMultipleArray) {\n    if (valueIsArray && underlyingValueShouldBeArray) {\n      if (value.length === 0) {\n        return isRequired ? new error_1.FieldError('array_nonempty', Object.assign(Object.assign({}, context), {\n          setting: true\n        })) : null;\n      }\n      // TODO: We need to be permissive here for file components, which have an array model type but don't have an underlying array value\n      // (in other words, a file component's data object will always be a single array regardless of whether or not multiple is set)\n      // In the future, we could consider checking the underlying value's type to determine if it should be an array\n      // return Array.isArray(value[0]) ? null : new FieldError('array', { ...context, setting: true });\n      return null;\n    } else if (valueIsArray && !underlyingValueShouldBeArray) {\n      if (value.length === 0) {\n        return isRequired ? new error_1.FieldError('array_nonempty', Object.assign(Object.assign({}, context), {\n          setting: true\n        })) : null;\n      }\n      return Array.isArray(value[0]) && compModelType !== 'any' ? new error_1.FieldError('nonarray', Object.assign(Object.assign({}, context), {\n        setting: true\n      })) : null;\n    } else {\n      const error = new error_1.FieldError('array', Object.assign(Object.assign({}, context), {\n        setting: true\n      }));\n      // Null/undefined is ok if this value isn't required; anything else should fail\n      return (0, lodash_1.isNil)(value) ? isRequired ? error : null : error;\n    }\n  } else {\n    const canBeArray = (0, exports.emptyValueIsArray)(component) || underlyingValueShouldBeArray;\n    if (!canBeArray && valueIsArray) {\n      return new error_1.FieldError('nonarray', Object.assign(Object.assign({}, context), {\n        setting: false\n      }));\n    }\n    return null;\n  }\n};\nexports.validateMultipleSync = validateMultipleSync;\nexports.validateMultipleInfo = {\n  name: 'validateMultiple',\n  process: exports.validateMultiple,\n  fullValue: true,\n  processSync: exports.validateMultipleSync,\n  shouldProcess: exports.shouldValidate\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Object", "defineProperty", "exports", "validateMultipleInfo", "validateMultipleSync", "validate<PERSON><PERSON><PERSON><PERSON>", "shouldValidate", "emptyValueIsArray", "isEligible", "lodash_1", "require", "error_1", "formUtil_1", "component", "type", "multiple", "as", "isTagsComponent", "storeas", "context", "_a", "shouldBeMultipleArray", "isRequired", "validate", "required", "compModelType", "getModelType", "underlyingValueShouldBeArray", "indexOf", "valueIsArray", "Array", "isArray", "length", "FieldError", "assign", "setting", "error", "isNil", "canBeArray", "name", "process", "fullValue", "processSync", "shouldProcess"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/process/validation/rules/validateMultiple.js"], "sourcesContent": ["\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateMultipleInfo = exports.validateMultipleSync = exports.validateMultiple = exports.shouldValidate = exports.emptyValueIsArray = exports.isEligible = void 0;\nconst lodash_1 = require(\"lodash\");\nconst error_1 = require(\"../../../error\");\nconst formUtil_1 = require(\"../../../utils/formUtil\");\nconst isEligible = (component) => {\n    // TODO: would be nice if this was type safe\n    switch (component.type) {\n        case 'hidden':\n        case 'address':\n            if (!component.multiple) {\n                return false;\n            }\n            return true;\n        case 'textarea':\n            if (!component.as ||\n                component.as !== 'json' ||\n                (component.as === 'json' && !component.multiple)) {\n                return false;\n            }\n            return true;\n        // TODO: For backwards compatibility, skip multiple validation for select components until we can investigate\n        // how this validation might break existing forms\n        case 'select':\n            return false;\n        default:\n            return true;\n    }\n};\nexports.isEligible = isEligible;\nconst isTagsComponent = (component) => {\n    return (component === null || component === void 0 ? void 0 : component.type) === 'tags';\n};\nconst emptyValueIsArray = (component) => {\n    // TODO: How do we infer the data model of the compoennt given only its JSON? For now, we have to hardcode component types\n    switch (component.type) {\n        case 'datagrid':\n        case 'editgrid':\n        case 'tagpad':\n        case 'sketchpad':\n        case 'datatable':\n        case 'dynamicWizard':\n        case 'file':\n            return true;\n        case 'select':\n        case 'textfield':\n            return !!component.multiple;\n        case 'tags':\n            return component.storeas !== 'string';\n        default:\n            return true;\n    }\n};\nexports.emptyValueIsArray = emptyValueIsArray;\nconst shouldValidate = (context) => {\n    const { component } = context;\n    if (!(0, exports.isEligible)(component)) {\n        return false;\n    }\n    return true;\n};\nexports.shouldValidate = shouldValidate;\nconst validateMultiple = (context) => __awaiter(void 0, void 0, void 0, function* () {\n    return (0, exports.validateMultipleSync)(context);\n});\nexports.validateMultiple = validateMultiple;\nconst validateMultipleSync = (context) => {\n    var _a;\n    const { component, value } = context;\n    // Skip multiple validation if the component tells us to\n    if (!(0, exports.isEligible)(component)) {\n        return null;\n    }\n    const shouldBeMultipleArray = !!component.multiple;\n    const isRequired = !!((_a = component.validate) === null || _a === void 0 ? void 0 : _a.required);\n    const compModelType = (0, formUtil_1.getModelType)(component);\n    const underlyingValueShouldBeArray = ['nestedArray', 'nestedDataArray'].indexOf(compModelType) !== -1 ||\n        (isTagsComponent(component) && component.storeas === 'array');\n    const valueIsArray = Array.isArray(value);\n    if (shouldBeMultipleArray) {\n        if (valueIsArray && underlyingValueShouldBeArray) {\n            if (value.length === 0) {\n                return isRequired ? new error_1.FieldError('array_nonempty', Object.assign(Object.assign({}, context), { setting: true })) : null;\n            }\n            // TODO: We need to be permissive here for file components, which have an array model type but don't have an underlying array value\n            // (in other words, a file component's data object will always be a single array regardless of whether or not multiple is set)\n            // In the future, we could consider checking the underlying value's type to determine if it should be an array\n            // return Array.isArray(value[0]) ? null : new FieldError('array', { ...context, setting: true });\n            return null;\n        }\n        else if (valueIsArray && !underlyingValueShouldBeArray) {\n            if (value.length === 0) {\n                return isRequired ? new error_1.FieldError('array_nonempty', Object.assign(Object.assign({}, context), { setting: true })) : null;\n            }\n            return Array.isArray(value[0]) && compModelType !== 'any'\n                ? new error_1.FieldError('nonarray', Object.assign(Object.assign({}, context), { setting: true }))\n                : null;\n        }\n        else {\n            const error = new error_1.FieldError('array', Object.assign(Object.assign({}, context), { setting: true }));\n            // Null/undefined is ok if this value isn't required; anything else should fail\n            return (0, lodash_1.isNil)(value) ? (isRequired ? error : null) : error;\n        }\n    }\n    else {\n        const canBeArray = (0, exports.emptyValueIsArray)(component) || underlyingValueShouldBeArray;\n        if (!canBeArray && valueIsArray) {\n            return new error_1.FieldError('nonarray', Object.assign(Object.assign({}, context), { setting: false }));\n        }\n        return null;\n    }\n};\nexports.validateMultipleSync = validateMultipleSync;\nexports.validateMultipleInfo = {\n    name: 'validateMultiple',\n    process: exports.validateMultiple,\n    fullValue: true,\n    processSync: exports.validateMultipleSync,\n    shouldProcess: exports.shouldValidate,\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACDO,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7De,OAAO,CAACC,oBAAoB,GAAGD,OAAO,CAACE,oBAAoB,GAAGF,OAAO,CAACG,gBAAgB,GAAGH,OAAO,CAACI,cAAc,GAAGJ,OAAO,CAACK,iBAAiB,GAAGL,OAAO,CAACM,UAAU,GAAG,KAAK,CAAC;AACzK,MAAMC,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAMC,OAAO,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACzC,MAAME,UAAU,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACrD,MAAMF,UAAU,GAAIK,SAAS,IAAK;EAC9B;EACA,QAAQA,SAAS,CAACC,IAAI;IAClB,KAAK,QAAQ;IACb,KAAK,SAAS;MACV,IAAI,CAACD,SAAS,CAACE,QAAQ,EAAE;QACrB,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,KAAK,UAAU;MACX,IAAI,CAACF,SAAS,CAACG,EAAE,IACbH,SAAS,CAACG,EAAE,KAAK,MAAM,IACtBH,SAAS,CAACG,EAAE,KAAK,MAAM,IAAI,CAACH,SAAS,CAACE,QAAS,EAAE;QAClD,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf;IACA;IACA,KAAK,QAAQ;MACT,OAAO,KAAK;IAChB;MACI,OAAO,IAAI;EACnB;AACJ,CAAC;AACDb,OAAO,CAACM,UAAU,GAAGA,UAAU;AAC/B,MAAMS,eAAe,GAAIJ,SAAS,IAAK;EACnC,OAAO,CAACA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,IAAI,MAAM,MAAM;AAC5F,CAAC;AACD,MAAMP,iBAAiB,GAAIM,SAAS,IAAK;EACrC;EACA,QAAQA,SAAS,CAACC,IAAI;IAClB,KAAK,UAAU;IACf,KAAK,UAAU;IACf,KAAK,QAAQ;IACb,KAAK,WAAW;IAChB,KAAK,WAAW;IAChB,KAAK,eAAe;IACpB,KAAK,MAAM;MACP,OAAO,IAAI;IACf,KAAK,QAAQ;IACb,KAAK,WAAW;MACZ,OAAO,CAAC,CAACD,SAAS,CAACE,QAAQ;IAC/B,KAAK,MAAM;MACP,OAAOF,SAAS,CAACK,OAAO,KAAK,QAAQ;IACzC;MACI,OAAO,IAAI;EACnB;AACJ,CAAC;AACDhB,OAAO,CAACK,iBAAiB,GAAGA,iBAAiB;AAC7C,MAAMD,cAAc,GAAIa,OAAO,IAAK;EAChC,MAAM;IAAEN;EAAU,CAAC,GAAGM,OAAO;EAC7B,IAAI,CAAC,CAAC,CAAC,EAAEjB,OAAO,CAACM,UAAU,EAAEK,SAAS,CAAC,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf,CAAC;AACDX,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,MAAMD,gBAAgB,GAAIc,OAAO,IAAKtC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EACjF,OAAO,CAAC,CAAC,EAAEqB,OAAO,CAACE,oBAAoB,EAAEe,OAAO,CAAC;AACrD,CAAC,CAAC;AACFjB,OAAO,CAACG,gBAAgB,GAAGA,gBAAgB;AAC3C,MAAMD,oBAAoB,GAAIe,OAAO,IAAK;EACtC,IAAIC,EAAE;EACN,MAAM;IAAEP,SAAS;IAAE1B;EAAM,CAAC,GAAGgC,OAAO;EACpC;EACA,IAAI,CAAC,CAAC,CAAC,EAAEjB,OAAO,CAACM,UAAU,EAAEK,SAAS,CAAC,EAAE;IACrC,OAAO,IAAI;EACf;EACA,MAAMQ,qBAAqB,GAAG,CAAC,CAACR,SAAS,CAACE,QAAQ;EAClD,MAAMO,UAAU,GAAG,CAAC,EAAE,CAACF,EAAE,GAAGP,SAAS,CAACU,QAAQ,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,QAAQ,CAAC;EACjG,MAAMC,aAAa,GAAG,CAAC,CAAC,EAAEb,UAAU,CAACc,YAAY,EAAEb,SAAS,CAAC;EAC7D,MAAMc,4BAA4B,GAAG,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAACC,OAAO,CAACH,aAAa,CAAC,KAAK,CAAC,CAAC,IAChGR,eAAe,CAACJ,SAAS,CAAC,IAAIA,SAAS,CAACK,OAAO,KAAK,OAAQ;EACjE,MAAMW,YAAY,GAAGC,KAAK,CAACC,OAAO,CAAC5C,KAAK,CAAC;EACzC,IAAIkC,qBAAqB,EAAE;IACvB,IAAIQ,YAAY,IAAIF,4BAA4B,EAAE;MAC9C,IAAIxC,KAAK,CAAC6C,MAAM,KAAK,CAAC,EAAE;QACpB,OAAOV,UAAU,GAAG,IAAIX,OAAO,CAACsB,UAAU,CAAC,gBAAgB,EAAEjC,MAAM,CAACkC,MAAM,CAAClC,MAAM,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAEf,OAAO,CAAC,EAAE;UAAEgB,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC,GAAG,IAAI;MACrI;MACA;MACA;MACA;MACA;MACA,OAAO,IAAI;IACf,CAAC,MACI,IAAIN,YAAY,IAAI,CAACF,4BAA4B,EAAE;MACpD,IAAIxC,KAAK,CAAC6C,MAAM,KAAK,CAAC,EAAE;QACpB,OAAOV,UAAU,GAAG,IAAIX,OAAO,CAACsB,UAAU,CAAC,gBAAgB,EAAEjC,MAAM,CAACkC,MAAM,CAAClC,MAAM,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAEf,OAAO,CAAC,EAAE;UAAEgB,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC,GAAG,IAAI;MACrI;MACA,OAAOL,KAAK,CAACC,OAAO,CAAC5C,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIsC,aAAa,KAAK,KAAK,GACnD,IAAId,OAAO,CAACsB,UAAU,CAAC,UAAU,EAAEjC,MAAM,CAACkC,MAAM,CAAClC,MAAM,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAEf,OAAO,CAAC,EAAE;QAAEgB,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC,GAChG,IAAI;IACd,CAAC,MACI;MACD,MAAMC,KAAK,GAAG,IAAIzB,OAAO,CAACsB,UAAU,CAAC,OAAO,EAAEjC,MAAM,CAACkC,MAAM,CAAClC,MAAM,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAEf,OAAO,CAAC,EAAE;QAAEgB,OAAO,EAAE;MAAK,CAAC,CAAC,CAAC;MAC3G;MACA,OAAO,CAAC,CAAC,EAAE1B,QAAQ,CAAC4B,KAAK,EAAElD,KAAK,CAAC,GAAImC,UAAU,GAAGc,KAAK,GAAG,IAAI,GAAIA,KAAK;IAC3E;EACJ,CAAC,MACI;IACD,MAAME,UAAU,GAAG,CAAC,CAAC,EAAEpC,OAAO,CAACK,iBAAiB,EAAEM,SAAS,CAAC,IAAIc,4BAA4B;IAC5F,IAAI,CAACW,UAAU,IAAIT,YAAY,EAAE;MAC7B,OAAO,IAAIlB,OAAO,CAACsB,UAAU,CAAC,UAAU,EAAEjC,MAAM,CAACkC,MAAM,CAAClC,MAAM,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAEf,OAAO,CAAC,EAAE;QAAEgB,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAC5G;IACA,OAAO,IAAI;EACf;AACJ,CAAC;AACDjC,OAAO,CAACE,oBAAoB,GAAGA,oBAAoB;AACnDF,OAAO,CAACC,oBAAoB,GAAG;EAC3BoC,IAAI,EAAE,kBAAkB;EACxBC,OAAO,EAAEtC,OAAO,CAACG,gBAAgB;EACjCoC,SAAS,EAAE,IAAI;EACfC,WAAW,EAAExC,OAAO,CAACE,oBAAoB;EACzCuC,aAAa,EAAEzC,OAAO,CAACI;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}