{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst ConditionOperator_1 = __importDefault(require(\"./ConditionOperator\"));\nconst lodash_1 = require(\"lodash\");\nclass StartsWith extends ConditionOperator_1.default {\n  static get operatorKey() {\n    return 'startsWith';\n  }\n  static get displayedName() {\n    return 'Starts With';\n  }\n  execute({\n    value,\n    comparedValue\n  }) {\n    return (0, lodash_1.startsWith)(value, comparedValue);\n  }\n}\nexports.default = StartsWith;", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "ConditionOperator_1", "require", "lodash_1", "StartsWith", "default", "operatorKey", "displayedName", "execute", "comparedValue", "startsWith"], "sources": ["D:/workspace/formtest_aug/node_modules/@formio/core/lib/utils/operators/StartsWith.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst ConditionOperator_1 = __importDefault(require(\"./ConditionOperator\"));\nconst lodash_1 = require(\"lodash\");\nclass StartsWith extends ConditionOperator_1.default {\n    static get operatorKey() {\n        return 'startsWith';\n    }\n    static get displayedName() {\n        return 'Starts With';\n    }\n    execute({ value, comparedValue }) {\n        return (0, lodash_1.startsWith)(value, comparedValue);\n    }\n}\nexports.default = StartsWith;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,MAAMC,mBAAmB,GAAGP,eAAe,CAACQ,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC3E,MAAMC,QAAQ,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAClC,MAAME,UAAU,SAASH,mBAAmB,CAACI,OAAO,CAAC;EACjD,WAAWC,WAAWA,CAAA,EAAG;IACrB,OAAO,YAAY;EACvB;EACA,WAAWC,aAAaA,CAAA,EAAG;IACvB,OAAO,aAAa;EACxB;EACAC,OAAOA,CAAC;IAAER,KAAK;IAAES;EAAc,CAAC,EAAE;IAC9B,OAAO,CAAC,CAAC,EAAEN,QAAQ,CAACO,UAAU,EAAEV,KAAK,EAAES,aAAa,CAAC;EACzD;AACJ;AACAV,OAAO,CAACM,OAAO,GAAGD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}